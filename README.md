# Dự án JESTER BOT APP

## Hướng dẫn build project

### Android

#### APK

- **Debug**:  

  ```bash
  flutter build apk --debug --flavor DEV -t lib/main.dart --split-per-abi
  ```

- **Release**:  

  ```bash
  flutter build apk --release --flavor DEV -t lib/main.dart
  ```

#### App Bundle

- **Debug**:  

  ```bash
  flutter build appbundle --debug --flavor DEV -t lib/main.dart --split-per-abi
  ```

- **Release**:  

  ```bash
  flutter build appbundle --release --flavor DEV -t lib/main.dart
  ```

### iOS

- **Debug**:  

  ```bash
  flutter build ios --debug --flavor DEV -t lib/main.dart
  ```

- **Release**:  

  ```bash
  flutter build ios --release --flavor DEV -t lib/main.dart
  ```

- **IPA**:  

  ```bash
  flutter build ipa --export-options-plist=./ios/Runner/exportOption.plist --flavor DEV --release
  ```

---

## Hướng dẫn chạy project

1. **Bước 1**: Ch<PERSON><PERSON> lệnh:  

   ```bash
   flutter pub get
   ```  

   để cài thư viện.

2. **Bước 2**: Đảm bảo cấu hình môi trường phát triển (nếu cần).

3. **Bước 3**: Chạy lệnh:  

   ```bash
   flutter gen-l10n
   ```  

   để tạo file ngôn ngữ.

4. **Bước 4**: Chạy lệnh:  

   ```bash
   flutter run lib/main.dart --flavor DEV
   ```  

   để chạy project.

## Hướng dẫn cấu hình project

- Flutter SDK: 3.24.4
- Android Studio: 2024.2.2
- Java: 17

## Danh sách các màn hình theo tên file

- `activate_account_page.dart`: Màn kích hoạt tài khoản
- `forgot_password_page.dart`: Màn quên mật khẩu
- `register_page.dart`: Màn đăng ký
- `reset_password_page.dart`: Màn reset mật khẩu
- `sign_in_page.dart`: Màn đăng nhập

- `main_page.dart`: Trang chính (Chứa 4 tab: Trang chủ, chat 1-1, Chat group, Tài khoản)
- `home_page.dart`: Tab Trang chủ
- `onboarding_page.dart`: Trang hướng dẫn

- `chat_page.dart`: Tab danh sách chat 1-1
- `chatbot_page.dart`: Màn chat
- `create_group_page.dart`: Màn tạo nhóm chat
- `group_member_page.dart`: Màn danh sách thành viên nhóm
- `group_settings_page.dart`: Màn cài đặt nhóm
- `join_group_page.dart`: Màn tham gia nhóm
- `member_request_page.dart`: Màn danh sách yêu cầu tham gia nhóm
- `photo_viewer_page.dart`: Màn xem ảnh tin nhắn
- `room_page.dart`: Tab danh sách nhóm chat
- `update_group_page.dart`: Màn cập nhật thông tin nhóm

- `upgrade_subscription_page.dart`: Màn nâng cấp gói dịch vụ

- `account_info_page.dart`: Màn thông tin tài khoản
- `profile_page.dart`: Tab Tài khoản

## Danh sách các service

- `deeplink_service.dart`: Cấu hình deep link, universal link
- `local_notification_service.dart`: Cấu hình local notification
- `channel_service.dart`: Cấu hình method channel
- `push_ios_notification_service.dart`: Cấu hình push notification cho iOS
- `ads_service.dart`: Cấu hình quảng cáo
- `analytics_service.dart`: Cấu hình analytics
- `fcm_service.dart`: Cấu hình FCM cho Android
- `web_socket_helper.dart`: Cấu hình WebSocket
- `secure_storage_helper.dart`: Cấu hình lưu trữ an toàn cho UUID

## Các Bloc quan trọng

- `app_notification_cubit.dart`: Quản lý trạng thái quyền thông báo
- `config_bloc.dart`: Quản lý cấu hình