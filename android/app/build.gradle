plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "vn.saymee.android"
    compileSdk = 34
    ndkVersion = "26.1.10909125"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

//    compileOptions {
//        coreLibraryDesugaringEnabled true
//        sourceCompatibility = JavaVersion.VERSION_17
//        targetCompatibility = JavaVersion.VERSION_17
//    }
//
//    kotlinOptions {
//        jvmTarget = '17'
//    }
//
//    kotlin {
//        jvmToolchain(17)
//    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "vn.saymee.android"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21
        targetSdk = 34
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        release {
            keyAlias = keystoreProperties['keyAlias']
            keyPassword = keystoreProperties['keyPassword']
            storeFile = keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword = keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.release
        }
    }

    flavorDimensions 'default'

    productFlavors {
        DEV {
            dimension 'default'
            resValue 'string', 'app_name', 'Saymee'
        }
        PROD {
            dimension 'default'
            resValue 'string', 'app_name', 'Saymee'
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation 'com.google.errorprone:error_prone_annotations:2.36.0'
    implementation 'com.github.spotbugs:spotbugs-annotations:4.8.6'
    implementation(platform("com.google.firebase:firebase-bom:33.7.0"))
    implementation 'com.ironsource:adqualitysdk:7.22.3'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'
    implementation 'com.google.android.gms:play-services-basement:18.0.0'
    implementation 'com.google.android.gms:play-services-appset:16.0.2'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
    implementation 'androidx.window:window:1.0.0'
    implementation 'androidx.window:window-java:1.0.0'
}
