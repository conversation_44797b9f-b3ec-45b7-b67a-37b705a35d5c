import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:saymee/core/constants/app_keys.dart';
import 'package:saymee/core/constants/app_language.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/core/constants/app_theme.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/helpers/web_socket_helper.dart';
import 'package:saymee/core/utils/analytic_service.dart';
import 'package:saymee/core/utils/deep_link_service.dart';
import 'package:saymee/core/utils/fcm_service.dart';
import 'package:saymee/core/utils/local_notification_service.dart';
import 'package:saymee/l10n/app_localizations.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/app/general/app_module_routes.dart';
import 'package:saymee/modules/app/presentation/blocs/app_banner/app_banner_cubit.dart';
import 'package:saymee/modules/app/presentation/blocs/app_notification/app_notification_cubit.dart';
import 'package:saymee/modules/app/presentation/blocs/chatbot_list/chatbot_list_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/product_list/product_list_bloc.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/auth/general/auth_module_routes.dart';
import 'package:saymee/modules/auth/presentation/blocs/auth/auth_bloc.dart';
import 'package:saymee/modules/auth/presentation/blocs/email_cubit.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_bloc.dart';
import 'package:saymee/modules/subscription/presentation/blocs/plan_list/plan_list_bloc.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'core/utils/push_ios_notification_service.dart';

class MainWidget extends StatefulWidget {
  const MainWidget({super.key});

  @override
  State<MainWidget> createState() => _MainWidgetState();
}

class _MainWidgetState extends State<MainWidget> with WidgetsBindingObserver {
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();

    final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();

    Modular.setNavigatorKey(AppKeys.navigatorKey);

    Modular.setInitialRoute(
        '${AppRoutes.moduleApp}${AppModuleRoutes.onboarding}');

    final isFirstOpenApp = sharedPreferenceHelper.checkFirstOpenApp();
    if (isFirstOpenApp) {
      Modular.setInitialRoute(
          '${AppRoutes.moduleApp}${AppModuleRoutes.onboarding}');
      sharedPreferenceHelper.setFirstOpenApp(firstOpenApp: false);
    } else {
      Modular.setInitialRoute(
          '${AppRoutes.moduleAuth}${AuthModuleRoutes.signIn}');
    }

    if (Platform.isIOS) {
      PushIOSNotificationService.retrieveDeviceToken();
      PushIOSNotificationService.listen();
      PushIOSNotificationService.retrieveTerminatedNotification();
    } else {
      FcmService.initialize();
    }

    LocalNotificationService.initLocalNotificationSetting();

    DeepLinkService.instance.initService();

    Modular.get<AppNotificationCubit>().checkPermission();
    AppModuleHelper.getCommonData();

    if (AuthModuleHelper.checkAccessTokenValid()) {
      UserModuleHelper.getUserData();
      WebSocketHelper().connect();
      if (Platform.isIOS) {
        PushIOSNotificationService.requestNotificationPermission();
      }
    }

    // if (Platform.isIOS) {
    //   AnalyticService.instance.initService();
    // }

    AnalyticService.instance.initService();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AppLanguageBloc>(
          create: (context) =>
              Modular.get<AppLanguageBloc>()..add(InitLanguageEvent()),
        ),
        BlocProvider<AuthBloc>(create: (context) => Modular.get<AuthBloc>()),
        BlocProvider<UserInfoBloc>(
            create: (context) => Modular.get<UserInfoBloc>()),
        BlocProvider<AppNotificationCubit>(
            create: (context) => Modular.get<AppNotificationCubit>()),
        BlocProvider<ConfigBloc>(
            create: (context) => Modular.get<ConfigBloc>()),
        BlocProvider<ChatbotListBloc>(
            create: (context) => Modular.get<ChatbotListBloc>()),
        BlocProvider<MySubscriptionBloc>(
            create: (context) => Modular.get<MySubscriptionBloc>()),
        BlocProvider<PlanListBloc>(
            create: (context) => Modular.get<PlanListBloc>()),
        BlocProvider<ProductListBloc>(
            create: (context) => Modular.get<ProductListBloc>()),
        BlocProvider<RoomListBloc>(
            create: (context) => Modular.get<RoomListBloc>()),
        BlocProvider<EmailCubit>(
            create: (context) => Modular.get<EmailCubit>()),
        BlocProvider<AppBannerCubit>(
            create: (context) => Modular.get<AppBannerCubit>()),
      ],
      child: MediaQuery(
        data: MediaQuery.of(context)
            .copyWith(textScaler: const TextScaler.linear(1)),
        child: Portal(
          child: BlocBuilder<AppLanguageBloc, AppLanguageState>(
            builder: (context, appLanguage) {
              return MaterialApp.router(
                title: 'Saymee',
                debugShowCheckedModeBanner: false,
                theme: AppTheme.theme,
                locale: appLanguage.locale,
                scaffoldMessengerKey: AppKeys.scaffoldMessengerKey,
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: AppLocalizations.supportedLocales,
                routerConfig: Modular.routerConfig,
              );
            },
          ),
        ),
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // get common data
      Modular.get<AppNotificationCubit>().checkPermission();
      AppModuleHelper.getBackgroundData();
    }
    super.didChangeAppLifecycleState(state);
  }
}
