import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_keys.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/app/general/app_module_routes.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/general/chat_module_routes.dart';
import 'package:saymee/modules/chat/presentation/blocs/message_list/message_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_overlay_cubit.dart';

class ChatModuleHelper {
  ChatModuleHelper._();

  static void goToChatBotPage({
    required int roomId,
    required RoomType type,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleChat}${ChatModuleRoutes.chatbot}/$roomId',
      arguments: {
        'roomId': roomId,
        'type': type,
      },
    );
  }

  static void pushReplaceChatBotPage({
    required int roomId,
    required RoomType type,
  }) {
    Modular.to.pushReplacementNamed(
      '${AppRoutes.moduleChat}${ChatModuleRoutes.chatbot}/$roomId',
      arguments: {
        'roomId': roomId,
        'type': type,
      },
    );
  }

  static void goToPhotoViewPage({
    required String url,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleChat}${ChatModuleRoutes.photoView}',
      arguments: {
        'url': url,
      },
    );
  }

  static void getRoomList() {
    final roomListBloc = Modular.get<RoomListBloc>();
    if (AuthModuleHelper.checkAccessTokenValid()) {
      roomListBloc.add(RoomGetListRequested());
    }
  }

  static void addMessageToRoom(MessageModel message) {
    final roomListBloc = Modular.get<RoomListBloc>();
    roomListBloc.add(RoomAddMessageRequest(message: message));
  }

  static void updateNotificationRoom(
      {required int roomId, required int notification}) {
    final roomListBloc = Modular.get<RoomListBloc>();
    roomListBloc.add(RoomUpdateNotificationRequest(
      roomId: roomId,
      notification: notification,
    ));
  }

  static void goToCreateRoomPage({
    required ChatbotModel chatbot,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleChat}${ChatModuleRoutes.createRoom}',
      arguments: {
        'chatbot': chatbot,
      },
    );
  }

  static void goToGroupSettingPage({
    required int roomId,
    required RoomInfoBloc roomInfoBloc,
    required MessageListBloc messageListBloc,
    required SearchOverlayCubit searchOverlayCubit,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleChat}${ChatModuleRoutes.groupSetting}',
      arguments: {
        'roomId': roomId,
        'roomInfoBloc': roomInfoBloc,
        'messageListBloc': messageListBloc,
        'searchOverlayCubit': searchOverlayCubit,
      },
    );
  }

  static void goToUpdateGroupPage({
    required RoomInfoBloc roomInfoBloc,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleChat}${ChatModuleRoutes.updateGroup}',
      arguments: {
        'roomInfoBloc': roomInfoBloc,
      },
    );
  }

  static void navigateToRoom(int roomId) {
    final path = Modular.to.path;
    if (path == '${AppRoutes.moduleApp}${AppModuleRoutes.main}') {
      ChatModuleHelper.goToChatBotPage(
        roomId: roomId,
        type: RoomType.group,
      );
    } else if (path ==
        '${AppRoutes.moduleChat}${ChatModuleRoutes.chatbot}/$roomId') {
      return;
    } else {
      AppModuleHelper.navigateToMainPage();
      ChatModuleHelper.goToChatBotPage(
        roomId: roomId,
        type: RoomType.group,
      );
    }
  }

  static void goToGroupMemberPage({
    required RoomInfoBloc roomInfoBloc,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleChat}${ChatModuleRoutes.groupMember}',
      arguments: {
        'roomInfoBloc': roomInfoBloc,
      },
    );
  }

  static void goToJoinGroupPage({
    required RoomModel room,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleChat}${ChatModuleRoutes.joinGroup}',
      arguments: {
        'room': room,
      },
    );
  }

  static void goToMemberRequestPage({
    required RoomInfoBloc roomInfoBloc,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleChat}${ChatModuleRoutes.memberRequest}',
      arguments: {
        'roomInfoBloc': roomInfoBloc,
      },
    );
  }

  static void joinWithSecret(String secret) async {
    final context = AppKeys.navigatorKey.currentContext;
    if (context != null) {
      final chatRepository = Modular.get<ChatRepository>();
      AppLoading.show(context: context);

      final result = await chatRepository.join(secret: secret);

      if (context.mounted) {
        AppLoading.turnOff(context: context);
      }

      result.fold((l) {
        AppDialog.showErrorDialog(context: context, error: l.reason);
      }, (r) {
        RoomModel? room = r['room'];

        ChatModuleHelper.goToChatBotPage(
          roomId: room?.roomId ?? -1,
          type: RoomType.group,
        );
      });
    }
  }
}
