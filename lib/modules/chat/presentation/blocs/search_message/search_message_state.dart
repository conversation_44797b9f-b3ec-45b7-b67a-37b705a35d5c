import 'package:saymee/modules/chat/data/models/message_model.dart';

abstract class SearchMessageState {}

class SearchMessageInitial extends SearchMessageState {}

class SearchMessageLoading extends SearchMessageState {}

class SearchMessageLoaded extends SearchMessageState {
  final List<MessageModel> messages;

  SearchMessageLoaded({required this.messages});
}

class SearchMessageError extends SearchMessageState {
  final String message;

  SearchMessageError({required this.message});
}
