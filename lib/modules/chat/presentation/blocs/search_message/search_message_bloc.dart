import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_message/search_message_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_message/search_message_state.dart';
import 'package:stream_transform/stream_transform.dart';

const debounceDuration = Duration(milliseconds: 300);

EventTransformer<E> debounceRestartable<E>(Duration duration) {
  return (events, mapper) {
    return events.debounce(duration).switchMap(mapper);
  };
}

class SearchMessageBloc extends Bloc<SearchMessageEvent, SearchMessageState> {
  final ChatRepository repository;

  CancelToken? _cancelToken;

  SearchMessageBloc({required this.repository})
      : super(SearchMessageInitial()) {
    on<SearchTextMessageChanged>(
      _onSearchText,
      transformer: debounceRestartable(debounceDuration),
    );
  }

  Future<void> _onSearchText(
      SearchTextMessageChanged event, Emitter<SearchMessageState> emit) async {
    _cancelToken?.cancel();
    _cancelToken = CancelToken();

    if (event.text.isEmpty) {
      emit(SearchMessageInitial());
      return;
    }

    emit(SearchMessageLoading());

    final result = await repository.searchMessage(
      q: event.text,
      roomId: event.roomId,
      cancelToken: _cancelToken,
    );

    result.fold((l) {
      emit(SearchMessageError(message: l.reason));
    }, (r) {
      final List<MessageModel> messages = r['messages'];

      emit(SearchMessageLoaded(messages: messages.reversed.toList()));
    });
  }
}
