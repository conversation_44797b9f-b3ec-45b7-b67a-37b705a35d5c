import 'package:saymee/modules/chat/data/models/room_model.dart';

abstract class SearchRoomState {}

class SearchRoomInitial extends SearchRoomState {}

class SearchRoomLoading extends SearchRoomState {}

class SearchRoomLoaded extends SearchRoomState {
  final List<RoomModel> rooms;

  SearchRoomLoaded({required this.rooms});
}

class SearchRoomError extends SearchRoomState {
  final String message;

  SearchRoomError({required this.message});
}
