import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_room/search_room_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_room/search_room_state.dart';
import 'package:stream_transform/stream_transform.dart';

const debounceDuration = Duration(milliseconds: 300);

EventTransformer<E> debounceRestartable<E>(Duration duration) {
  return (events, mapper) {
    return events.debounce(duration).switchMap(mapper);
  };
}

class SearchRoomBloc extends Bloc<SearchRoomEvent, SearchRoomState> {
  final ChatRepository repository;

  CancelToken? _cancelToken;

  SearchRoomBloc({required this.repository}) : super(SearchRoomInitial()) {
    on<SearchTextChanged>(
      _onSearchText,
      transformer: debounceRestartable(debounceDuration),
    );
  }

  Future<void> _onSearchText(
      SearchTextChanged event, Emitter<SearchRoomState> emit) async {
    _cancelToken?.cancel();
    _cancelToken = CancelToken();

    if (event.text.isEmpty) {
      emit(SearchRoomInitial());
      return;
    }

    final result = await repository.searchRoom(
      q: event.text,
      cancelToken: _cancelToken,
    );

    result.fold((l) {
      emit(SearchRoomError(message: l.reason));
    }, (r) {
      final List<RoomModel> rooms = r['rooms'];

      emit(SearchRoomLoaded(rooms: rooms));
    });
  }
}
