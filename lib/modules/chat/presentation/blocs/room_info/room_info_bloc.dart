import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/member_model.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';

import 'room_info_event.dart';
import 'room_info_state.dart';

class RoomInfoBloc extends Bloc<RoomInfoEvent, RoomInfoState> {
  final ChatRepository repository;

  RoomInfoBloc({required this.repository})
      : super(const RoomInfoState.initial()) {
    on<RoomInfoEvent>((event, emit) async {
      if (event is RoomGetInfoRequested) {
        emit(RoomInfoState.pending(room: state.room));

        final result = await repository.getRoom(
          roomId: event.roomId,
        );

        result.fold((l) {
          emit(RoomInfoState.rejected(
            code: l.code,
            reason: l.reason,
            room: state.room,
          ));
        }, (r) {
          final RoomModel room = r['room'];

          emit(RoomInfoState.fulfilled(room: room));
        });
      } else if (event is RoomBlockMemberRequested) {
        List<MemberModel> members =
            List<MemberModel>.from(state.room?.members ?? []);

        try {
          int index = members.indexWhere((item) => item.userId == event.userId);

          if (index != -1) {
            members[index] =
                members[index].copyWith(status: MemberStatus.blocked);
          }
        } catch (e) {
          Utils.debugLog(e);
        }

        final updatedRoom = state.room?.copyWith(members: members);

        emit(RoomInfoState.fulfilled(room: updatedRoom));
        repository.block(
          roomId: event.roomId,
          userId: event.userId,
        );
      } else if (event is RoomUnblockMemberRequested) {
        List<MemberModel> members =
            List<MemberModel>.from(state.room?.members ?? []);

        try {
          int index = members.indexWhere((item) => item.userId == event.userId);

          if (index != -1) {
            members[index] =
                members[index].copyWith(status: MemberStatus.joined);
          }
        } catch (e) {
          Utils.debugLog(e);
        }

        final updatedRoom = state.room?.copyWith(members: members);

        emit(RoomInfoState.fulfilled(room: updatedRoom));

        repository.unblock(
          roomId: event.roomId,
          userId: event.userId,
        );
      } else if (event is RoomAcceptMemberRequested) {
        List<MemberModel> members =
            List<MemberModel>.from(state.room?.members ?? []);

        try {
          int index =
              members.indexWhere((item) => item.memberId == event.memberId);

          if (index != -1) {
            members[index] =
                members[index].copyWith(status: MemberStatus.joined);
          }
        } catch (e) {
          Utils.debugLog(e);
        }

        int memberCount = state.room?.memberCount ?? 0;
        int requestCount = state.room?.requestCount ?? 0;

        memberCount = memberCount + 1;
        requestCount = requestCount == 0 ? 0 : (requestCount - 1);

        final updatedRoom = state.room?.copyWith(
          members: members,
          memberCount: memberCount,
          requestCount: requestCount,
        );

        emit(RoomInfoState.fulfilled(room: updatedRoom));

        repository.acceptMember(memberId: event.memberId);
      } else if (event is RoomRejectMemberRequested) {
        List<MemberModel> members =
            List<MemberModel>.from(state.room?.members ?? []);

        members.removeWhere((member) => member.memberId == event.memberId);

        int requestCount = state.room?.requestCount ?? 0;

        requestCount = requestCount == 0 ? 0 : (requestCount - 1);

        final updatedRoom = state.room?.copyWith(
          members: members,
          requestCount: requestCount,
        );

        emit(RoomInfoState.fulfilled(room: updatedRoom));

        repository.rejectMember(memberId: event.memberId);
      } else if (event is RoomUpdateDescriptionRequested) {
        final updatedRoom =
            state.room?.copyWith(description: event.description);

        emit(RoomInfoState.fulfilled(room: updatedRoom));
      } else if (event is RoomUpdateNotificationRequested) {
        if (state.room?.myMember != null) {
          List<MemberModel> members =
              List<MemberModel>.from(state.room?.members ?? []);

          MemberModel newMyMember =
              state.room!.myMember!.copyWith(notification: event.notification);

          try {
            int index = members
                .indexWhere((item) => item.memberId == newMyMember.memberId);

            if (index != -1) {
              members[index] =
                  members[index].copyWith(notification: event.notification);
            }
          } catch (e) {
            Utils.debugLog(e);
          }

          final updatedRoom =
              state.room?.copyWith(members: members, myMember: newMyMember);

          emit(RoomInfoState.fulfilled(room: updatedRoom));
        }
      }
    });
  }
}
