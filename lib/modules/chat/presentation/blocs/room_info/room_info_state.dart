import 'package:equatable/equatable.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';

final class RoomInfoState extends Equatable {
  final bool isRequesting;
  final String reason;
  final String code;
  final RoomModel? room;

  const RoomInfoState._({
    this.isRequesting = false,
    this.reason = '',
    this.code = '',
    this.room,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        reason,
        code,
        room,
      ];

  const RoomInfoState.initial() : this._();
  const RoomInfoState.pending({
    RoomModel? room,
  }) : this._(
          isRequesting: true,
          room: room,
        );
  const RoomInfoState.rejected({
    required String code,
    required String reason,
    RoomModel? room,
  }) : this._(
          reason: reason,
          code: code,
          room: room,
        );
  const RoomInfoState.fulfilled({
    RoomModel? room,
  }) : this._(
          room: room,
        );
}
