import 'package:equatable/equatable.dart';

sealed class RoomInfoEvent extends Equatable {
  const RoomInfoEvent();

  @override
  List<Object?> get props => [];
}

final class RoomGetInfoRequested extends RoomInfoEvent {
  final int roomId;

  const RoomGetInfoRequested({required this.roomId});
}

final class RoomBlockMemberRequested extends RoomInfoEvent {
  final int roomId;
  final int userId;

  const RoomBlockMemberRequested({
    required this.roomId,
    required this.userId,
  });
}

final class RoomUnblockMemberRequested extends RoomInfoEvent {
  final int roomId;
  final int userId;

  const RoomUnblockMemberRequested({
    required this.roomId,
    required this.userId,
  });
}

final class RoomAcceptMemberRequested extends RoomInfoEvent {
  final int memberId;

  const RoomAcceptMemberRequested({
    required this.memberId,
  });
}

final class RoomRejectMemberRequested extends RoomInfoEvent {
  final int memberId;

  const RoomRejectMemberRequested({
    required this.memberId,
  });
}

final class RoomUpdateDescriptionRequested extends RoomInfoEvent {
  final String description;

  const RoomUpdateDescriptionRequested({
    required this.description,
  });
}

final class RoomUpdateNotificationRequested extends RoomInfoEvent {
  final int notification;

  const RoomUpdateNotificationRequested({
    required this.notification,
  });
}
