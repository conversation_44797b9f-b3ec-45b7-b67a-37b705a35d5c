import 'dart:collection';

import 'package:equatable/equatable.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';

final class MessageListState extends Equatable {
  final bool isRequesting;
  final String reason;
  final String code;
  final LinkedHashMap<String, MessageModel>? messagesMap;
  final bool hasReachedMax;
  final bool updateFlag;
  final bool haveUserMessage;
  final int? scrollIndex;

  const MessageListState._({
    this.isRequesting = false,
    this.reason = '',
    this.code = '',
    this.messagesMap,
    this.hasReachedMax = false,
    this.updateFlag = false,
    this.haveUserMessage = false,
    this.scrollIndex,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        reason,
        code,
        messagesMap,
        hasReachedMax,
        updateFlag,
        haveUserMessage,
        scrollIndex,
      ];

  const MessageListState.initial() : this._();
  const MessageListState.pending({
    LinkedHashMap<String, MessageModel>? messagesMap,
  }) : this._(
          isRequesting: true,
        );
  const MessageListState.rejected({
    required String code,
    required String reason,
  }) : this._(
          reason: reason,
          code: code,
        );
  const MessageListState.fulfilled({
    LinkedHashMap<String, MessageModel>? messagesMap,
    bool hasReachedMax = false,
    bool updateFlag = false,
    bool haveUserMessage = false,
    int? scrollIndex,
  }) : this._(
          messagesMap: messagesMap,
          hasReachedMax: hasReachedMax,
          updateFlag: updateFlag,
          haveUserMessage: haveUserMessage,
          scrollIndex: scrollIndex,
        );
}
