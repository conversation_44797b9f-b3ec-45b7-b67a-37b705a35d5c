import 'dart:collection';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';

import 'message_list_event.dart';
import 'message_list_state.dart';

class MessageListBloc extends Bloc<MessageListEvent, MessageListState> {
  final ChatRepository repository;

  final int _limit = 20; // Number of items to fetch per request
  int _offset = 0;

  MessageListBloc({required this.repository})
      : super(const MessageListState.initial()) {
    on<MessageListEvent>(
      (event, emit) async {
        if (event is MessageGetListRequested) {
          if (state.isRequesting) {
            return;
          }

          emit(const MessageListState.pending());
          _offset = 0;

          final result = await repository.getRoomMessages(
            roomId: event.roomId,
            offset: _offset,
            limit: _limit,
          );

          result.fold((l) {
            emit(MessageListState.rejected(
              code: l.code,
              reason: l.reason,
            ));
          }, (r) {
            final List<MessageModel> messages = r['messages'] ?? [];
            bool haveUserMessage = r['haveUserMessage'];

            LinkedHashMap<String, MessageModel> messagesMap =
                LinkedHashMap.fromIterable(
              messages,
              key: (element) =>
                  ((element as MessageModel).messageId?.toString() ?? '0'),
              value: (element) => (element as MessageModel),
            );

            emit(MessageListState.fulfilled(
              messagesMap: messagesMap,
              hasReachedMax: messages.length < _limit,
              haveUserMessage: haveUserMessage,
            ));
          });
        } else if (event is MessageGetMore) {
          if (state.messagesMap != null) {
            if (state.hasReachedMax) {
              return;
            }

            _offset = state.messagesMap!.length;

            final result = await repository.getRoomMessages(
              roomId: event.roomId,
              offset: _offset,
              limit: _limit,
            );

            result.fold((l) {
              emit(MessageListState.fulfilled(
                messagesMap: state.messagesMap,
                hasReachedMax: true,
                updateFlag: !state.updateFlag,
                haveUserMessage: state.haveUserMessage,
              ));
            }, (r) {
              final List<MessageModel> messages = r['messages'] ?? [];
              bool haveUserMessage = r['haveUserMessage'];

              LinkedHashMap<String, MessageModel> messagesMap =
                  state.messagesMap ?? LinkedHashMap.from({});

              for (var element in messages) {
                messagesMap[element.messageId?.toString() ?? "0"] = element;
              }

              emit(MessageListState.fulfilled(
                messagesMap: messagesMap,
                hasReachedMax: messages.length < _limit,
                updateFlag: !state.updateFlag,
                haveUserMessage: (state.haveUserMessage || haveUserMessage),
              ));
            });
          }
        } else if (event is MessageAddData) {
          LinkedHashMap<String, MessageModel> messagesMap =
              state.messagesMap ?? LinkedHashMap.from({});
          LinkedHashMap<String, MessageModel> newMessagesMap = LinkedHashMap();

          newMessagesMap[event.message.messageId.toString()] = event.message;
          newMessagesMap.addAll(messagesMap);

          emit(MessageListState.fulfilled(
            messagesMap: newMessagesMap,
            hasReachedMax: state.hasReachedMax,
            updateFlag: !state.updateFlag,
            haveUserMessage: true,
          ));
        } else if (event is MessageAddChunk) {
          LinkedHashMap<String, MessageModel> messagesMap =
              state.messagesMap ?? LinkedHashMap.from({});

          if (messagesMap[event.message.messageId.toString()] != null) {
            MessageModel message =
                messagesMap[event.message.messageId.toString()]!;
            final newMessage = event.isEnd
                ? event.message
                : message.copyWith(
                    content: ((message.content ?? '') +
                        (event.message.content ?? '')),
                  );
            messagesMap[event.message.messageId.toString()] = newMessage;

            //Utils.debugLog(messagesMap);

            emit(MessageListState.fulfilled(
              messagesMap: messagesMap,
              hasReachedMax: state.hasReachedMax,
              updateFlag: !state.updateFlag,
              haveUserMessage: state.haveUserMessage,
            ));
          } else {
            LinkedHashMap<String, MessageModel> newMessagesMap =
                LinkedHashMap();

            newMessagesMap[event.message.messageId.toString()] = event.message;
            newMessagesMap.addAll(messagesMap);

            emit(MessageListState.fulfilled(
              messagesMap: newMessagesMap,
              hasReachedMax: state.hasReachedMax,
              updateFlag: !state.updateFlag,
              haveUserMessage: state.haveUserMessage,
            ));
          }
        } else if (event is MessageClearContext) {
          LinkedHashMap<String, MessageModel> newMessagesMap = LinkedHashMap();
          emit(MessageListState.fulfilled(
            messagesMap: newMessagesMap,
            hasReachedMax: true,
            updateFlag: !state.updateFlag,
            haveUserMessage: false,
          ));
        } else if (event is SearchMessageRequest) {
          LinkedHashMap<String, MessageModel> messagesMap =
              state.messagesMap ?? LinkedHashMap.from({});

          final messageSearch = event.message;

          if (messagesMap[messageSearch.messageId.toString()] != null) {
            final List<MessageModel> messageList = messagesMap.values.toList();

            final index = messageList
                .indexWhere((e) => e.messageId == messageSearch.messageId);

            if (index != -1) {
              emit(MessageListState.fulfilled(
                messagesMap: state.messagesMap,
                hasReachedMax: state.hasReachedMax,
                haveUserMessage: state.haveUserMessage,
                updateFlag: !state.updateFlag,
                scrollIndex: index,
              ));
            }
          } else {
            final localState = state;
            emit(const MessageListState.pending());

            final since = messageSearch.time != null
                ? DateFormat('yyyy-MM-dd').format(messageSearch.time!)
                : null;

            final result = await repository.getRoomMessages(
              roomId: event.roomId,
              limit: 1000000,
              since: since,
              scrollMessageId: messageSearch.messageId,
            );

            result.fold((l) {
              emit(MessageListState.fulfilled(
                messagesMap: localState.messagesMap,
                hasReachedMax: localState.hasReachedMax,
                haveUserMessage: localState.haveUserMessage,
                updateFlag: localState.updateFlag,
              ));
            }, (r) {
              final List<MessageModel> messages = r['messages'] ?? [];
              bool haveUserMessage = r['haveUserMessage'];
              int? scrollIndex = r['scrollIndex'];

              LinkedHashMap<String, MessageModel> messagesMap =
                  LinkedHashMap.fromIterable(
                messages,
                key: (element) =>
                    ((element as MessageModel).messageId?.toString() ?? '0'),
                value: (element) => (element as MessageModel),
              );

              emit(MessageListState.fulfilled(
                messagesMap: messagesMap,
                hasReachedMax: messages.length < _limit,
                haveUserMessage: haveUserMessage,
                scrollIndex: scrollIndex,
              ));
            });
          }
        }
      },
    );
  }
}
