import 'package:equatable/equatable.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';

sealed class Message<PERSON>ist<PERSON>vent extends Equatable {
  const MessageListEvent();

  @override
  List<Object?> get props => [];
}

final class MessageGetListRequested extends Message<PERSON>istEvent {
  final int roomId;

  const MessageGetListRequested({required this.roomId});
}

final class MessageGetMore extends MessageListEvent {
  final int roomId;

  const MessageGetMore({required this.roomId});
}

final class MessageAddData extends MessageListEvent {
  final MessageModel message;

  const MessageAddData({required this.message});

  @override
  List<Object?> get props => [
        message,
      ];
}

final class MessageAddChunk extends MessageListEvent {
  final MessageModel message;
  final bool isEnd;

  const MessageAddChunk({
    required this.message,
    this.isEnd = false,
  });

  @override
  List<Object?> get props => [
        message,
        isEnd,
      ];
}

final class MessageClearContext extends MessageListEvent {}

final class SearchMessageRequest extends MessageListEvent {
  final int roomId;
  final MessageModel message;

  const SearchMessageRequest({
    required this.roomId,
    required this.message,
  });
}
