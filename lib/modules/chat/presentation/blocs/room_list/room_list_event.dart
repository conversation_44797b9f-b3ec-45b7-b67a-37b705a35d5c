import 'package:equatable/equatable.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';

sealed class RoomListEvent extends Equatable {
  const RoomListEvent();

  @override
  List<Object?> get props => [];
}

final class RoomGetListRequested extends Room<PERSON>istEvent {}

final class RoomResetRequested extends Room<PERSON>istEvent {}

final class RoomDeleteRequested extends RoomListEvent {
  final int roomId;

  const RoomDeleteRequested({required this.roomId});
}

final class RoomAddMessageRequest extends RoomListEvent {
  final MessageModel message;

  const RoomAddMessageRequest({required this.message});
}

final class RoomUpdateNotificationRequest extends RoomListEvent {
  final int roomId;
  final int notification;

  const RoomUpdateNotificationRequest({
    required this.roomId,
    required this.notification,
  });
}
