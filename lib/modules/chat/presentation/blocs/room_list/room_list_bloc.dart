import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';

import 'room_list_event.dart';
import 'room_list_state.dart';

class RoomListBloc extends Bloc<RoomListEvent, RoomListState> {
  final ChatRepository repository;
  CancelToken cancelToken = CancelToken();

  RoomListBloc({required this.repository})
      : super(const RoomListState.initial()) {
    on<RoomListEvent>((event, emit) async {
      if (event is RoomGetListRequested) {
        if (state.isRequesting) {
          return;
        }

        emit(RoomListState.pending(
          oneToOneRooms: state.oneToOneRooms,
          groupRooms: state.groupRooms,
        ));

        final result = await repository.getMyRooms(
          cancelToken: cancelToken,
        );

        result.fold((l) {
          emit(RoomListState.rejected(
            code: l.code,
            reason: l.reason,
            oneToOneRooms: state.oneToOneRooms,
            groupRooms: state.groupRooms,
          ));
        }, (r) {
          final List<RoomModel>? oneToOneRooms = r['oneToOneRooms'];
          final List<RoomModel>? groupRooms = r['groupRooms'];
          bool? isCancel = r['isCancel'];

          if (isCancel == true) {
            emit(const RoomListState.initial());
          } else {
            emit(RoomListState.fulfilled(
              oneToOneRooms: oneToOneRooms,
              groupRooms: groupRooms,
            ));
          }
        });
      } else if (event is RoomResetRequested) {
        if (state.isRequesting) {
          cancelToken.cancel();
          cancelToken = CancelToken();
        } else {
          emit(const RoomListState.initial());
        }
      } else if (event is RoomDeleteRequested) {
        final roomId = event.roomId;

        List<RoomModel> oneToOneRooms =
            List<RoomModel>.from(state.oneToOneRooms ?? []);
        List<RoomModel> groupRooms =
            List<RoomModel>.from(state.groupRooms ?? []);

        try {
          oneToOneRooms.removeWhere((room) => room.roomId == roomId);
          groupRooms.removeWhere((room) => room.roomId == roomId);
        } catch (e) {
          Utils.debugLogError(e);
        }

        emit(RoomListState.fulfilled(
          oneToOneRooms: oneToOneRooms,
          groupRooms: groupRooms,
        ));
      } else if (event is RoomAddMessageRequest) {
        final message = event.message;

        List<RoomModel> oneToOneRooms =
            List<RoomModel>.from(state.oneToOneRooms ?? []);
        List<RoomModel> groupRooms =
            List<RoomModel>.from(state.groupRooms ?? []);

        try {
          int indexOneRoom =
              oneToOneRooms.indexWhere((room) => room.roomId == message.roomId);

          if (indexOneRoom != -1) {
            oneToOneRooms[indexOneRoom] = oneToOneRooms[indexOneRoom].copyWith(
              lastMessage: message,
              lastMessageId: message.messageId,
            );
          }

          int indexGroupRoom =
              groupRooms.indexWhere((room) => room.roomId == message.roomId);

          if (indexGroupRoom != -1) {
            groupRooms[indexGroupRoom] = groupRooms[indexGroupRoom].copyWith(
              lastMessage: message,
              lastMessageId: message.messageId,
            );
          }
        } catch (e) {
          Utils.debugLog(e);
        }

        emit(RoomListState.fulfilled(
          oneToOneRooms: oneToOneRooms,
          groupRooms: groupRooms,
          isRequesting: state.isRequesting,
        ));
      } else if (event is RoomUpdateNotificationRequest) {
        List<RoomModel> oneToOneRooms =
            List<RoomModel>.from(state.oneToOneRooms ?? []);
        List<RoomModel> groupRooms =
            List<RoomModel>.from(state.groupRooms ?? []);

        try {
          int indexOneRoom =
              oneToOneRooms.indexWhere((room) => room.roomId == event.roomId);

          if (indexOneRoom != -1) {
            oneToOneRooms[indexOneRoom] = oneToOneRooms[indexOneRoom].copyWith(
              myMember: oneToOneRooms[indexOneRoom]
                  .myMember
                  ?.copyWith(notification: event.notification),
            );
          }

          int indexGroupRoom =
              groupRooms.indexWhere((room) => room.roomId == event.roomId);

          if (indexGroupRoom != -1) {
            groupRooms[indexGroupRoom] = groupRooms[indexGroupRoom].copyWith(
              myMember: oneToOneRooms[indexGroupRoom]
                  .myMember
                  ?.copyWith(notification: event.notification),
            );
          }
        } catch (e) {
          Utils.debugLog(e);
        }

        emit(RoomListState.fulfilled(
          oneToOneRooms: oneToOneRooms,
          groupRooms: groupRooms,
          isRequesting: state.isRequesting,
        ));
      }
    });
  }
}
