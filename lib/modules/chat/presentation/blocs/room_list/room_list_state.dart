import 'package:equatable/equatable.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';

final class RoomListState extends Equatable {
  final bool isRequesting;
  final String reason;
  final String code;
  final List<RoomModel>? oneToOneRooms;
  final List<RoomModel>? groupRooms;

  const RoomListState._({
    this.isRequesting = false,
    this.reason = '',
    this.code = '',
    this.oneToOneRooms,
    this.groupRooms,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        reason,
        code,
        oneToOneRooms,
        groupRooms,
      ];

  const RoomListState.initial() : this._();
  const RoomListState.pending({
    List<RoomModel>? oneToOneRooms,
    List<RoomModel>? groupRooms,
  }) : this._(
          isRequesting: true,
          oneToOneRooms: oneToOneRooms,
          groupRooms: groupRooms,
        );
  const RoomListState.rejected({
    required String code,
    required String reason,
    List<RoomModel>? oneToOneRooms,
    List<RoomModel>? groupRooms,
  }) : this._(
          reason: reason,
          code: code,
          oneToOneRooms: oneToOneRooms,
          groupRooms: groupRooms,
        );
  const RoomListState.fulfilled({
    List<RoomModel>? oneToOneRooms,
    List<RoomModel>? groupRooms,
    bool isRequesting = false,
  }) : this._(
          oneToOneRooms: oneToOneRooms,
          groupRooms: groupRooms,
          isRequesting: isRequesting,
        );
}
