import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:saymee/core/components/app_cupertino_action_sheet.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:saymee/core/components/app_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/member_model.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_event.dart';

class CellMemberItem extends StatelessWidget {
  const CellMemberItem(
      {super.key,
      required this.member,
      required this.roomInfoBloc,
      required this.roomId});

  final MemberModel member;
  final RoomInfoBloc roomInfoBloc;
  final int roomId;

  String getMemberName(BuildContext context) {
    if (member.role == MemberRole.admin) {
      return 'Admin';
    }

    switch (member.status) {
      case MemberStatus.joined:
        return 'Member';
      case MemberStatus.blocked:
        return 'Blocked';
      case MemberStatus.invited:
        return 'Invited';
      case MemberStatus.requested:
        return 'Requested';
      default:
        return '';
    }
  }

  void _memberAction(BuildContext context) {
    if (member.role == MemberRole.admin) {
      return;
    }

    if (roomInfoBloc.state.room?.myMember?.role != MemberRole.admin) {
      return;
    }

    switch (member.status) {
      case MemberStatus.joined:
        _showJoinedMember(context);
        break;
      case MemberStatus.blocked:
        _showBlockedMember(context);
        break;
      default:
    }
  }

  void _showJoinedMember(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return AppCupertinoActionSheet(
          title: Text(
            member.user?.fullname ?? '',
            style: CustomStyle.large.smb.copyWith(color: AppColors.system8),
          ),
          actions: [
            AppCupertinoActionSheetAction(
              onPressed: () {
                Modular.to.pop();
                roomInfoBloc.add(RoomBlockMemberRequested(
                  roomId: roomId,
                  userId: member.userId ?? -1,
                ));
              },
              child: Row(
                children: [
                  SvgPicture.asset(AppIcons.iconBlock),
                  12.horizontalSpace,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.localization.blockMember,
                          style: CustomStyle.large.regular
                              .copyWith(color: AppColors.system8),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            AppCupertinoActionSheetAction(
              onPressed: () {
                Modular.to.pop();
                roomInfoBloc.add(
                    RoomRejectMemberRequested(memberId: member.memberId ?? -1));
              },
              child: Row(
                children: [
                  SvgPicture.asset(AppIcons.iconRemove),
                  12.horizontalSpace,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.localization.removeFromGroup,
                          style: CustomStyle.large.regular
                              .copyWith(color: AppColors.system8),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
          cancelButton: AppCupertinoButton(
            padding: const EdgeInsets.all(12),
            child: Text(
              context.localization.cancel,
              style: CustomStyle.large.smb.copyWith(color: AppColors.system6),
            ),
            onPressed: () {
              Modular.to.pop();
            },
          ),
        );
      },
    );
  }

  void _showBlockedMember(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return AppCupertinoActionSheet(
          title: Text(
            member.user?.fullname ?? (member.user?.username ?? ''),
            style: CustomStyle.large.smb.copyWith(color: AppColors.system8),
          ),
          actions: [
            AppCupertinoActionSheetAction(
              onPressed: () {
                Modular.to.pop();
                roomInfoBloc.add(RoomUnblockMemberRequested(
                  roomId: roomId,
                  userId: member.userId ?? -1,
                ));
              },
              child: Row(
                children: [
                  SvgPicture.asset(AppIcons.iconUnblock),
                  12.horizontalSpace,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.localization.unblockMember,
                          style: CustomStyle.large.regular
                              .copyWith(color: AppColors.system8),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            AppCupertinoActionSheetAction(
              onPressed: () {
                Modular.to.pop();
                roomInfoBloc.add(
                    RoomRejectMemberRequested(memberId: member.memberId ?? -1));
              },
              child: Row(
                children: [
                  SvgPicture.asset(AppIcons.iconRemove),
                  12.horizontalSpace,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.localization.removeFromGroup,
                          style: CustomStyle.large.regular
                              .copyWith(color: AppColors.system8),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
          cancelButton: AppCupertinoButton(
            padding: const EdgeInsets.all(12),
            child: Text(
              context.localization.cancel,
              style: CustomStyle.large.smb.copyWith(color: AppColors.system6),
            ),
            onPressed: () {
              Modular.to.pop();
            },
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (member.status == MemberStatus.invited ||
        member.status == MemberStatus.requested) {
      return Container();
    }

    return AppInkwell(
      onTap: () => _memberAction(context),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: const BoxDecoration(
          color: AppColors.system0,
          border: Border(
            top: BorderSide(width: 1, color: AppColors.system2),
          ),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                color: Colors.white,
                child: CachedNetworkImage(
                  imageUrl: Utils.convertImageUrl(member.user?.avatar),
                  width: 24,
                  height: 24,
                  fit: BoxFit.cover,
                  errorWidget: (context, error, stackTrace) {
                    return Image.asset(
                      width: 24,
                      height: 24,
                      AppImages.imageDefaultAvatar,
                    );
                  },
                ),
              ),
            ),
            8.horizontalSpace,
            Expanded(
              child: Text(
                (member.user?.fullname ?? (member.user?.username ?? '')),
                style: CustomStyle.large.regular
                    .copyWith(color: AppColors.system8),
              ),
            ),
            Text(
              getMemberName(context),
              style:
                  CustomStyle.medium.regular.copyWith(color: AppColors.system5),
            ).paddingOnly(left: 8),
          ],
        ),
      ),
    );
  }
}
