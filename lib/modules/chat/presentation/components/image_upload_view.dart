import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/presentation/blocs/upload_file/upload_file_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/upload_file/upload_file_event.dart';
import 'package:saymee/modules/app/presentation/blocs/upload_file/upload_file_state.dart';

class ImageUploadView extends StatelessWidget {
  const ImageUploadView({super.key, required this.uploadFileBloc});

  final UploadFileBloc uploadFileBloc;
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<UploadFileBloc, UploadFileState>(
      bloc: uploadFileBloc,
      listener: (context, state) {
        Utils.debugLog(state);
        if (!state.isRequesting &&
            state.medias == null &&
            !state.isCancel &&
            state.reason.isNotEmpty) {
          Fluttertoast.showToast(
            msg: state.reason,
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.CENTER,
            backgroundColor: AppColors.black400,
            textColor: AppColors.system0,
            fontSize: 14,
          );
        }
      },
      builder: (context, state) {
        bool isRequesting = state.isRequesting;
        bool isError = (state.medias == null || state.medias!.isEmpty);

        if (state.path == null) {
          return Container();
        }

        return Stack(
          children: [
            Hero(
              tag: 'hero-photo',
              child: isRequesting
                  ? Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: AppColors.system3,
                        borderRadius: BorderRadius.circular(16),
                      ),
                    )
                  : (isError
                      ? Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: AppColors.system3,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Center(
                            child: InkWell(
                              onTap: () => uploadFileBloc.add(
                                  FileUploadRequested(path: state.path ?? '')),
                              child: Transform.flip(
                                flipX: true,
                                child: const Icon(Icons.refresh),
                              ),
                            ),
                          ),
                        )
                      : ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                  width: 1, color: AppColors.system3),
                            ),
                            child: Image.file(
                              File(state.path!),
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                            ),
                          ),
                        )),
            ).paddingAll(12),
            Positioned(
              top: 0,
              right: 0,
              child: InkWell(
                onTap: () => uploadFileBloc.add(const CancelUpload()),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
