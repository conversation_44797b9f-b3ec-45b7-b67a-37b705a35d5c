import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';

class ActionGroup {
  ActionGroup._();

  static void showConfirmGroup(
    BuildContext context, {
    required String image,
    required String title,
    required String description,
    required VoidCallback confirmCallback,
  }) {
    AppDialog.show(
      context: context,
      buttons: [
        AppButton(
          buttonTitle: context.localization.cancel,
          outline: true,
          backgroundColor: AppColors.system0,
          borderColor: AppColors.system3,
          buttonColor: AppColors.system6,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0D000000), // Hexadecimal color #0000000D
              offset: Offset(0, 14), // Horizontal and vertical offset
              blurRadius: 14, // Blur radius
              spreadRadius: 0, // Spread radius
            ),
          ],
          onPressed: () {
            Modular.to.pop();
          },
        ),
        AppButton(
          buttonTitle: context.localization.yes,
          backgroundColor: AppColors.error,
          buttonColor: AppColors.system0,
          onPressed: () {
            Modular.to.pop();
            confirmCallback.call();
          },
        ),
      ],
      descriptionWidget: Column(
        children: [
          Image.asset(image),
          24.verticalSpace,
          Text(
            title,
            style: CustomStyle.h5.smb.copyWith(color: AppColors.error),
          ),
          4.verticalSpace,
          Text(
            description,
            textAlign: TextAlign.center,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system5),
          ),
          8.verticalSpace,
        ],
      ),
    );
  }
}
