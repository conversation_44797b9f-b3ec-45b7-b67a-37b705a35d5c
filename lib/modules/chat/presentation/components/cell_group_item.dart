import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';

class CellGroupItem extends StatelessWidget {
  const CellGroupItem({
    super.key,
    required this.room,
    this.isShowLastMessage = true,
  });

  final RoomModel room;
  final bool isShowLastMessage;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppColors.system0,
      child: InkWell(
        onTap: () {
          if (room.myMember != null &&
                  room.myMember?.status == MemberStatus.blocked ||
              room.myMember?.status == MemberStatus.joined) {
            ChatModuleHelper.goToChatBotPage(
              roomId: room.roomId ?? -1,
              type: RoomType.group,
            );
          } else {
            ChatModuleHelper.goToJoinGroupPage(room: room);
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1,
                color: AppColors.system2,
              ),
            ),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: CachedNetworkImage(
                  imageUrl: Utils.convertImageUrl(
                      room.logo ?? room.chatbot?.smallAvatar),
                  width: 40,
                  height: 40,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(),
                  errorWidget: (context, error, stackTrace) {
                    return Container(
                      width: 40,
                      height: 40,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topRight,
                          end: Alignment.bottomLeft,
                          colors: [
                            Color(0xFFD7E4FB), // #D7E4FB
                            Color(0xFFF9DEEC), // #F9DEEC
                          ],
                          stops: [0.1175, 0.8976], // 11.75% and 89.76%
                        ),
                      ),
                    );
                  },
                ),
              ),
              12.horizontalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            room.name ?? (room.chatbot?.name ?? ''),
                            style: CustomStyle.large.smb
                                .copyWith(color: AppColors.system8),
                          ),
                        ),
                        if (room.myMember?.notification == 0)
                          SvgPicture.asset(AppIcons.iconOffNotification)
                              .paddingOnly(left: 8),
                      ],
                    ),
                    if (room.lastMessage != null && isShowLastMessage)
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              room.lastMessage?.content ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: CustomStyle.medium.regular
                                  .copyWith(color: AppColors.system5),
                            ),
                          ),
                          if (room.lastMessage?.time != null)
                            Text(
                              "• ${Utils.getMessageDateString(room.lastMessage!.time!.toLocal())}",
                              style: CustomStyle.medium.regular
                                  .copyWith(color: AppColors.system5),
                            ).paddingOnly(left: 4),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
