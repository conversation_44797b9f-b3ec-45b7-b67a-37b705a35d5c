part of 'flutter_mentions.dart';

class OptionList extends StatelessWidget {
  const OptionList({
    super.key,
    required this.data,
    required this.onTap,
    required this.suggestionListHeight,
    this.suggestionBuilder,
    this.suggestionListDecoration,
  });

  final Widget Function(Map<String, dynamic>)? suggestionBuilder;

  final List<Map<String, dynamic>> data;

  final Function(Map<String, dynamic>) onTap;

  final double suggestionListHeight;

  final BoxDecoration? suggestionListDecoration;

  @override
  Widget build(BuildContext context) {
    return data.isNotEmpty
        ? Container(
            decoration: suggestionListDecoration ??
                const BoxDecoration(color: Colors.white),
            constraints: BoxConstraints(
              maxHeight: suggestionListHeight,
              minHeight: 0,
            ),
            child: ListView.builder(
              itemCount: data.length,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    onTap(data[index]);
                  },
                  child: suggestionBuilder != null
                      ? suggestionBuilder!(data[index])
                      : Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Row(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(15),
                                child: CachedNetworkImage(
                                  imageUrl: data[index]['photo'] as String,
                                  width: 30,
                                  height: 30,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Container(),
                                  errorWidget: (context, error, stackTrace) {
                                    return Image.asset(
                                      width: 30,
                                      height: 30,
                                      AppImages.imageDefaultAvatar,
                                    );
                                  },
                                ),
                              ),
                              8.horizontalSpace,
                              Expanded(
                                child: Container(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 10),
                                  decoration: const BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        width: 1,
                                        color: AppColors.system2,
                                      ),
                                    ),
                                  ),
                                  child: Text(
                                    data[index]['display'],
                                    style: CustomStyle.medium.regular
                                        .copyWith(color: AppColors.system8),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                );
              },
            ),
          )
        : Container();
  }
}
