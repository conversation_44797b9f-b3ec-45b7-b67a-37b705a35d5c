import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';

class CellChatItem extends StatelessWidget {
  const CellChatItem({super.key, required this.room});

  final RoomModel room;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppColors.system0,
      child: InkWell(
        onTap: () => ChatModuleHelper.goToChatBotPage(
          roomId: room.roomId ?? -1,
          type: RoomType.single,
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1,
                color: AppColors.system2,
              ),
            ),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: CachedNetworkImage(
                  imageUrl: Utils.convertImageUrl(
                      room.logo ?? room.chatbot?.smallAvatar),
                  width: 40,
                  height: 40,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(),
                  errorWidget: (context, error, stackTrace) {
                    return Container(
                      width: 40,
                      height: 40,
                      color: AppColors.system3,
                    );
                  },
                ),
              ),
              12.horizontalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      room.name ?? (room.chatbot?.name ?? ''),
                      style: CustomStyle.large.smb
                          .copyWith(color: AppColors.system8),
                    ),
                    if (room.firstMessage != null)
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              room.firstMessage?.content ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: CustomStyle.medium.regular
                                  .copyWith(color: AppColors.system5),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
