import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/components/text_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

class GroupIntroductionEditorSheet {
  static void showIntroEditorSheet(
    BuildContext context, {
    String? description,
    required void Function(String result) onSave,
  }) {
    showCupertinoModalBottomSheet(
      context: context,
      builder: (context) {
        return GroupIntroductionEditor(
          description: description,
          onSave: onSave,
        );
      },
    );
  }
}

class GroupIntroductionEditor extends StatefulWidget {
  const GroupIntroductionEditor({
    super.key,
    this.description,
    required this.onSave,
  });

  final String? description;
  final void Function(String result) onSave;

  @override
  State<GroupIntroductionEditor> createState() =>
      _GroupIntroductionEditorState();
}

class _GroupIntroductionEditorState extends State<GroupIntroductionEditor> {
  late TextEditingController editingController;

  @override
  void initState() {
    editingController = TextEditingController(text: widget.description);
    super.initState();
  }

  @override
  void dispose() {
    editingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppColors.system0,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Container(
            //   width: 50,
            //   height: 4,
            //   decoration: BoxDecoration(
            //     color: AppColors.system3,
            //     borderRadius: BorderRadius.circular(12),
            //   ),
            // ).paddingSymmetric(v: 12),
            16.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconInkwell(
                  path: AppIcons.iconClose,
                  color: AppColors.system5,
                  padding: const EdgeInsets.only(left: 16, right: 16),
                  onTap: () {
                    Modular.to.pop();
                  },
                ),
                Text(
                  context.localization.groupIntro,
                  style:
                      CustomStyle.large.smb.copyWith(color: AppColors.system8),
                ),
                TextInkwell(
                  text: context.localization.save,
                  color: AppColors.blue,
                  style: CustomStyle.large.medium,
                  padding: const EdgeInsets.only(right: 16),
                  onTap: () {
                    if (editingController.text.isNotEmpty) {
                      final text = editingController.text;
                      Modular.to.pop();
                      widget.onSave.call(text);
                    }
                  },
                ),
              ],
            ),
            12.verticalSpace,
            const Divider(
              height: 1,
              color: AppColors.system3,
            ),
            16.verticalSpace,
            TextField(
              controller: editingController,
              autofocus: true,
              minLines: 10,
              maxLines: 20,
              style:
                  CustomStyle.large.regular.copyWith(color: AppColors.system8),
              cursorColor: AppColors.blue,
              decoration: InputDecoration(
                hintText: '${context.localization.writeShortDescription}....',
                hintStyle: CustomStyle.large.regular
                    .copyWith(color: AppColors.system5),
                border: InputBorder.none,
              ),
            ).paddingSymmetric(h: 16),
            MediaQuery.of(context).viewPadding.bottom.verticalSpace,
            MediaQuery.of(context).viewInsets.bottom.verticalSpace,
          ],
        ),
      ),
    );
  }
}
