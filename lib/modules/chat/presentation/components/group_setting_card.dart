import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/widget_extension.dart';

class GroupSettingCard extends StatelessWidget {
  const GroupSettingCard({
    super.key,
    this.title,
    required this.list,
  });

  final List<KeyGroupSetting> list;
  final String? title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Text(
            title!,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system5),
          ).paddingOnly(bottom: 4),
        Container(
          decoration: BoxDecoration(
            color: AppColors.system0,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(width: 1, color: AppColors.system3),
          ),
          child: Column(
            children: [
              ...list.mapIndexed(
                (index, element) {
                  return Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: element.onTap,
                      customBorder: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: index == 0
                              ? const Radius.circular(15)
                              : Radius.zero,
                          topRight: index == 0
                              ? const Radius.circular(15)
                              : Radius.zero,
                          bottomLeft: index == list.length - 1
                              ? const Radius.circular(15)
                              : Radius.zero,
                          bottomRight: index == list.length - 1
                              ? const Radius.circular(15)
                              : Radius.zero,
                        ),
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 10),
                        decoration: BoxDecoration(
                          border: index == 0
                              ? null
                              : const Border(
                                  top: BorderSide(
                                    width: 1,
                                    color: AppColors.system3,
                                  ),
                                ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    element.title,
                                    style: CustomStyle.large.smb.copyWith(
                                      color: element.titleColor,
                                    ),
                                  ),
                                  Text(
                                    element.description,
                                    maxLines: element.descriptionMaxLines,
                                    overflow:
                                        element.descriptionMaxLines != null
                                            ? TextOverflow.ellipsis
                                            : null,
                                    style: CustomStyle.medium.regular.copyWith(
                                      color: element.descriptionColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (element.suffixWidget != null ||
                                element.isShowRightIndicator)
                              (element.suffixWidget ??
                                      SvgPicture.asset(
                                          AppIcons.iconChevronRight))
                                  .paddingOnly(left: 10),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class KeyGroupSetting {
  final String title;
  final String description;
  final Color titleColor;
  final Color descriptionColor;
  final int? descriptionMaxLines;
  final VoidCallback? onTap;
  final Widget? suffixWidget;
  final bool isShowRightIndicator;

  KeyGroupSetting({
    required this.title,
    required this.description,
    this.titleColor = AppColors.system8,
    this.descriptionColor = AppColors.system5,
    this.descriptionMaxLines,
    this.onTap,
    this.suffixWidget,
    this.isShowRightIndicator = true,
  });
}
