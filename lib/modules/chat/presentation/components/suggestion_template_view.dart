import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/modules/app/data/models/template_model.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/presentation/blocs/message_list/message_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/message_list/message_list_state.dart';

class SuggestionTemplateView extends StatelessWidget {
  const SuggestionTemplateView({
    super.key,
    required this.room,
    this.onTapTemplate,
  });

  final RoomModel? room;
  final void Function(String template)? onTapTemplate;

  Widget templateCard(BuildContext context, {required TemplateModel template}) {
    return Material(
      color: AppColors.system0,
      borderRadius: BorderRadius.circular(24),
      child: InkWell(
        onTap: () {
          if (template.template != null && template.template!.isNotEmpty) {
            onTapTemplate?.call(template.template!);
          }
        },
        customBorder:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 2 / 3),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                template.title ?? '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: CustomStyle.medium.medium
                    .copyWith(color: AppColors.system8),
              ),
              Text(
                template.description ?? '',
                style: CustomStyle.small.regular
                    .copyWith(color: AppColors.system6),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MessageListBloc, MessageListState>(
      builder: (context, state) {
        final templates = room?.chatbot?.templates ?? [];
        if (state.isRequesting || state.haveUserMessage || templates.isEmpty) {
          return Container();
        }
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              16.horizontalSpace,
              ...templates.map(
                (e) {
                  return templateCard(context, template: e)
                      .paddingOnly(right: 16);
                },
              ),
            ],
          ),
        ).paddingOnly(bottom: 8);
      },
    );
  }
}
