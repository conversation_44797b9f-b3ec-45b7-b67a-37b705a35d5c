import 'package:flutter/material.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';

class CellSearchMessageItem extends StatelessWidget {
  const CellSearchMessageItem({super.key, required this.message, this.onTap});

  final MessageModel message;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: const BoxDecoration(
        color: AppColors.system0,
        border: Border(
          bottom: BorderSide(width: 1, color: AppColors.system3),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              message.content ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style:
                  CustomStyle.medium.regular.copyWith(color: AppColors.system8),
            ),
          ),
          if (message.time != null)
            Text(
              Utils.getMessageDateString(message.time!.toLocal()),
              style:
                  CustomStyle.medium.regular.copyWith(color: AppColors.system5),
            ).paddingOnly(left: 4),
        ],
      ),
    ).inkwell(onTap, force: true);
  }
}
