import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:saymee/core/components/text_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_room/search_room_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_room/search_room_event.dart';

class SearchRoomField extends StatefulWidget {
  const SearchRoomField({
    super.key,
    required this.controller,
    required this.cancelUpdateCallback,
    required this.searchRoomBloc,
  });

  final TextEditingController controller;
  final void Function(bool isCancel) cancelUpdateCallback;
  final SearchRoomBloc searchRoomBloc;

  @override
  State<SearchRoomField> createState() => _SearchRoomFieldState();
}

class _SearchRoomFieldState extends State<SearchRoomField> {
  late FocusNode _focus;
  bool _showSuffixIcon = false;
  bool _focusState = false;
  bool _showCancel = false;

  @override
  void initState() {
    _focus = FocusNode();
    _focus.addListener(_setFocusState);
    widget.controller.addListener(_setShowSuffixIcon);
    super.initState();
  }

  @override
  void dispose() {
    _focus.dispose();
    super.dispose();
  }

  void _setFocusState() {
    if (_focus.hasFocus) {
      if (!_focusState && mounted) {
        setState(() {
          _focusState = true;
        });
      }

      if (!_showCancel && mounted) {
        setState(() {
          _showCancel = true;
          widget.cancelUpdateCallback.call(_showCancel);
        });
      }
    } else {
      if (_focusState && mounted) {
        setState(() {
          _focusState = false;
        });
      }
    }
  }

  void _setShowSuffixIcon() {
    if (widget.controller.text.isEmpty) {
      if (_showSuffixIcon && mounted) {
        setState(() {
          _showSuffixIcon = false;
        });
      }
    } else {
      if (!_showSuffixIcon && mounted) {
        setState(() {
          _showSuffixIcon = true;
        });
      }
    }
  }

  void _clearInput() {
    widget.controller.clear();
    widget.searchRoomBloc.add(SearchTextChanged(text: ""));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      decoration: const BoxDecoration(
        color: AppColors.system0,
        border: Border(
          bottom: BorderSide(
            width: 1,
            color: AppColors.system2,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.system1,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  width: 1,
                  color: _focusState ? AppColors.blue : AppColors.system1,
                ),
              ),
              child: TextField(
                controller: widget.controller,
                maxLines: 1,
                minLines: 1,
                style: CustomStyle.medium.regular
                    .copyWith(color: AppColors.system8),
                cursorColor: AppColors.blue,
                focusNode: _focus,
                onTapOutside: (event) => Utils.hideKeyboard(),
                onChanged: (value) =>
                    widget.searchRoomBloc.add(SearchTextChanged(text: value)),
                decoration: InputDecoration(
                  hintText: context.localization.findGroupsToJoin,
                  isDense: true,
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  label: null,
                  hintStyle: CustomStyle.medium.regular
                      .copyWith(color: AppColors.system5),
                  border: InputBorder.none,
                  prefixIcon: Container(
                    height: 16,
                    width: 16,
                    alignment: Alignment.center,
                    child: SvgPicture.asset(AppIcons.iconDefaultSearch),
                  ),
                  suffixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (_showSuffixIcon) ...[
                        IconButton(
                          iconSize: 20,
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          icon: const Icon(
                            Icons.close,
                            color: AppColors.neutral80,
                            size: 20,
                          ),
                          onPressed: () => {_clearInput()},
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (_showCancel)
            TextInkwell(
              text: context.localization.cancel,
              color: AppColors.blue,
              style: CustomStyle.medium.medium,
              onTap: () {
                setState(() {
                  _showCancel = false;
                  widget.cancelUpdateCallback.call(_showCancel);
                });
                _clearInput();
                _focus.unfocus();
              },
            ).paddingOnly(left: 8),
        ],
      ),
    );
  }
}
