import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:saymee/core/components/text_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_message/search_message_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_message/search_message_event.dart';

class SearchMessageField extends StatefulWidget {
  const SearchMessageField({
    super.key,
    required this.controller,
    required this.cancelUpdateCallback,
    required this.searchMessageBloc,
    required this.roomId,
  });

  final TextEditingController controller;
  final VoidCallback cancelUpdateCallback;
  final SearchMessageBloc searchMessageBloc;
  final int roomId;

  @override
  State<SearchMessageField> createState() => _SearchMessageFieldState();
}

class _SearchMessageFieldState extends State<SearchMessageField> {
  late FocusNode _focus;
  bool _showSuffixIcon = false;
  bool _focusState = false;

  @override
  void initState() {
    _focus = FocusNode();
    _focus.addListener(_setFocusState);
    widget.controller.addListener(_setShowSuffixIcon);
    super.initState();
  }

  @override
  void dispose() {
    _focus.dispose();
    super.dispose();
  }

  void _setFocusState() {
    if (_focus.hasFocus) {
      if (!_focusState && mounted) {
        setState(() {
          _focusState = true;
        });
      }
    } else {
      if (_focusState && mounted) {
        setState(() {
          _focusState = false;
        });
      }
    }
  }

  void _setShowSuffixIcon() {
    if (widget.controller.text.isEmpty) {
      if (_showSuffixIcon && mounted) {
        setState(() {
          _showSuffixIcon = false;
        });
      }
    } else {
      if (!_showSuffixIcon && mounted) {
        setState(() {
          _showSuffixIcon = true;
        });
      }
    }
  }

  void _clearInput() {
    widget.controller.clear();
    widget.searchMessageBloc.add(SearchTextMessageChanged(
      text: "",
      roomId: widget.roomId,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: const BoxDecoration(
        color: AppColors.system0,
        border: Border(
          bottom: BorderSide(
            width: 1,
            color: AppColors.system2,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.system1,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  width: 1,
                  color: _focusState ? AppColors.blue : AppColors.system1,
                ),
              ),
              child: TextField(
                controller: widget.controller,
                maxLines: 1,
                minLines: 1,
                style: CustomStyle.medium.regular
                    .copyWith(color: AppColors.system8),
                cursorColor: AppColors.blue,
                focusNode: _focus,
                onTapOutside: (event) => Utils.hideKeyboard(),
                onChanged: (value) =>
                    widget.searchMessageBloc.add(SearchTextMessageChanged(
                  text: value,
                  roomId: widget.roomId,
                )),
                decoration: InputDecoration(
                  hintText: context.localization.search,
                  isDense: true,
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  label: null,
                  hintStyle: CustomStyle.medium.regular
                      .copyWith(color: AppColors.system5),
                  border: InputBorder.none,
                  prefixIcon: Container(
                    height: 24,
                    width: 16,
                    alignment: Alignment.center,
                    child: SvgPicture.asset(AppIcons.iconDefaultSearch),
                  ),
                  suffixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (_showSuffixIcon) ...[
                        IconButton(
                          iconSize: 20,
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          icon: const Icon(
                            Icons.close,
                            color: AppColors.neutral80,
                            size: 20,
                          ),
                          onPressed: () => {_clearInput()},
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
          TextInkwell(
            text: context.localization.cancel,
            color: AppColors.blue,
            style: CustomStyle.medium.medium,
            onTap: () {
              setState(() {
                widget.cancelUpdateCallback.call();
              });
              _clearInput();
              _focus.unfocus();
            },
          ).paddingOnly(left: 8),
        ],
      ),
    );
  }
}
