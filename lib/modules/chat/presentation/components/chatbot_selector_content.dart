import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_cupertino_action_sheet.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';
import 'package:saymee/modules/app/presentation/blocs/chatbot_list/chatbot_list_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/chatbot_list/chatbot_list_state.dart';

class ChatbotSelectorContent extends StatelessWidget {
  const ChatbotSelectorContent({super.key, required this.onSelected});

  final void Function(ChatbotModel chatbot) onSelected;

  Widget _cellChatbotCard(
    BuildContext context, {
    required ChatbotModel chatbot,
  }) {
    return AppCupertinoActionSheetAction(
      onPressed: () {
        Modular.to.pop();
        onSelected.call(chatbot);
      },
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(30),
            child: CachedNetworkImage(
              imageUrl: Utils.convertImageUrl(chatbot.smallAvatar),
              width: 60,
              height: 60,
              fit: BoxFit.contain,
              placeholder: (context, url) => Container(),
              errorWidget: (context, error, stackTrace) {
                return Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppColors.system3,
                    borderRadius: BorderRadius.circular(30),
                  ),
                );
              },
            ),
          ),
          12.horizontalSpace,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  chatbot.name ?? '',
                  style: CustomStyle.large.regular
                      .copyWith(color: AppColors.system8),
                ),
                if (chatbot.description != null)
                  Text(
                    chatbot.description ?? '',
                    textAlign: TextAlign.start,
                    style: CustomStyle.medium.regular
                        .copyWith(color: AppColors.system5),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatbotListBloc, ChatbotListState>(
      builder: (context, state) {
        final chatbots = state.chatbots ?? [];
        return AppCupertinoActionSheet(
          title: Text(
            context.localization.selectChatbot,
            style: CustomStyle.large.smb.copyWith(color: AppColors.system8),
          ),
          actions: [
            ...chatbots.map(
              (e) => _cellChatbotCard(context, chatbot: e),
            ),
          ],
          cancelButton: AppCupertinoButton(
            padding: const EdgeInsets.all(12),
            child: Text(
              context.localization.cancel,
              style: CustomStyle.large.smb.copyWith(color: AppColors.system6),
            ),
            onPressed: () {
              Modular.to.pop();
            },
          ),
        );
      },
    );
  }
}
