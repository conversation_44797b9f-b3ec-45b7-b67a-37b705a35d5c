import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/custom_markdown/flutter_markdown.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:markdown/markdown.dart' as md;

class CellChatMessageItem extends StatelessWidget {
  // ---- params ----
  final bool isMine;
  final MessageModel message;
  final RoomModel? room;
  final bool isShowTime;
  final bool isShowUsername;
  final bool isShowTimeTitle;
  final RegExp? regex;
  // constructor
  const CellChatMessageItem({
    super.key,
    required this.message,
    this.isMine = false,
    required this.room,
    this.isShowTime = false,
    this.isShowUsername = false,
    this.isShowTimeTitle = false,
    this.regex,
  });

  // ---- methods ----

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (message.time != null && isShowTimeTitle)
          Container(
            padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
            decoration: BoxDecoration(
              color: AppColors.system3,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              Utils.getDateLabel(message.time!.toLocal()),
              style:
                  CustomStyle.small.regular.copyWith(color: AppColors.system4),
            ),
          ).paddingOnly(bottom: 8),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment:
                    isMine ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (!isMine)
                        (isShowUsername
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(18),
                                    child: Container(
                                      color: Colors.white,
                                      child: CachedNetworkImage(
                                        imageUrl: Utils.convertImageUrl(
                                            message.userId == null
                                                ? room?.chatbot?.smallAvatar
                                                : message.user?.avatar),
                                        width: 36,
                                        height: 36,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) =>
                                            Container(),
                                        errorWidget:
                                            (context, error, stackTrace) {
                                          if (message.userId == null) {
                                            return SvgPicture.asset(
                                                AppIcons.iconDefaultBot);
                                          }

                                          return Image.asset(
                                            width: 36,
                                            height: 36,
                                            AppImages.imageDefaultAvatar,
                                          );
                                        },
                                      ),
                                    ),
                                  )
                                : const SizedBox(width: 36))
                            .paddingOnly(right: 8),
                      Flexible(
                        child: Column(
                          crossAxisAlignment: isMine
                              ? CrossAxisAlignment.end
                              : CrossAxisAlignment.start,
                          children: [
                            if (message.image != null && message.userId != null)
                              ClipRRect(
                                borderRadius: BorderRadius.circular(16),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: CachedNetworkImage(
                                    imageUrl:
                                        Utils.convertImageUrl(message.image!),
                                    width: 150,
                                    fit: BoxFit.cover,
                                    errorWidget: (context, url, error) {
                                      return Container();
                                    },
                                  ),
                                ),
                              )
                                  .inkwell(
                                    () => ChatModuleHelper.goToPhotoViewPage(
                                        url: message.image!),
                                    force: true,
                                    borderRadius: 16,
                                  )
                                  .paddingOnly(bottom: 8),
                            Container(
                              constraints: const BoxConstraints(minWidth: 80),
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                              decoration: BoxDecoration(
                                color:
                                    isMine ? AppColors.blue : AppColors.system0,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (!isMine && isShowUsername)
                                    Text(
                                      message.userId == null
                                          ? (room?.chatbot?.name ?? '')
                                          : (message.user?.fullname ??
                                              (message.user?.username ?? '')),
                                      style: CustomStyle.small.regular.copyWith(
                                          color: !isMine
                                              ? AppColors.system4
                                              : AppColors.system3),
                                    ),
                                  SelectionArea(
                                    child: MarkdownBody(
                                      data: message.content ?? '',
                                      builders: {
                                        'mention':
                                            FullNameHighlighter(isMine: isMine),
                                      },
                                      extensionSet: md.ExtensionSet(
                                        md.ExtensionSet.gitHubWeb.blockSyntaxes,
                                        [
                                          ...md.ExtensionSet.gitHubWeb
                                              .inlineSyntaxes, // Spread original list
                                          if (regex != null)
                                            MentionSyntax(regex!),
                                        ],
                                      ),
                                      onTapLink: (text, href, title) {
                                        Utils.launchURL(urlString: text);
                                      },
                                      //selectable: true,
                                      styleSheet: MarkdownStyleSheet(
                                        p: CustomStyle.medium.regular.copyWith(
                                          color: isMine
                                              ? AppColors.system0
                                              : AppColors.system8,
                                          fontFamily: Platform.isIOS
                                              ? '.SF UI Display'
                                              : null,
                                        ), // Adjust as needed
                                        a: CustomStyle.medium.regular.copyWith(
                                          decoration: TextDecoration.underline,
                                          decorationColor: AppColors.system0,
                                          color: isMine
                                              ? Colors.white
                                              : AppColors.blue,
                                          fontFamily: Platform.isIOS
                                              ? '.SF UI Display'
                                              : null,
                                        ),
                                        listBullet: CustomStyle.medium.bold
                                            .copyWith(
                                                color: isMine
                                                    ? AppColors.system0
                                                    : AppColors.system8),
                                      ),
                                    ),
                                  ),
                                  if (message.time != null && isShowTime)
                                    Text(
                                      DateFormat('HH:mm')
                                          .format(message.time!.toLocal()),
                                      style: CustomStyle.small.regular.copyWith(
                                          color: !isMine
                                              ? AppColors.system4
                                              : AppColors.system3),
                                    ),
                                  if (!isMine &&
                                      message.userId == null &&
                                      message.buttons != null &&
                                      message.buttons!.isNotEmpty)
                                    Wrap(
                                      spacing: 8,
                                      runSpacing: 8,
                                      children: [
                                        ...message.buttons!.map(
                                          (e) => Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              AppButton(
                                                buttonTitle: e.title ?? '',
                                                outline: true,
                                                backgroundColor:
                                                    AppColors.system0,
                                                borderColor: AppColors.system3,
                                                buttonColor: AppColors.system6,
                                                buttonSize: ButtonSize.small,
                                                borderRadius: 40,
                                                onPressed: () {
                                                  Utils.launchURL(
                                                      urlString: e.link ?? '');
                                                },
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ).paddingOnly(top: 10),
                                ],
                              ),
                            ),
                            if (message.image != null && message.userId == null)
                              ClipRRect(
                                borderRadius: BorderRadius.circular(16),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: CachedNetworkImage(
                                    imageUrl:
                                        Utils.convertImageUrl(message.image!),
                                    width: 150,
                                    fit: BoxFit.cover,
                                    errorWidget: (context, url, error) {
                                      return Container();
                                    },
                                  ),
                                ),
                              )
                                  .inkwell(
                                    () => ChatModuleHelper.goToPhotoViewPage(
                                        url: message.image!),
                                    force: true,
                                    borderRadius: 16,
                                  )
                                  .paddingOnly(top: 8),
                          ],
                        ),
                      ),
                    ],
                  ),
                  16.verticalSpace,
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class MentionSyntax extends md.InlineSyntax {
  MentionSyntax(RegExp pattern) : super(pattern.pattern);

  @override
  bool onMatch(md.InlineParser parser, Match match) {
    final mentionText = match[0]!; // Entire match
    final element = md.Element.text('mention', mentionText);
    parser.addNode(element);
    return true;
  }
}

class FullNameHighlighter extends MarkdownElementBuilder {
  final bool isMine;

  FullNameHighlighter({required this.isMine});

  @override
  Widget? visitElementAfterWithContext(BuildContext context, md.Element element,
      TextStyle? preferredStyle, TextStyle? parentStyle) {
    return Text.rich(
      TextSpan(
        text: element.textContent,
        style: TextStyle(
          color: isMine ? Colors.white : Colors.black, // Mention text color
          fontWeight: FontWeight.bold, // Bold style
          fontSize: 14, // Font size for mention
          fontFamily: Platform.isIOS ? '.SF UI Display' : null,
        ),
      ),
    );
  }
}
