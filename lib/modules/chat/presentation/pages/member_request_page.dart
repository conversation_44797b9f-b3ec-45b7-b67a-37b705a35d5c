import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/member_model.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_state.dart';

class MemberRequestPage extends StatelessWidget {
  const MemberRequestPage({super.key, required this.roomInfoBloc});

  final RoomInfoBloc roomInfoBloc;

  Widget _memberCard(
    BuildContext context, {
    required MemberModel member,
  }) {
    if (member.status != MemberStatus.requested) {
      return Container();
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: const BoxDecoration(
        color: AppColors.system0,
        border: Border(
          top: BorderSide(width: 1, color: AppColors.system2),
        ),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CachedNetworkImage(
              imageUrl: Utils.convertImageUrl(member.user?.avatar),
              width: 24,
              height: 24,
              fit: BoxFit.cover,
              errorWidget: (context, error, stackTrace) {
                return Image.asset(
                  width: 24,
                  height: 24,
                  AppImages.imageDefaultAvatar,
                );
              },
            ),
          ),
          8.horizontalSpace,
          Expanded(
            child: Text(
              (member.user?.fullname ?? (member.user?.username ?? '')),
              style:
                  CustomStyle.large.regular.copyWith(color: AppColors.system8),
            ),
          ),
          8.horizontalSpace,
          AppButton(
            buttonTitle: 'Approve',
            backgroundColor: AppColors.green,
            buttonSize: ButtonSize.small,
            paddingX: 16,
            onPressed: () {
              roomInfoBloc.add(
                  RoomAcceptMemberRequested(memberId: member.memberId ?? -1));
            },
          ),
          8.horizontalSpace,
          AppButton(
            buttonTitle: 'Deny',
            backgroundColor: AppColors.error,
            buttonSize: ButtonSize.small,
            paddingX: 16,
            onPressed: () {
              roomInfoBloc.add(
                  RoomRejectMemberRequested(memberId: member.memberId ?? -1));
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<RoomInfoBloc>.value(value: roomInfoBloc),
      ],
      child: Scaffold(
        backgroundColor: AppColors.system1,
        appBar: AppTopBar(
          title: context.localization.memberRequest,
          leading: IconInkwell(
            path: AppIcons.iconChevronLeft,
            color: AppColors.system5,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            onTap: () {
              Modular.to.pop();
            },
          ),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            child: BlocBuilder<RoomInfoBloc, RoomInfoState>(
              builder: (context, state) {
                final members = state.room?.members ?? [];
                return Column(
                  children: [
                    ...members.map((e) => _memberCard(context, member: e)),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
