import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:image_picker/image_picker.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/inputs/app_text_field.dart';
import 'package:saymee/core/components/text_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_configs.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_validator_type.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/data/models/media_model.dart';
import 'package:saymee/modules/app/data/repositories/app_repository.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_state.dart';

class UpdateGroupPage extends StatefulWidget {
  const UpdateGroupPage({super.key, required this.roomInfoBloc});

  final RoomInfoBloc roomInfoBloc;

  @override
  State<UpdateGroupPage> createState() => _UpdateGroupPageState();
}

class _UpdateGroupPageState extends State<UpdateGroupPage> {
  late TextEditingController editingController;
  final _imagePicker = ImagePicker();
  String? _imageCode;
  XFile? _xfile;
  GlobalKey globalKey = GlobalKey();
  final _formKey = GlobalKey<FormState>();

  final appRepository = Modular.get<AppRepository>();
  final chatRepository = Modular.get<ChatRepository>();

  @override
  void initState() {
    editingController =
        TextEditingController(text: widget.roomInfoBloc.state.room?.name ?? '');
    super.initState();
  }

  @override
  void dispose() {
    editingController.dispose();
    super.dispose();
  }

  Future<List<int>?> _capturePng() async {
    try {
      RenderRepaintBoundary boundary =
          globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      List<int> pngBytes = byteData!.buffer.asUint8List().toList();

      Utils.debugLogSuccess(pngBytes);
      return pngBytes;
    } catch (e) {
      return null;
    }
  }

  void _uploadImage() async {
    if (_formKey.currentState!.validate()) {
      AppLoading.show(
        context: context,
        title: '${context.localization.updating}...',
      );

      if (_xfile != null) {
        //upload file image

        final uploadResult =
            await appRepository.uploadFile(paths: [_xfile!.path]);

        uploadResult.fold((l) {
          if (mounted) {
            AppLoading.turnOff(context: context);
            AppDialog.showErrorDialog(context: context, error: l.reason);
            return;
          }
        }, (r) {
          List<MediaModel> medias = r['medias'];
          if (medias.isEmpty) {
            AppLoading.turnOff(context: context);
            AppDialog.showErrorDialog(
              context: context,
              error: context.localization.pleaseTryAgain,
            );
            return;
          } else {
            _updateRoom(medias.first.relativePath ?? '');
          }
        });
      } else if (_imageCode != null) {
        //upload local image

        final pngBytes = await _capturePng();

        if (pngBytes == null) {
          if (mounted) {
            AppLoading.turnOff(context: context);
          }
          return;
        } else {
          final uploadResult =
              await appRepository.uploadFileWithBytes(bytes: pngBytes);

          uploadResult.fold((l) {
            if (mounted) {
              AppLoading.turnOff(context: context);
              AppDialog.showErrorDialog(context: context, error: l.reason);
              return;
            }
          }, (r) {
            List<MediaModel> medias = r['medias'];
            if (medias.isEmpty) {
              AppLoading.turnOff(context: context);
              return;
            } else {
              _updateRoom(medias.first.relativePath ?? '');
            }
          });
        }
      } else {
        _updateRoom(null);
      }
    }
  }

  void _updateRoom(String? path) async {
    final initRoom = widget.roomInfoBloc.state.room;
    final result = await chatRepository.updateRoom(
      roomId: initRoom?.roomId ?? -1,
      name: editingController.text.trim(),
      logo: path,
    );

    if (mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      widget.roomInfoBloc.add(RoomGetInfoRequested(
          roomId: widget.roomInfoBloc.state.room?.roomId ?? -1));
      ChatModuleHelper.getRoomList();
      Modular.to.pop();
    });
  }

  Widget _defaultAvatar(BuildContext context) {
    return Stack(
      children: [
        _avatar(context),
        if (_imageCode != null || _xfile != null)
          Positioned(
            top: 0,
            right: 0,
            child: InkWell(
              onTap: () {
                setState(() {
                  _imageCode = null;
                  _xfile = null;
                });
              },
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: const BoxDecoration(
                  color: AppColors.green,
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: Icon(
                    Icons.close,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _avatar(BuildContext context) {
    if (_imageCode != null) {
      return RepaintBoundary(
        key: globalKey,
        child: Container(
          width: 100,
          height: 100,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: [
                Color(0xFFD7E4FB), // #D7E4FB
                Color(0xFFF9DEEC), // #F9DEEC
              ],
              stops: [0.1175, 0.8976], // 11.75% and 89.76%
            ),
          ),
          child: Center(
            child: Text(
              _imageCode!,
              style: const TextStyle(
                fontSize: 50,
                fontWeight: FontWeight.w500,
                height: 27 / 50,
                color: Colors.white,
              ),
            ),
          ),
        ),
      );
    }

    if (_xfile != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(50),
        child: Image.file(
          File(_xfile!.path),
          width: 100,
          height: 100,
          fit: BoxFit.contain,
        ),
      );
    }

    return BlocBuilder<RoomInfoBloc, RoomInfoState>(
      builder: (context, state) {
        final room = state.room;
        return ClipRRect(
          borderRadius: BorderRadius.circular(50),
          child: CachedNetworkImage(
            imageUrl:
                Utils.convertImageUrl(room?.logo ?? room?.chatbot?.smallAvatar),
            width: 100,
            height: 100,
            fit: BoxFit.contain,
            placeholder: (context, url) => Container(),
            errorWidget: (context, error, stackTrace) {
              return Container(
                width: 100,
                height: 100,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: [
                      Color(0xFFD7E4FB), // #D7E4FB
                      Color(0xFFF9DEEC), // #F9DEEC
                    ],
                    stops: [0.1175, 0.8976], // 11.75% and 89.76%
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget exampleAvatar(String code) {
    return Container(
      width: 70,
      height: 70,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            Color(0xFFD7E4FB), // #D7E4FB
            Color(0xFFF9DEEC), // #F9DEEC
          ],
          stops: [0.1175, 0.8976], // 11.75% and 89.76%
        ),
      ),
      child: Center(
        child: Text(
          code,
          style: const TextStyle(
            fontSize: 40,
            fontWeight: FontWeight.w500,
            height: 19 / 40,
            color: Colors.white,
          ),
        ),
      ),
    ).inkwell(
      () {
        setState(() {
          _imageCode = code;
          _xfile = null;
        });
      },
      force: true,
      borderRadius: (MediaQuery.of(context).size.width - 16 * 5) / 8,
    ).paddingOnly(right: 16);
  }

  void _handleImageFile(XFile? image) async {
    if (image != null) {
      final size = await image.length();
      if (size > 20 * 1024 * 1024) {
        if (mounted) {
          AppDialog.showErrorDialog(
            context: context,
            error: AppConfigs.maxFileSizeError,
          );
        }
      } else {
        setState(() {
          _xfile = image;
          _imageCode = null;
        });
      }
    }
  }

  void _handlePickImage() async {
    final XFile? image = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      requestFullMetadata: false,
    );
    _handleImageFile(image);
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<RoomInfoBloc>.value(
          value: widget.roomInfoBloc,
        ),
      ],
      child: Scaffold(
        backgroundColor: AppColors.system1,
        appBar: AppTopBar(
          title: context.localization.createGroup,
          leading: TextInkwell(
            text: context.localization.cancel,
            color: AppColors.blue,
            style: CustomStyle.large.medium,
            padding: const EdgeInsets.only(left: 16),
            onTap: () {
              Modular.to.pop();
            },
          ),
          actions: [
            TextInkwell(
              text: context.localization.done,
              color: AppColors.blue,
              style: CustomStyle.large.medium,
              padding: const EdgeInsets.only(right: 16),
              onTap: _uploadImage,
            ),
          ],
        ),
        body: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                24.verticalSpace,
                _defaultAvatar(context),
                12.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppButton(
                      prefixIconPath: AppIcons.iconUpload,
                      paddingX: 12,
                      buttonGap: 8,
                      buttonTitle: context.localization.uploadImage,
                      onPressed: _handlePickImage,
                    ),
                  ],
                ).paddingSymmetric(h: 16),
                24.verticalSpace,
                Text(
                  'Set emoji as your group avatar',
                  style: CustomStyle.medium.medium
                      .copyWith(color: AppColors.system8),
                ).paddingSymmetric(h: 16),
                16.verticalSpace,
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      16.horizontalSpace,
                      exampleAvatar("💎"),
                      exampleAvatar("😍"),
                      exampleAvatar("😄"),
                      exampleAvatar("🚀"),
                      exampleAvatar("🎸"),
                      exampleAvatar("👍"),
                      exampleAvatar("🥰"),
                      exampleAvatar("⚽"),
                      exampleAvatar("🎾"),
                      exampleAvatar("🏋️‍♀️"),
                      exampleAvatar("🎬"),
                      exampleAvatar("🎉"),
                      exampleAvatar("️‍🔥"),
                      exampleAvatar("🎄"),
                      exampleAvatar("👑"),
                      exampleAvatar("🐳"),
                    ],
                  ),
                ),
                24.verticalSpace,
                AppTextField(
                  label: context.localization.nameGroupChat,
                  prefixIcon: AppIcons.iconGroup,
                  hintText: context.localization.inputNameGroupChat,
                  controller: editingController,
                  validatorType: AppTextFieldValidatorType.groupName,
                  formKey: _formKey,
                ).paddingSymmetric(h: 16),
                24.verticalSpace,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
