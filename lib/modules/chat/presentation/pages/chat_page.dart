import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_state.dart';
import 'package:saymee/modules/chat/presentation/components/cell_chat_item.dart';
import 'package:saymee/modules/chat/presentation/components/chatbot_selector_content.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';

class ChatPage extends StatelessWidget {
  const ChatPage({super.key});

  void _selectChatbot(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return ChatbotSelectorContent(
          onSelected: (chatbot) {
            _createChatbotRoom(context, chatbot: chatbot);
          },
        );
      },
    );
  }

  void _createChatbotRoom(
    BuildContext context, {
    required ChatbotModel chatbot,
  }) async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(context: context);
    final result = await chatRepository.createRoom(
      chatbotId: chatbot.chatbotId ?? -1,
      public: 0,
    );
    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      RoomModel? room = r['room'];
      if (room != null) {
        room = room.copyWith(chatbot: chatbot);
        ChatModuleHelper.goToChatBotPage(
          roomId: room.roomId ?? -1,
          type: RoomType.single,
        );
        ChatModuleHelper.getRoomList();
      }
    });
  }

  Widget emptyView(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        children: [
          64.verticalSpace,
          Image.asset(AppImages.imageChat),
          32.verticalSpace,
          Text(
            context.localization.yourPersonalFriend,
            textAlign: TextAlign.center,
            style: CustomStyle.h3.smb.copyWith(color: AppColors.system9),
          ),
          4.verticalSpace,
          Text(
            context.localization.yourPersonalFriendContent,
            textAlign: TextAlign.center,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system6),
          ),
          12.verticalSpace,
          AppButton(
            buttonTitle: context.localization.startConversation,
            boxShadow: const [
              BoxShadow(
                color: Color(0x0D000000), // Shadow color with 13% opacity
                offset:
                    Offset(0, 14), // Horizontal and vertical shadow position
                blurRadius: 14, // Blur radius
                spreadRadius: 0, // Spread radius
              ),
            ],
            onPressed: () => _selectChatbot(context),
          ),
        ],
      ).paddingOnly(left: 16, right: 16, bottom: 16),
    );
  }

  Widget listRoom() {
    return BlocBuilder<RoomListBloc, RoomListState>(
      builder: (context, state) {
        if (state.isRequesting) {
          return const Center(
            child: CircularProgressIndicator(
              color: AppColors.blue,
            ),
          ).paddingSymmetric(v: 16);
        }

        if (state.oneToOneRooms == null || state.oneToOneRooms!.isEmpty) {
          return emptyView(context);
        }

        final oneToOneRooms = state.oneToOneRooms!.reversed.toList();
        return ListView.builder(
          itemCount: oneToOneRooms.length,
          itemBuilder: (context, index) => CellChatItem(
            room: oneToOneRooms[index],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.system1,
      appBar: AppTopBar(
        title: context.localization.chats,
      ),
      body: SafeArea(
        child: RefreshIndicator(
          backgroundColor: AppColors.system0,
          color: AppColors.blue,
          onRefresh: () async {
            await AppModuleHelper.getCommonDataWithRefresh();
            await UserModuleHelper.getUserInfoWithRefresh();
          },
          child: SizedBox(
            height: double.infinity,
            child: listRoom(),
          ),
        ),
      ),
    );
  }
}
