import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';

class JoinGroupPage extends StatefulWidget {
  const JoinGroupPage({super.key, required this.room});

  final RoomModel room;

  @override
  State<JoinGroupPage> createState() => _JoinGroupPageState();
}

class _JoinGroupPageState extends State<JoinGroupPage> {
  late bool isRequested;

  @override
  void initState() {
    isRequested = (widget.room.myMember != null &&
        widget.room.myMember?.status == MemberStatus.requested);
    super.initState();
  }

  void _joinGroup(BuildContext context) async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(
        context: context, title: '${context.localization.requesting}...');

    final result = await chatRepository.join(roomId: widget.room.roomId ?? -1);

    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      setState(() {
        isRequested = true;
      });
      if (widget.room.public == 1) {
        ChatModuleHelper.pushReplaceChatBotPage(
          roomId: widget.room.roomId ?? -1,
          type: RoomType.group,
        );
      }

      ChatModuleHelper.getRoomList();
    });
  }

  void _acceptInvite(BuildContext context) async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(
        context: context, title: '${context.localization.joining}...');

    final result =
        await chatRepository.acceptInvite(roomId: widget.room.roomId ?? -1);

    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      ChatModuleHelper.pushReplaceChatBotPage(
        roomId: widget.room.roomId ?? -1,
        type: RoomType.group,
      );

      ChatModuleHelper.getRoomList();
    });
  }

  void _rejectInvite(BuildContext context) async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(
        context: context, title: '${context.localization.rejecting}...');

    final result =
        await chatRepository.rejectInvite(roomId: widget.room.roomId ?? -1);

    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      Modular.to.pop();
    });
  }

  Widget bottomWidget(BuildContext context) {
    if (widget.room.myMember != null &&
        widget.room.myMember?.status == MemberStatus.invited) {
      Row(
        children: [
          Expanded(
            child: AppButton(
              buttonTitle: context.localization.deny,
              backgroundColor: AppColors.error,
              onPressed: () => _rejectInvite(context),
            ),
          ),
          8.horizontalSpace,
          Expanded(
            child: AppButton(
              buttonTitle: context.localization.accept,
              backgroundColor: AppColors.green,
              onPressed: () => _acceptInvite(context),
            ),
          ),
        ],
      );
    }

    return AppButton(
      buttonTitle: isRequested
          ? context.localization.requested
          : context.localization.joinTheGroup,
      disabled: isRequested,
      onPressed: () => _joinGroup(context),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.system1,
      appBar: AppTopBar(
        title: widget.room.myMember != null
            ? context.localization.groupInvitation
            : widget.room.name,
        subTitle: '${widget.room.memberCount} ${context.localization.members}',
        subTitleColor: AppColors.system6,
        leading: IconInkwell(
          path: AppIcons.iconChevronLeft,
          color: AppColors.system5,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          onTap: () {
            Modular.to.pop();
          },
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  16.verticalSpace,
                  Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColors.system0,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CachedNetworkImage(
                          imageUrl: Utils.convertImageUrl(widget.room.logo ??
                              widget.room.chatbot?.smallAvatar),
                          width: 100,
                          height: 100,
                          fit: BoxFit.contain,
                          placeholder: (context, url) => Container(),
                          errorWidget: (context, error, stackTrace) {
                            return Container(
                              width: 100,
                              height: 100,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topRight,
                                  end: Alignment.bottomLeft,
                                  colors: [
                                    Color(0xFFD7E4FB), // #D7E4FB
                                    Color(0xFFF9DEEC), // #F9DEEC
                                  ],
                                  stops: [0.1175, 0.8976], // 11.75% and 89.76%
                                ),
                              ),
                            );
                          },
                        ),
                        10.verticalSpace,
                        Text(
                          widget.room.description ?? '',
                          style: CustomStyle.medium.regular
                              .copyWith(color: AppColors.system8),
                        ),
                      ],
                    ),
                  ),
                ],
              ).paddingSymmetric(h: 16),
            ),
          ),
          Container(
            padding: EdgeInsets.only(
              top: 16,
              left: 16,
              right: 16,
              bottom: 16 + MediaQuery.of(context).viewPadding.bottom,
            ),
            decoration: BoxDecoration(
              color: AppColors.system0,
              border: const Border(
                top: BorderSide(width: 1, color: AppColors.system3),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 5,
                  blurRadius: 5,
                  offset: const Offset(0, 2), // changes position of shadow
                ),
              ],
            ),
            child: bottomWidget(context),
          ),
        ],
      ),
    );
  }
}
