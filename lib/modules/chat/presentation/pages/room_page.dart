import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_state.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_room/search_room_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_room/search_room_state.dart';
import 'package:saymee/modules/chat/presentation/components/cell_group_item.dart';
import 'package:saymee/modules/chat/presentation/components/chatbot_selector_content.dart';
import 'package:saymee/modules/chat/presentation/components/search_room_field.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';

class RoomPage extends StatefulWidget {
  const RoomPage({super.key});

  @override
  State<RoomPage> createState() => _RoomPageState();
}

class _RoomPageState extends State<RoomPage> {
  final TextEditingController editingController = TextEditingController();
  bool isShowCancel = false;
  final searchRoomBloc = Modular.get<SearchRoomBloc>();

  @override
  void dispose() {
    editingController.dispose();
    super.dispose();
  }

  Widget emptyView(BuildContext context) {
    return Column(
      children: [
        32.verticalSpace,
        Image.asset(AppImages.imageGroup),
        12.verticalSpace,
        Text(
          context.localization.roomIntro,
          textAlign: TextAlign.center,
          style: CustomStyle.h3.smb.copyWith(color: AppColors.system9),
        ),
        4.verticalSpace,
        Text(
          context.localization.roomIntroContent,
          textAlign: TextAlign.center,
          style: CustomStyle.medium.regular.copyWith(color: AppColors.system6),
        ),
        24.verticalSpace,
        AppButton(
          buttonTitle: context.localization.createNewGroup,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0D000000), // Shadow color with 13% opacity
              offset: Offset(0, 14), // Horizontal and vertical shadow position
              blurRadius: 14, // Blur radius
              spreadRadius: 0, // Spread radius
            ),
          ],
          onPressed: () => _selectChatbot(context),
        ),
      ],
    ).paddingOnly(bottom: 16, left: 16, right: 16);
  }

  void _selectChatbot(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return ChatbotSelectorContent(
          onSelected: (chatbot) =>
              ChatModuleHelper.goToCreateRoomPage(chatbot: chatbot),
        );
      },
    );
  }

  Widget roomsView(BuildContext context) {
    return BlocBuilder<RoomListBloc, RoomListState>(
      builder: (context, state) {
        if (state.isRequesting) {
          return const Center(
            child: CircularProgressIndicator(
              color: AppColors.blue,
            ),
          ).paddingSymmetric(v: 16);
        }

        if (state.groupRooms == null || state.groupRooms!.isEmpty) {
          return emptyView(context);
        }

        final groupRooms = state.groupRooms!;
        return Column(
          children: [
            ...groupRooms.map((room) => CellGroupItem(room: room)),
          ],
        ).paddingOnly(bottom: 16);
      },
    );
  }

  Widget _searchRoomResult(BuildContext context) {
    return BlocBuilder<SearchRoomBloc, SearchRoomState>(
      builder: (context, state) {
        if (state is SearchRoomLoading) {
          return const Center(
            child: CircularProgressIndicator(
              color: Colors.blue,
            ),
          );
        }

        if (state is SearchRoomError) {
          return Center(
            child: Text(
              state.message,
              style:
                  CustomStyle.large.regular.copyWith(color: AppColors.system8),
            ),
          );
        }

        if (state is SearchRoomLoaded) {
          return Column(
            children: [
              ...state.rooms.map((room) => CellGroupItem(
                    room: room,
                    isShowLastMessage: false,
                  )),
            ],
          ).paddingOnly(bottom: 16);
        }

        return Container();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<SearchRoomBloc>.value(
          value: searchRoomBloc,
        ),
      ],
      child: Scaffold(
        backgroundColor: AppColors.system1,
        appBar: AppTopBar(
          title: context.localization.chatRoom,
          showShadow: false,
          actions: [
            IconInkwell(
              path: AppIcons.iconAdd,
              color: AppColors.system5,
              padding: const EdgeInsets.only(right: 16, left: 4),
              onTap: () => _selectChatbot(context),
            ),
          ],
        ),
        body: SizedBox(
          height: double.infinity,
          child: Column(
            children: [
              SearchRoomField(
                controller: editingController,
                cancelUpdateCallback: (isCancel) {
                  setState(() {
                    isShowCancel = isCancel;
                  });
                },
                searchRoomBloc: searchRoomBloc,
              ),
              Expanded(
                child: RefreshIndicator(
                  backgroundColor: AppColors.system0,
                  color: AppColors.blue,
                  onRefresh: () async {
                    await AppModuleHelper.getCommonDataWithRefresh();
                    await UserModuleHelper.getUserInfoWithRefresh();
                  },
                  child: SizedBox(
                    height: double.infinity,
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Column(
                        children: [
                          if (!isShowCancel) roomsView(context),
                          if (isShowCancel) _searchRoomResult(context),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
