import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gal/gal.dart';
import 'package:saymee/core/components/app_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

class PhotoViewPage extends StatelessWidget {
  const PhotoViewPage({super.key, required this.url});

  final String url;

  void _saveImage(BuildContext context) async {
    try {
      final response = await Dio()
          .get(url, options: Options(responseType: ResponseType.bytes));

      if (response.statusCode == HttpStatus.ok) {
        Uint8List imageBytes = Uint8List.fromList(response.data);
        await Gal.putImageBytes(imageBytes);

        if (context.mounted) {
          Fluttertoast.showToast(
            msg: context.localization.imageDownloadedSuccess,
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.CENTER,
            backgroundColor: Colors.black.withOpacity(0.5),
            fontSize: 14,
          );
        }
      }
    } catch (e) {
      Utils.debugLog(e);
    }
  }

  void _share() async {
    try {
      String fileName = Uri.parse(url).pathSegments.last;
      final tempDir = await getTemporaryDirectory();
      final filePath = '${tempDir.path}/$fileName';
      await Dio().download(url, filePath);

      await Share.shareXFiles([XFile(filePath)]);
    } catch (e) {
      Utils.debugLog(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // Transparent status bar
        statusBarIconBrightness: Brightness.light, // Light status bar icons
        statusBarBrightness: Brightness.dark,
      ),
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AppInkwell(
                onTap: () => Modular.to.pop(),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                ).paddingAll(4),
              ).paddingSymmetric(v: 8, h: 16),
              Expanded(
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    Hero(
                      tag: 'hero-photo',
                      child: CachedNetworkImage(
                        imageUrl: Utils.convertImageUrl(url),
                        progressIndicatorBuilder:
                            (context, child, loadingProgress) {
                          return const Center(
                            child: CircularProgressIndicator(
                              color: AppColors.blue,
                            ),
                          );
                        },
                        errorWidget: (context, error, stackTrace) {
                          return Container();
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => _saveImage(context),
                      child: Column(
                        children: [
                          const Icon(
                            Icons.download,
                            color: AppColors.system0,
                          ),
                          Text(
                            context.localization.save,
                            style: CustomStyle.large.regular
                                .copyWith(color: AppColors.system0),
                          )
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: InkWell(
                      onTap: _share,
                      child: Column(
                        children: [
                          const Icon(
                            Icons.share,
                            color: AppColors.system0,
                          ),
                          Text(
                            context.localization.share,
                            style: CustomStyle.large.regular
                                .copyWith(color: AppColors.system0),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ).paddingAll(16),
            ],
          ),
        ),
      ),
    );
  }
}
