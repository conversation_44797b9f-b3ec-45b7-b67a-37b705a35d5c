import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_state.dart';
import 'package:saymee/modules/chat/presentation/components/cell_member_item.dart';

class GroupMemberPage extends StatelessWidget {
  const GroupMemberPage({super.key, required this.roomInfoBloc});

  final RoomInfoBloc roomInfoBloc;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<RoomInfoBloc>.value(value: roomInfoBloc),
      ],
      child: Scaffold(
        backgroundColor: AppColors.system1,
        appBar: AppTopBar(
          title: context.localization.groupMembers,
          leading: IconInkwell(
            path: AppIcons.iconChevronLeft,
            color: AppColors.system5,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            onTap: () {
              Modular.to.pop();
            },
          ),
        ),
        body: SafeArea(
          child: BlocBuilder<RoomInfoBloc, RoomInfoState>(
            builder: (context, state) {
              final members = state.room?.members ?? [];
              return ListView.builder(
                itemCount: members.length,
                itemBuilder: (context, index) => CellMemberItem(
                  member: members[index],
                  roomInfoBloc: roomInfoBloc,
                  roomId: state.room?.roomId ?? -1,
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
