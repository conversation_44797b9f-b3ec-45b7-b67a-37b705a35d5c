import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:saymee/core/components/app_cupertino_action_sheet.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_switcher.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/chat/presentation/blocs/message_list/message_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/message_list/message_list_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_state.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_overlay_cubit.dart';
import 'package:saymee/modules/chat/presentation/components/action_group.dart';
import 'package:saymee/modules/chat/presentation/components/group_introduction_editor.dart';
import 'package:saymee/modules/chat/presentation/components/group_setting_card.dart';
import 'package:share_plus/share_plus.dart';

class GroupSettingPage extends StatelessWidget {
  const GroupSettingPage({
    super.key,
    required this.roomId,
    required this.roomInfoBloc,
    required this.messageListBloc,
    required this.searchOverlayCubit,
  });

  final int roomId;
  final RoomInfoBloc roomInfoBloc;
  final MessageListBloc messageListBloc;
  final SearchOverlayCubit searchOverlayCubit;

  void _updatePublic(int public) {
    final chatRepository = Modular.get<ChatRepository>();
    chatRepository.updateRoom(
      roomId: roomId,
      public: public,
    );
  }

  void _updateNotification(int notification) {
    final chatRepository = Modular.get<ChatRepository>();
    chatRepository.setMemberNotification(roomId: roomId, onOff: notification);
    roomInfoBloc
        .add(RoomUpdateNotificationRequested(notification: notification));
    ChatModuleHelper.updateNotificationRoom(
        roomId: roomId, notification: notification);
  }

  void _updateDescription(
    BuildContext context, {
    required String description,
  }) async {
    final chatRepository = Modular.get<ChatRepository>();

    AppLoading.show(context: context);

    final result = await chatRepository.updateRoom(
      roomId: roomId,
      description: description,
    );

    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      Utils.showToast(context.localization.updateSuccess);
      roomInfoBloc
          .add(RoomUpdateDescriptionRequested(description: description));
    });
  }

  Future<void> _getRoomInfoWithRefresh() async {
    final block = roomInfoBloc.stream.first;
    roomInfoBloc.add(RoomGetInfoRequested(roomId: roomId));
    await block;
  }

  void _clearContext(BuildContext context) async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(context: context);

    final result = await chatRepository.clearContext(
      roomId: roomId,
    );

    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      Utils.showToast(context.localization.clearSuccess);
      messageListBloc.add(MessageClearContext());
    });
  }

  void _deleteGroup(BuildContext context) async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(context: context);

    final result = await chatRepository.deleteRoom(
      roomId: roomId,
    );

    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      final roomListBloc = Modular.get<RoomListBloc>();
      roomListBloc.add(RoomDeleteRequested(roomId: roomId));
      AppModuleHelper.navigateToMainPage();
    });
  }

  void _leaveGroup(BuildContext context) async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(context: context);

    final result = await chatRepository.leave(
      roomId: roomId,
    );

    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      final roomListBloc = Modular.get<RoomListBloc>();
      roomListBloc.add(RoomDeleteRequested(roomId: roomId));
      AppModuleHelper.navigateToMainPage();
    });
  }

  Widget _groupInfo(BuildContext context) {
    return BlocBuilder<RoomInfoBloc, RoomInfoState>(
      builder: (context, state) {
        RoomModel? roomData = state.room;
        return Column(
          children: [
            16.verticalSpace,
            ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: CachedNetworkImage(
                imageUrl: Utils.convertImageUrl(
                    roomData?.logo ?? roomData?.chatbot?.smallAvatar),
                width: 100,
                height: 100,
                fit: BoxFit.contain,
                placeholder: (context, url) => Container(),
                errorWidget: (context, error, stackTrace) {
                  return Container(
                    width: 100,
                    height: 100,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        colors: [
                          Color(0xFFD7E4FB), // #D7E4FB
                          Color(0xFFF9DEEC), // #F9DEEC
                        ],
                        stops: [0.1175, 0.8976], // 11.75% and 89.76%
                      ),
                    ),
                  );
                },
              ),
            ),
            8.verticalSpace,
            Text(
              roomData?.name ?? '',
              style: CustomStyle.h5.smb.copyWith(color: AppColors.system8),
            ),
            Text(
              roomData?.chatbot?.name ?? '',
              style:
                  CustomStyle.medium.regular.copyWith(color: AppColors.system5),
            ),
          ],
        );
      },
    ).paddingOnly(bottom: 24);
  }

  Widget _actionRow(BuildContext context) {
    return BlocBuilder<RoomInfoBloc, RoomInfoState>(
      builder: (context, state) {
        final notification = state.room?.myMember?.notification;
        return IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _actionItem(
                context,
                text: context.localization.search,
                iconPath: AppIcons.iconSearch,
                onTap: () {
                  Modular.to.pop();
                  searchOverlayCubit.updateValue(true);
                },
              ),
              8.horizontalSpace,
              if (notification != null)
                _actionItem(
                  context,
                  text: notification == 1
                      ? context.localization.mute
                      : context.localization.unmute,
                  iconPath: notification == 0
                      ? AppIcons.iconUnmute
                      : AppIcons.iconMute,
                  onTap: () {
                    _updateNotification(1 - notification);
                  },
                ),
              if (notification != null) 8.horizontalSpace,
              _actionItem(
                context,
                text: context.localization.clear,
                iconPath: AppIcons.iconClear,
                onTap: () {
                  ActionGroup.showConfirmGroup(
                    context,
                    image: AppImages.imageClearContext,
                    title: context.localization.clearMessage,
                    description: context.localization.clearMessageContent,
                    confirmCallback: () => _clearContext(context),
                  );
                },
              ),
              8.horizontalSpace,
              _actionItem(
                context,
                text: context.localization.leave,
                iconPath: AppIcons.iconLeave,
                textColor: AppColors.error,
                onTap: () {
                  ActionGroup.showConfirmGroup(
                    context,
                    image: AppImages.imageLeave,
                    title: context.localization.leaveGroup,
                    description: context.localization.leaveGroupContent,
                    confirmCallback: () => _leaveGroup(context),
                  );
                },
              ),
            ],
          ),
        );
      },
    ).paddingOnly(bottom: 8);
  }

  Widget _actionItem(
    BuildContext context, {
    required String text,
    required String iconPath,
    Color textColor = AppColors.blue,
    VoidCallback? onTap,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
        decoration: BoxDecoration(
          color: AppColors.system0,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(width: 1, color: AppColors.system3),
        ),
        child: Column(
          children: [
            SvgPicture.asset(iconPath),
            4.verticalSpace,
            Text(
              text,
              style: CustomStyle.small.regular.copyWith(color: textColor),
            ),
          ],
        ),
      ).inkwell(
        onTap,
        force: true,
        borderRadius: 16,
      ),
    );
  }

  Widget _inviteView(BuildContext context) {
    return BlocBuilder<RoomInfoBloc, RoomInfoState>(
      builder: (context, state) {
        final room = state.room;
        if (room == null) {
          return Container();
        }

        return GroupSettingCard(
          title: context.localization.groupMembers,
          list: [
            KeyGroupSetting(
              title: context.localization.groupMembers,
              description:
                  '${room.memberCount ?? 0} ${context.localization.members}',
              onTap: () {
                ChatModuleHelper.goToGroupMemberPage(
                    roomInfoBloc: roomInfoBloc);
              },
            ),
            if (room.link != null)
              KeyGroupSetting(
                title: context.localization.inviteLink,
                description: room.link ?? '',
                descriptionMaxLines: 1,
                descriptionColor: AppColors.blue,
                onTap: () => showInviteLink(context, link: room.link!),
              ),
          ],
        );
      },
    ).paddingOnly(bottom: 16);
  }

  Widget _actionCard(
    BuildContext context, {
    required String icon,
    required String title,
    required VoidCallback action,
  }) {
    return AppCupertinoActionSheetAction(
      onPressed: () {
        Modular.to.pop();
        action.call();
      },
      child: Row(
        children: [
          SvgPicture.asset(icon),
          12.horizontalSpace,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: CustomStyle.large.regular
                      .copyWith(color: AppColors.system8),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void showInviteLink(BuildContext context, {required String link}) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return AppCupertinoActionSheet(
          title: Column(
            children: [
              Text(
                'Invite link',
                style: CustomStyle.large.smb.copyWith(color: AppColors.system8),
              ),
              Text(
                'Share this link with your friends! They can tap it to join the chat directly, or search for the group using the link.',
                style: CustomStyle.medium.regular
                    .copyWith(color: AppColors.system8),
              ),
              Text(
                link,
                style:
                    CustomStyle.medium.regular.copyWith(color: AppColors.blue),
              ),
            ],
          ),
          actions: [
            _actionCard(
              context,
              icon: AppIcons.iconCopyLink,
              title: "Copy link",
              action: () async {
                await Clipboard.setData(ClipboardData(text: link));
                if (context.mounted) {
                  Utils.showToast(context.localization.copied);
                }
              },
            ),
            _actionCard(
              context,
              icon: AppIcons.iconShareLink,
              title: 'Share link',
              action: () {
                Share.shareUri(Uri.parse(link));
              },
            ),
          ],
          cancelButton: AppCupertinoButton(
            padding: const EdgeInsets.all(12),
            child: Text(
              context.localization.cancel,
              style: CustomStyle.large.smb.copyWith(color: AppColors.system6),
            ),
            onPressed: () {
              Modular.to.pop();
            },
          ),
        );
      },
    );
  }

  Widget _infoCard(BuildContext context) {
    return BlocBuilder<RoomInfoBloc, RoomInfoState>(
      builder: (context, state) {
        final room = state.room;
        if (room?.myMember?.role == MemberRole.admin) {
          return GroupSettingCard(
            title: context.localization.groupInfo,
            list: [
              KeyGroupSetting(
                title: context.localization.groupIntro,
                description: context.localization.writeDescription,
                onTap: () {
                  GroupIntroductionEditorSheet.showIntroEditorSheet(
                    context,
                    description: room?.description,
                    onSave: (result) {
                      _updateDescription(context, description: result);
                    },
                  );
                },
              ),
              KeyGroupSetting(
                title: context.localization.nameOrAvatarGroup,
                description: context.localization.updateNameOrAvatarGroup,
                onTap: () {
                  ChatModuleHelper.goToUpdateGroupPage(
                      roomInfoBloc: roomInfoBloc);
                },
              ),
              KeyGroupSetting(
                title: context.localization.setGroupPrivate,
                description: context.localization.setGroupPrivateContent,
                suffixWidget: AppSwitcher(
                  switchValue: room?.public == 0,
                  callback: (value) {
                    _updatePublic(value ? 0 : 1);
                  },
                ),
              ),
              KeyGroupSetting(
                title: context.localization.memberRequest,
                description: context.localization.memberRequestContent,
                onTap: () {
                  ChatModuleHelper.goToMemberRequestPage(
                      roomInfoBloc: roomInfoBloc);
                },
              ),
            ],
          ).paddingOnly(bottom: 16);
        }

        return Container();
      },
    );
  }

  Widget _manageCard(BuildContext context) {
    return BlocBuilder<RoomInfoBloc, RoomInfoState>(
      builder: (context, state) {
        if (state.room?.myMember?.role == MemberRole.admin) {
          return GroupSettingCard(
            title: context.localization.groupManage,
            list: [
              KeyGroupSetting(
                title: context.localization.deleteGroup,
                description: context.localization.deleteGroupContent,
                isShowRightIndicator: false,
                titleColor: AppColors.error,
                onTap: () {
                  ActionGroup.showConfirmGroup(
                    context,
                    image: AppImages.imageDelete,
                    title: context.localization.deleteGroup,
                    description: context.localization.confirmDeleteGroup,
                    confirmCallback: () => _deleteGroup(context),
                  );
                },
              ),
            ],
          ).paddingOnly(bottom: 16);
        }

        return Container();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<RoomInfoBloc>.value(value: roomInfoBloc),
        BlocProvider<MessageListBloc>.value(value: messageListBloc),
      ],
      child: Scaffold(
        backgroundColor: AppColors.system1,
        appBar: AppTopBar(
          title: context.localization.groupSetting,
          leading: IconInkwell(
            path: AppIcons.iconChevronLeft,
            color: AppColors.system5,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            onTap: () {
              Modular.to.pop();
            },
          ),
        ),
        body: SafeArea(
          child: RefreshIndicator(
            backgroundColor: AppColors.system0,
            color: AppColors.blue,
            onRefresh: _getRoomInfoWithRefresh,
            child: SizedBox(
              height: double.infinity,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    _groupInfo(context),
                    _actionRow(context),
                    _inviteView(context),
                    _infoCard(context),
                    _manageCard(context),
                  ],
                ).paddingSymmetric(h: 16),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
