import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:saymee/core/components/app_cupertino_action_sheet.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_inkwell.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/components/scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_configs.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_environment.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/helpers/web_socket_helper.dart';
import 'package:saymee/core/utils/ads_service.dart';
import 'package:saymee/core/utils/analytic_service.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/app/presentation/blocs/app_banner/app_banner_cubit.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_state.dart';
import 'package:saymee/modules/app/presentation/blocs/upload_file/upload_file_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/upload_file/upload_file_event.dart';
import 'package:saymee/modules/app/presentation/blocs/upload_file/upload_file_state.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/chat/presentation/blocs/message_list/message_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/message_list/message_list_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/message_list/message_list_state.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_state.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_message/search_message_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_message/search_message_state.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_overlay_cubit.dart';
import 'package:saymee/modules/chat/presentation/components/action_group.dart';
import 'package:saymee/modules/chat/presentation/components/cell_chat_message_item.dart';
import 'package:saymee/modules/chat/presentation/components/cell_search_message_item.dart';
import 'package:saymee/modules/chat/presentation/components/mentions/flutter_mentions.dart';
import 'package:saymee/modules/chat/presentation/components/image_upload_view.dart';
import 'package:saymee/modules/chat/presentation/components/search_message_field.dart';
import 'package:saymee/modules/chat/presentation/components/suggestion_template_view.dart';
import 'package:saymee/modules/subscription/general/subscription_module_helper.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_bloc.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_state.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

class ChatbotPage extends StatefulWidget {
  const ChatbotPage({
    super.key,
    required this.type,
    required this.roomId,
  });

  final RoomType type;
  final int roomId;

  @override
  State<ChatbotPage> createState() => _ChatbotPageState();
}

class _ChatbotPageState extends State<ChatbotPage> {
  final messageListBloc = Modular.get<MessageListBloc>();
  final uploadFileBloc = Modular.get<UploadFileBloc>();
  final webSocketHelper = WebSocketHelper();
  final userInfoBloc = Modular.get<UserInfoBloc>();
  final ItemScrollController _itemScrollController = ItemScrollController();
  final ItemPositionsListener _itemPositionsListener =
      ItemPositionsListener.create();
  int userId = -1;
  StreamSubscription<MessageModel>? _subscription;
  GlobalKey<FlutterMentionsState> key = GlobalKey<FlutterMentionsState>();
  final _imagePicker = ImagePicker();
  final roomInfoBloc = Modular.get<RoomInfoBloc>();
  int intervalAds = 0;
  final searchController = TextEditingController();
  final searchMessageBloc = Modular.get<SearchMessageBloc>();
  final mySubscriptionBloc = Modular.get<MySubscriptionBloc>();
  final searchOverlayCubit = Modular.get<SearchOverlayCubit>();
  final configBloc = Modular.get<ConfigBloc>();
  final scrollCubit = ScrollCubit();

  @override
  void initState() {
    _itemPositionsListener.itemPositions.addListener(_onScroll);
    roomInfoBloc.add(RoomGetInfoRequested(roomId: widget.roomId));
    super.initState();
    final user = userInfoBloc.state.user;
    if (user?.userId != null) {
      userId = user!.userId!;
    }
    getInitialMessages();

    streamMessageHandler();
  }

  @override
  void dispose() {
    _subscription?.cancel();
    searchController.dispose();
    super.dispose();
  }

  void getInitialMessages() {
    messageListBloc.add(MessageGetListRequested(roomId: widget.roomId));
  }

  streamMessageHandler() {
    _subscription = webSocketHelper.messages.listen((message) {
      if (message.roomId == widget.roomId) {
        switch (message.command) {
          case WebSocketCommand.userMessage:
            messageListBloc.add(MessageAddData(message: message));
            if (message.userId == userId) {
              //_scrollToBottom();
              AnalyticService.instance.logEvent(event: AFEvent.afChat);
            } else {
              _checkNewMessage();
            }
            checkAdsShown();
            break;
          case WebSocketCommand.botMessageChunk:
            messageListBloc.add(MessageAddChunk(message: message));
            break;
          case WebSocketCommand.botMessageComplete:
            messageListBloc.add(MessageAddChunk(message: message, isEnd: true));
            checkAdsShown();
            _checkNewMessage();
            break;
          default:
        }
      }
    });
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_itemScrollController.isAttached) {
        _itemScrollController.scrollTo(
          index: 0, // First item in the reversed list
          duration: const Duration(milliseconds: 100),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  checkAdsShown() {
    final endDate =
        mySubscriptionBloc.state.activeSubscription?.plan?.code == 'TRIAL'
            ? null
            : mySubscriptionBloc.state.activeSubscription?.endDate;
    int? adsInterstitialInterval =
        int.tryParse(configBloc.state.config?.adsInterstitialInterval ?? '');
    final maxInterval =
        (adsInterstitialInterval != null && adsInterstitialInterval > 0)
            ? adsInterstitialInterval
            : 5;
    if (endDate == null || endDate.toLocal().isBefore(DateTime.now())) {
      if (intervalAds < maxInterval * 2) {
        intervalAds++;
      } else {
        intervalAds = 0;
        if (widget.type == RoomType.single &&
            AdsService.instance.interstitialAdLoaded) {
          Utils.hideKeyboard();
          AdsService.instance.showInterstitialAd();
        }
      }

      Utils.debugLogSuccess(
          'intervalAds: $intervalAds, maxInterval: $maxInterval');
    }
  }

  void _onScroll() {
    final positions = _itemPositionsListener.itemPositions.value;
    if (positions.isNotEmpty) {
      // Check if the last message (bottom of reversed list) is visible
      final isLastMessageVisible = positions.any(
        (position) => position.index == 0 && position.itemTrailingEdge > 0.0,
      );
      if (isLastMessageVisible && scrollCubit.state) {
        scrollCubit.updateValue(false);
      }
    }
  }

  void _checkNewMessage() {
    Future.delayed(const Duration(milliseconds: 100), () {
      final positions = _itemPositionsListener.itemPositions.value;
      if (positions.isNotEmpty) {
        // Check if the last message (bottom of reversed list) is visible
        final isLastMessageVisible = positions.any(
          (position) => position.index == 0 && position.itemTrailingEdge > 0.0,
        );
        if (!isLastMessageVisible && !scrollCubit.state) {
          scrollCubit.updateValue(true);
        }
      }
    });
  }

  // void _onScroll() {
  //   if (_scrollController.position.atEdge) {
  //     bool isTop = _scrollController.position.pixels != 0;
  //     if (isTop) {
  //       messageListBloc.add(MessageGetMore(roomId: widget.roomId));
  //     }
  //   }
  // }

  void _handleSendMessage() {
    if (key.currentState!.controller!.text.trim().isNotEmpty) {
      final mediaUrl = (uploadFileBloc.state.medias != null &&
              uploadFileBloc.state.medias!.isNotEmpty)
          ? uploadFileBloc.state.medias?.first.relativePath
          : null;
      webSocketHelper.sendUserMessage(
        roomId: widget.roomId,
        content: key.currentState!.controller!.text.trim(),
        type: mediaUrl != null ? MessageType.image : MessageType.text,
        image: mediaUrl != null ? '${AppEnvironment.apiUrl}/$mediaUrl' : null,
      );

      key.currentState!.controller!.clear();
      uploadFileBloc.add(const FileUploadReset());
    }
  }

  void _handleImageFile(XFile? image) async {
    if (image != null) {
      final size = await image.length();
      if (size > 20 * 1024 * 1024) {
        if (mounted) {
          AppDialog.showErrorDialog(
            context: context,
            error: AppConfigs.maxFileSizeError,
          );
        }
      } else {
        uploadFileBloc.add(FileUploadRequested(path: image.path));
      }
    }
  }

  void _handlePickImage() async {
    final XFile? image = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      requestFullMetadata: false,
    );
    _handleImageFile(image);
  }

  void _handleCamera() async {
    // check permission first
    final status = await Permission.camera.status;
    if (status.isDenied) {
      // We haven't asked for permission yet or the permission has been denied before, but not permanently.
      await Permission.camera.onGrantedCallback(
        () async {
          final XFile? image =
              await _imagePicker.pickImage(source: ImageSource.camera);
          _handleImageFile(image);
        },
      ).request();
      return;
    }
    if (status.isPermanentlyDenied) {
      if (mounted) {
        AppDialog.show(
          context: context,
          buttons: [
            AppButton(
              backgroundColor: AppColors.system0,
              borderColor: AppColors.system3,
              outline: true,
              buttonColor: AppColors.system6,
              titleStyle: CustomStyle.large.smb,
              boxShadow: const [
                BoxShadow(
                  color: Color(0x0D000000), // Hexadecimal color #0000000D
                  offset: Offset(0, 14), // Horizontal and vertical offset
                  blurRadius: 14, // Blur radius
                  spreadRadius: 0, // Spread radius
                ),
              ],
              buttonTitle: context.localization.cancel,
              onPressed: () {
                Modular.to.pop();
              },
            ),
            AppButton(
              buttonTitle: context.localization.openSetting,
              onPressed: () {
                Modular.to.pop();
                openAppSettings();
              },
            ),
          ],
          title: context.localization.noPermission,
          description: context.localization.pleaseOpenSetting,
        );
      }
      return;
    }

    final XFile? image =
        await _imagePicker.pickImage(source: ImageSource.camera);
    _handleImageFile(image);
  }

  Widget _chatContent(RoomModel? room, RegExp? regex) {
    return GestureDetector(
      onTap: () => Utils.hideKeyboard(),
      child: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          if (notification is ScrollUpdateNotification) {
            final metrics = notification.metrics;

            // Check if we've scrolled to the top
            bool isAtTop = metrics.pixels == metrics.maxScrollExtent;

            if (isAtTop) {
              messageListBloc.add(MessageGetMore(roomId: widget.roomId));
            }
          }
          return false;
        },
        child: BlocConsumer<MessageListBloc, MessageListState>(
          bloc: messageListBloc,
          listener: (context, state) {
            if (state.scrollIndex != null) {
              Future.delayed(const Duration(milliseconds: 100), () {
                _itemScrollController.scrollTo(
                    index: state.scrollIndex!,
                    alignment: 0.5,
                    duration: const Duration(milliseconds: 200));
              });
            }
          },
          builder: (context, state) {
            if (state.isRequesting) {
              return const Center(
                child: CircularProgressIndicator(
                  color: Colors.blue,
                ),
              );
            }

            List<MessageModel> messages = state.messagesMap != null
                ? state.messagesMap!.values.toList()
                : [];

            if (messages.isEmpty) {
              return newChatView(context);
            }

            return Stack(
              children: [
                ScrollConfiguration(
                  behavior: _ScrollbarBehavior(),
                  child: ScrollablePositionedList.builder(
                    physics: const AlwaysScrollableScrollPhysics(),
                    itemCount: state.hasReachedMax
                        ? messages.length
                        : messages.length + 1,
                    itemScrollController: _itemScrollController,
                    itemPositionsListener: _itemPositionsListener,
                    reverse: true,
                    padding:
                        const EdgeInsets.only(left: 16, right: 16, top: 16),
                    //keepPositionWithoutScroll: true,
                    onItemKey: (index) => (index == messages.length)
                        ? "0"
                        : messages[index].messageId.toString(),
                    itemBuilder: (context, index) {
                      if (index == messages.length) {
                        return const Center(
                          child: CircularProgressIndicator(
                            color: Colors.blue,
                          ),
                        );
                      }
                      return RepaintBoundary(
                        child: CellChatMessageItem(
                          key: ValueKey(messages[index].messageId),
                          message: messages[index],
                          isMine: messages[index].userId == userId,
                          room: room,
                          regex: regex,
                          isShowTime: index == 0 ||
                              (messages[index].userId !=
                                  messages[index - 1].userId),
                          isShowUsername: (index == messages.length - 1) ||
                              (messages[index].userId !=
                                  messages[index + 1].userId),
                          isShowTimeTitle: (index == messages.length - 1) ||
                              (!Utils.isSameDay(messages[index].time,
                                  messages[index + 1].time)),
                        ),
                      );
                    },
                  ),
                ),
                BlocBuilder<ScrollCubit, bool>(
                  builder: (context, state) {
                    return state
                        ? Positioned(
                            child: Align(
                              alignment: Alignment.bottomCenter,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  AppButton(
                                    onPressed: _scrollToBottom,
                                    buttonGap: 8,
                                    paddingX: 12,
                                    buttonTitle: 'New message',
                                    prefixIcon: Icons.arrow_downward,
                                    buttonSize: ButtonSize.small,
                                    backgroundColor: AppColors.neutral,
                                    buttonColor: AppColors.neutral80,
                                    boxShadow: const [
                                      BoxShadow(
                                        color: Color(
                                            0x0D000000), // Hexadecimal color #0000000D
                                        offset: Offset(0,
                                            14), // Horizontal and vertical offset
                                        blurRadius: 14, // Blur radius
                                        spreadRadius: 0, // Spread radius
                                      ),
                                    ],
                                  ).paddingOnly(bottom: 8),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox();
                  },
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget leading() {
    return BlocBuilder<UploadFileBloc, UploadFileState>(
      builder: (context, state) {
        if (state.path != null) {
          return Container();
        }
        return Row(
          children: [
            AppInkwell(
              onTap: _handlePickImage,
              child: SvgPicture.asset(AppIcons.iconImagePicker),
            ),
            16.horizontalSpace,
            AppInkwell(
              onTap: _handleCamera,
              child: SvgPicture.asset(AppIcons.iconCamera),
            ),
            16.horizontalSpace,
          ],
        );
      },
    );
  }

  Widget newChatView(BuildContext context) {
    return BlocBuilder<RoomInfoBloc, RoomInfoState>(
      builder: (context, state) {
        return SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              24.verticalSpace,
              CachedNetworkImage(
                imageUrl:
                    Utils.convertImageUrl(state.room?.chatbot?.mediumAvatar),
                width: 140,
                height: 140,
                fit: BoxFit.contain,
                placeholder: (context, url) => Container(),
                errorWidget: (context, url, error) {
                  return Image.asset(AppImages.imagePlanBot);
                },
              ),
              12.verticalSpace,
              Text(
                state.room?.chatbot?.name ?? '',
                style: CustomStyle.h2.smb.copyWith(color: AppColors.system9),
              ),
              Text(
                context.localization.howCanIHelpYou,
                style: CustomStyle.medium.regular
                    .copyWith(color: AppColors.system8),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _actionCard(
    BuildContext context, {
    required String icon,
    required String title,
    required VoidCallback action,
  }) {
    return AppCupertinoActionSheetAction(
      onPressed: () {
        Modular.to.pop();
        action.call();
      },
      child: Row(
        children: [
          SvgPicture.asset(icon),
          12.horizontalSpace,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: CustomStyle.large.regular
                      .copyWith(color: AppColors.system8),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _clearContext() async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(context: context);

    final result = await chatRepository.clearContext(
      roomId: widget.roomId,
    );

    if (mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      messageListBloc.add(MessageClearContext());
    });
  }

  void _deleteGroup() async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(context: context);

    final result = await chatRepository.deleteRoom(
      roomId: widget.roomId,
    );

    if (mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      final roomListBloc = Modular.get<RoomListBloc>();
      roomListBloc.add(RoomDeleteRequested(roomId: widget.roomId));
      AppModuleHelper.navigateToMainPage();
    });
  }

  void showMoreAction(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return AppCupertinoActionSheet(
          actions: [
            _actionCard(
              context,
              icon: AppIcons.iconAddMember,
              title: context.localization.addMemberToCreateGroup,
              action: () {
                ChatModuleHelper.goToGroupSettingPage(
                  roomId: widget.roomId,
                  roomInfoBloc: roomInfoBloc,
                  messageListBloc: messageListBloc,
                  searchOverlayCubit: searchOverlayCubit,
                );
              },
            ),
            _actionCard(
              context,
              icon: AppIcons.iconClearContext,
              title: context.localization.clearContext,
              action: () {
                ActionGroup.showConfirmGroup(
                  context,
                  image: AppImages.imageClearContext,
                  title: context.localization.clearMessage,
                  description: context.localization.clearMessageContent,
                  confirmCallback: _clearContext,
                );
              },
            ),
            _actionCard(
              context,
              icon: AppIcons.iconSearch,
              title: context.localization.search,
              action: () {
                searchOverlayCubit.updateValue(true);
              },
            ),
            _actionCard(
              context,
              icon: AppIcons.iconDelete,
              title: context.localization.delete,
              action: () {
                ActionGroup.showConfirmGroup(
                  context,
                  image: AppImages.imageDelete,
                  title: context.localization.delete,
                  description: context.localization.confirmDelete,
                  confirmCallback: () => _deleteGroup(),
                );
              },
            ),
          ],
          cancelButton: AppCupertinoButton(
            padding: const EdgeInsets.all(12),
            child: Text(
              context.localization.cancel,
              style: CustomStyle.large.smb.copyWith(color: AppColors.system6),
            ),
            onPressed: () {
              Modular.to.pop();
            },
          ),
        );
      },
    );
  }

  Widget _searchOverlay(BuildContext context) {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withOpacity(0.5),
        child: Column(
          children: [
            SearchMessageField(
              controller: searchController,
              cancelUpdateCallback: () {
                searchOverlayCubit.updateValue(false);
              },
              searchMessageBloc: searchMessageBloc,
              roomId: widget.roomId,
            ),
            Expanded(
              child: BlocBuilder<SearchMessageBloc, SearchMessageState>(
                builder: (context, state) {
                  if (state is SearchMessageLoading) {
                    return const Center(
                      child: CircularProgressIndicator(
                        color: Colors.blue,
                      ),
                    );
                  }

                  if (state is SearchMessageError) {
                    return Center(
                      child: Text(
                        state.message,
                        style: CustomStyle.large.regular
                            .copyWith(color: AppColors.system0),
                      ),
                    );
                  }

                  if (state is SearchMessageLoaded) {
                    final messages = state.messages;
                    return ListView.builder(
                      itemCount: messages.length,
                      itemBuilder: (context, index) {
                        return CellSearchMessageItem(
                          message: messages[index],
                          onTap: () {
                            searchOverlayCubit.updateValue(false);
                            messageListBloc.add(SearchMessageRequest(
                              roomId: widget.roomId,
                              message: messages[index],
                            ));
                          },
                        );
                      },
                    );
                  }

                  return Container();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  RegExp? getRegex(RoomModel? room) {
    List<String> fullNames = [];

    final chatbotName = room?.chatbot?.name;

    room?.members?.forEach((member) {
      String? fullname = member.user?.fullname;
      if (fullname != null && fullname.isNotEmpty) {
        fullNames.add(fullname);
      }
    });

    if (chatbotName != null && chatbotName.isNotEmpty) {
      fullNames.add(chatbotName);
    }

    if (fullNames.isNotEmpty) {
      final namesPattern = fullNames.map(RegExp.escape).join('|');
      final regex = RegExp(r"@(" + namesPattern + r")");
      return regex;
    }

    return null;
  }

  Widget _banner(BuildContext context) {
    return BlocBuilder<AppBannerCubit, bool>(
      builder: (context, isBannerDisplayed) {
        return BlocBuilder<MySubscriptionBloc, MySubscriptionState>(
          builder: (context, mySubscriptionState) {
            final endDate =
                mySubscriptionState.activeSubscription?.plan?.code == 'TRIAL'
                    ? null
                    : mySubscriptionState.activeSubscription?.endDate;

            if (endDate != null &&
                (endDate.toLocal()).isAfter(DateTime.now()) &&
                !isBannerDisplayed) {
              return Container();
            }

            return BlocBuilder<ConfigBloc, ConfigState>(
              builder: (context, state) {
                final ads = state.config?.ads;
                return Stack(
                  children: [
                    Transform.scale(
                      scale: AdsService.instance.bannerAdView != null
                          ? MediaQuery.of(context).size.width / 320
                          : 1,
                      child: SizedBox(
                        height:
                            AdsService.instance.bannerAdView != null ? 50 : 1,
                        child: AdsService.instance.bannerAdView ?? Container(),
                      ),
                    ).paddingOnly(
                        bottom:
                            AdsService.instance.bannerAdView != null ? 4 : 0),
                    if (ads == "5")
                      Positioned(
                        top: -2,
                        right: 0,
                        child: Container(
                          width: 20,
                          height: 20,
                          color: AppColors.system5,
                          child: const Icon(Icons.close, size: 12),
                        ).inkwell(() {
                          AdsService.instance.destroyAdAndCreateNew();
                          SubscriptionModuleHelper
                              .goToUpgradeSubscriptionPage();
                        }, force: true),
                      ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<RoomInfoBloc>.value(value: roomInfoBloc),
        BlocProvider<MessageListBloc>.value(value: messageListBloc),
        BlocProvider<UploadFileBloc>.value(value: uploadFileBloc),
        BlocProvider<SearchMessageBloc>.value(value: searchMessageBloc),
        BlocProvider<SearchOverlayCubit>.value(value: searchOverlayCubit),
        BlocProvider<ScrollCubit>.value(value: scrollCubit),
      ],
      child: BlocBuilder<RoomInfoBloc, RoomInfoState>(
        builder: (context, state) {
          final roomData = state.room;
          final regex = getRegex(roomData);
          return BlocBuilder<SearchOverlayCubit, bool>(
            builder: (context, showOverlay) {
              return Scaffold(
                backgroundColor: AppColors.system1,
                resizeToAvoidBottomInset: !showOverlay,
                appBar: AppTopBar(
                  title: roomData?.name ?? context.localization.chat,
                  titleColor: AppColors.blue,
                  leadingWidth: null,
                  avatar: roomData?.logo ?? roomData?.chatbot?.smallAvatar,
                  subTitle: widget.type == RoomType.group
                      ? (roomData?.memberCount != null
                          ? '${roomData?.memberCount} ${context.localization.members}'
                          : null)
                      : null,
                  subTitleColor: AppColors.system6,
                  leading: IconInkwell(
                    path: AppIcons.iconChevronLeft,
                    color: AppColors.system5,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    onTap: () {
                      Modular.to.pop();
                      Utils.hideKeyboard();
                    },
                  ),
                  actions: [
                    if (roomData != null)
                      IconInkwell(
                        path: AppIcons.iconNewChat,
                        color: AppColors.system5,
                        padding: const EdgeInsets.all(16),
                        onTap: () {
                          Utils.hideKeyboard();
                          if (widget.type == RoomType.group) {
                            ChatModuleHelper.goToGroupSettingPage(
                              roomId: widget.roomId,
                              roomInfoBloc: roomInfoBloc,
                              messageListBloc: messageListBloc,
                              searchOverlayCubit: searchOverlayCubit,
                            );
                          } else {
                            showMoreAction(context);
                          }
                        },
                      )
                  ],
                ),
                body: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: Utils.convertImageUrl(roomData?.background),
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      alignment: Alignment.topCenter,
                      errorWidget: (context, error, stackTrace) {
                        return Container();
                      },
                    ),
                    Column(
                      children: [
                        _banner(context),
                        Expanded(child: _chatContent(roomData, regex)),
                        //if (roomData?.myMember?.status == MemberStatus.joined)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SuggestionTemplateView(
                              room: roomData,
                              onTapTemplate: (template) {
                                webSocketHelper.sendUserMessage(
                                  roomId: widget.roomId,
                                  content: template,
                                  type: MessageType.text,
                                );
                              },
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 16),
                              decoration: const BoxDecoration(
                                color: AppColors.system0,
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(16),
                                    topRight: Radius.circular(16)),
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(
                                        0x0D000000), // #0000000D in ARGB format
                                    offset: Offset(0,
                                        -2), // Horizontal: 0px, Vertical: -2px
                                    blurRadius: 4, // Blur radius: 4px
                                    spreadRadius: 0, // Spread radius: 0px
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ImageUploadView(
                                      uploadFileBloc: uploadFileBloc),
                                  FlutterMentions(
                                    key: key,
                                    mentions: [
                                      Mention(
                                        trigger: '@',
                                        style: CustomStyle.medium.regular
                                            .copyWith(color: AppColors.blue),
                                        data: [
                                          {
                                            "id":
                                                "chatbot-${roomData?.chatbot?.chatbotId ?? -1}",
                                            "display":
                                                roomData?.chatbot?.name ??
                                                    'chatbot',
                                            "photo": Utils.convertImageUrl(
                                                roomData?.chatbot?.smallAvatar),
                                          },
                                          ...(roomData?.members ??
                                                  (roomData?.members ?? []))
                                              .map((e) {
                                            return {
                                              "id": "member-${e.memberId}",
                                              "display": e.user?.username ??
                                                  (e.user?.fullname ?? ''),
                                              "photo": Utils.convertImageUrl(
                                                  e.user?.avatar),
                                            };
                                          }),
                                        ],
                                      ),
                                    ],
                                    suggestionPosition: SuggestionPosition.Top,
                                    cursorColor: AppColors.blue,
                                    style: CustomStyle.medium.regular
                                        .copyWith(color: AppColors.system8),
                                    decoration: InputDecoration(
                                      fillColor: AppColors.system0,
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(50),
                                        borderSide: BorderSide.none,
                                      ),
                                      isDense: true,
                                      contentPadding:
                                          const EdgeInsetsDirectional.symmetric(
                                              vertical: 8, horizontal: 4),
                                      filled: true,
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      // hint
                                      hintText:
                                          '${context.localization.talkChatbot}...',
                                      hintStyle: CustomStyle.medium.regular
                                          .copyWith(color: AppColors.system4),
                                    ),
                                    // multiline
                                    minLines: 1,
                                    maxLines: 5,
                                    // keyboard done action
                                    textInputAction: TextInputAction.newline,
                                    leading: [leading()],
                                    trailing: [
                                      8.horizontalSpace,
                                      IconInkwell(
                                        path: AppIcons.iconSend,
                                        color: AppColors.blue,
                                        onTap: _handleSendMessage,
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: (Platform.isIOS
                                        ? (MediaQuery.of(context)
                                                    .viewInsets
                                                    .bottom >
                                                MediaQuery.of(context)
                                                        .viewPadding
                                                        .bottom +
                                                    MediaQuery.of(context)
                                                        .viewPadding
                                                        .top
                                            ? 0
                                            : MediaQuery.of(context)
                                                .viewPadding
                                                .bottom)
                                        : 0),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    if (showOverlay) _searchOverlay(context),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}

class _ScrollbarBehavior extends ScrollBehavior {
  @override
  Widget buildScrollbar(
      BuildContext context, Widget child, ScrollableDetails details) {
    return Scrollbar(controller: details.controller, child: child);
  }
}

class ScrollCubit extends Cubit<bool> {
  ScrollCubit() : super(false);

  void updateValue(bool value) {
    emit(value);
  }
}
