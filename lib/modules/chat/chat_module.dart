import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/modules/chat/data/datasources/chat_api.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/general/chat_module_routes.dart';
import 'package:saymee/modules/chat/presentation/blocs/message_list/message_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_info/room_info_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_message/search_message_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_overlay_cubit.dart';
import 'package:saymee/modules/chat/presentation/blocs/search_room/search_room_bloc.dart';
import 'package:saymee/modules/chat/presentation/pages/chatbot_page.dart';
import 'package:saymee/modules/chat/presentation/pages/create_room_page.dart';
import 'package:saymee/modules/chat/presentation/pages/group_member_page.dart';
import 'package:saymee/modules/chat/presentation/pages/group_setting_page.dart';
import 'package:saymee/modules/chat/presentation/pages/join_group_page.dart';
import 'package:saymee/modules/chat/presentation/pages/member_request_page.dart';
import 'package:saymee/modules/chat/presentation/pages/photo_view_page.dart';
import 'package:saymee/modules/chat/presentation/pages/update_group_page.dart';

class ChatModule extends Module {
  @override
  // ignore: unnecessary_overrides
  void binds(Injector i) {
    super.binds(i);
  }

  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);

    i.addSingleton(() => ChatApi());
    i.addSingleton(() => ChatRepository(api: Modular.get<ChatApi>()));

    i.addSingleton(
        () => RoomListBloc(repository: Modular.get<ChatRepository>()));
    i.add(() => MessageListBloc(repository: Modular.get<ChatRepository>()));
    i.add(() => RoomInfoBloc(repository: Modular.get<ChatRepository>()));
    i.add(() => SearchRoomBloc(repository: Modular.get<ChatRepository>()));
    i.add(() => SearchMessageBloc(repository: Modular.get<ChatRepository>()));
    i.add(() => SearchOverlayCubit());
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);

    r.child(
      '${ChatModuleRoutes.chatbot}/:id',
      child: (context) => ChatbotPage(
        roomId: r.args.data['roomId'],
        type: r.args.data['type'],
      ),
    );
    r.child(
      ChatModuleRoutes.photoView,
      child: (context) => PhotoViewPage(url: r.args.data['url']),
    );
    r.child(
      ChatModuleRoutes.createRoom,
      child: (context) => CreateRoomPage(chatbot: r.args.data['chatbot']),
    );
    r.child(
      ChatModuleRoutes.groupSetting,
      child: (context) => GroupSettingPage(
        roomId: r.args.data['roomId'],
        roomInfoBloc: r.args.data['roomInfoBloc'],
        messageListBloc: r.args.data['messageListBloc'],
        searchOverlayCubit: r.args.data['searchOverlayCubit'],
      ),
    );
    r.child(
      ChatModuleRoutes.updateGroup,
      child: (context) => UpdateGroupPage(
        roomInfoBloc: r.args.data['roomInfoBloc'],
      ),
    );
    r.child(
      ChatModuleRoutes.groupMember,
      child: (context) => GroupMemberPage(
        roomInfoBloc: r.args.data['roomInfoBloc'],
      ),
    );
    r.child(
      ChatModuleRoutes.joinGroup,
      child: (context) => JoinGroupPage(
        room: r.args.data['room'],
      ),
    );
    r.child(
      ChatModuleRoutes.memberRequest,
      child: (context) => MemberRequestPage(
        roomInfoBloc: r.args.data['roomInfoBloc'],
      ),
    );
  }
}
