import 'package:dio/dio.dart';
import 'package:saymee/core/utils/utils.dart';

class ChatApi {
  final dioClient = Utils.dioClient;

  Future<Response> getMyRooms({
    required CancelToken cancelToken,
  }) async {
    const String url = '/api/v3/room/my';

    try {
      final Response response = await dioClient.get(
        url,
        cancelToken: cancelToken,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> createRoom({
    required int chatbotId,
    required int public,
    String? name,
    String? logo,
  }) async {
    const String url = '/api/v3/room/create';

    final params = {
      'chatbotId': chatbotId,
      'public': public,
      'name': name,
      'logo': logo,
    };

    Utils.debugLogWarning(params);

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> getRoomMessages({
    required int roomId,
    int? offset,
    int? limit,
    String? since,
  }) async {
    const String url = '/api/v3/room/messages';

    final params = {
      'roomId': roomId,
      'offset': offset,
      'limit': limit,
      'since': since,
    };

    try {
      final Response response = await dioClient.get(
        url,
        queryParameters: params,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> getRoom({
    required int roomId,
  }) async {
    const String url = '/api/v3/room/search';

    final params = {
      'roomId': roomId,
    };

    try {
      final Response response = await dioClient.get(
        url,
        queryParameters: params,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> updateRoom({
    required int roomId,
    String? name,
    String? logo,
    int? public,
    String? description,
  }) async {
    String url = '/api/v3/room/$roomId';

    final params = {
      'name': name,
      'logo': logo,
      'public': public,
      'description': description,
    };

    try {
      final Response response = await dioClient.put(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> clearContext({
    required int roomId,
  }) async {
    const String url = '/api/v3/room/clearContext';

    final params = {
      'roomId': roomId,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> join({
    int? roomId,
    String? secret,
  }) async {
    const String url = '/api/v3/room/join';

    final params = {
      'roomId': roomId,
      'secret': secret,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> leave({
    required int roomId,
  }) async {
    const String url = '/api/v3/room/leave';

    final params = {
      'roomId': roomId,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> block({
    required int roomId,
    required int userId,
  }) async {
    const String url = '/api/v3/room/block';

    final params = {
      'roomId': roomId,
      'userId': userId,
    };

    try {
      Utils.debugLog(params);
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> unblock({
    required int roomId,
    required int userId,
  }) async {
    const String url = '/api/v3/room/unblock';

    final params = {
      'roomId': roomId,
      'userId': userId,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> invite({
    required int roomId,
    required int userId,
  }) async {
    const String url = '/api/v3/room/invite';

    final params = {
      'roomId': roomId,
      'userId': userId,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> acceptMember({
    required int memberId,
  }) async {
    const String url = '/api/v3/room/acceptMember';

    final params = {
      'memberId': memberId,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> rejectMember({
    required int memberId,
  }) async {
    const String url = '/api/v3/room/rejectMember';

    final params = {
      'memberId': memberId,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> acceptInvite({
    required int roomId,
  }) async {
    const String url = '/api/v3/room/acceptInvite';

    final params = {
      'roomId': roomId,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> rejectInvite({
    required int roomId,
  }) async {
    const String url = '/api/v3/room/rejectInvite';

    final params = {
      'roomId': roomId,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> searchRoom({
    required String q,
    CancelToken? cancelToken,
  }) async {
    const String url = '/api/v3/room/search';

    final params = {'q': q};

    try {
      final Response response = await dioClient.get(
        url,
        queryParameters: params,
        cancelToken: cancelToken,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> deleteRoom({
    required int roomId,
  }) async {
    String url = '/api/v3/room/$roomId';

    final params = {'roomId': roomId};

    try {
      final Response response = await dioClient.delete(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> setMemberNotification({
    required int roomId,
    required int onOff,
  }) async {
    String url = '/api/v3/room/setNotification';

    final params = {
      'roomId': roomId,
      'onoff': onOff,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> searchMessage({
    required int roomId,
    required String q,
    CancelToken? cancelToken,
  }) async {
    String url = '/api/v3/chat/search';

    final params = {
      'roomId': roomId,
      'q': q,
    };

    try {
      final Response response = await dioClient.get(
        url,
        queryParameters: params,
        cancelToken: cancelToken,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }
}
