import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/network/dio_exceptions.dart';
import 'package:saymee/core/network/dio_failure.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/datasources/chat_api.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';

class ChatRepository {
  final ChatApi api;

  ChatRepository({required this.api});

  Future<Either<DioFailure, Map<String, dynamic>>> getMyRooms(
      {required CancelToken cancelToken}) async {
    try {
      final response = await api.getMyRooms(cancelToken: cancelToken);
      Utils.debugLog('getMyRooms response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final userId = UserModuleHelper.getUserId();
        final List<RoomModel> oneToOneRooms = mapData['oneToOneRooms'] != null
            ? (mapData['oneToOneRooms'] as List)
                .map((e) => RoomModel.fromJson(
                      e as Map<String, dynamic>,
                      userId: userId,
                    ))
                .toList()
            : [];
        final List<RoomModel> groupRooms = mapData['groupRooms'] != null
            ? (mapData['groupRooms'] as List)
                .map((e) => RoomModel.fromJson(
                      e as Map<String, dynamic>,
                      userId: userId,
                    ))
                .toList()
            : [];

        return Right({
          'statusCode': statusCode,
          'oneToOneRooms': oneToOneRooms,
          'groupRooms': groupRooms,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      if (CancelToken.isCancel(e)) {
        return const Right({
          'isCancel': true,
        });
      }
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> getRoomMessages({
    required int roomId,
    int? offset,
    int? limit,
    String? since,
    int? scrollMessageId,
  }) async {
    try {
      final response = await api.getRoomMessages(
        roomId: roomId,
        offset: offset,
        limit: limit,
        since: since,
      );
      //Utils.debugLog('getRoomMessages response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final messagesJsonList = mapData['messages'] as List;
        List<MessageModel> messages = [];
        bool haveUserMessage = false;
        int? scrollIndex;

        for (int i = 0; i < messagesJsonList.length; i++) {
          final message = MessageModel.fromJson(
              messagesJsonList[i] as Map<String, dynamic>);

          messages.add(message);
          if (!haveUserMessage && message.userId != null) {
            haveUserMessage = true;
          }

          if (scrollIndex == null) {
            if (scrollMessageId != null &&
                message.messageId == scrollMessageId) {
              scrollIndex = i;
            }
          }
        }

        return Right({
          'statusCode': statusCode,
          'messages': messages,
          'haveUserMessage': haveUserMessage,
          'scrollIndex': scrollIndex,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> createRoom({
    required int chatbotId,
    required int public,
    String? name,
    String? logo,
  }) async {
    try {
      final response = await api.createRoom(
        chatbotId: chatbotId,
        public: public,
        name: name,
        logo: logo,
      );
      Utils.debugLog('createRoom response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final userId = UserModuleHelper.getUserId();
        final RoomModel? room = mapData['room'] != null
            ? RoomModel.fromJson(
                mapData['room'] as Map<String, dynamic>,
                userId: userId,
              )
            : null;
        return Right({
          'statusCode': statusCode,
          'room': room,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> getRoom(
      {required int roomId}) async {
    try {
      final response = await api.getRoom(roomId: roomId);
      Utils.debugLog('getRoom response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final dataList = mapData['rooms'] as List?;

        if (dataList == null || dataList.length != 1) {
          return Left(ApiFailure(
            reason: "An error occurred, please try again.",
            statusCode: statusCode,
          ));
        }

        final userId = UserModuleHelper.getUserId();

        final room = RoomModel.fromJson(
          dataList.first as Map<String, dynamic>,
          userId: userId,
        );

        return Right({
          'statusCode': statusCode,
          'room': room,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> updateRoom({
    required int roomId,
    String? name,
    String? logo,
    int? public,
    String? description,
  }) async {
    try {
      final response = await api.updateRoom(
        roomId: roomId,
        name: name,
        logo: logo,
        public: public,
      );
      Utils.debugLog('updateRoom response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> clearContext({
    required int roomId,
  }) async {
    try {
      final response = await api.clearContext(roomId: roomId);
      Utils.debugLog('clearContext response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> join({
    int? roomId,
    String? secret,
  }) async {
    try {
      final response = await api.join(
        roomId: roomId,
        secret: secret,
      );
      Utils.debugLog('join response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final RoomModel? room = mapData['room'] != null
            ? RoomModel.fromJson(mapData['room'])
            : null;
        return Right({
          'statusCode': statusCode,
          'room': room,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> leave({
    required int roomId,
  }) async {
    try {
      final response = await api.leave(roomId: roomId);
      Utils.debugLog('leave response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> block({
    required int roomId,
    required int userId,
  }) async {
    try {
      final response = await api.block(
        roomId: roomId,
        userId: userId,
      );
      Utils.debugLog('block response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> unblock({
    required int roomId,
    required int userId,
  }) async {
    try {
      final response = await api.unblock(
        roomId: roomId,
        userId: userId,
      );
      Utils.debugLog('unblock response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> invite({
    required int roomId,
    required int userId,
  }) async {
    try {
      final response = await api.invite(
        roomId: roomId,
        userId: userId,
      );
      Utils.debugLog('invite response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> acceptMember({
    required int memberId,
  }) async {
    try {
      final response = await api.acceptMember(memberId: memberId);
      Utils.debugLog('acceptMember response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> rejectMember({
    required int memberId,
  }) async {
    try {
      final response = await api.rejectMember(memberId: memberId);
      Utils.debugLog('rejectMember response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> acceptInvite({
    required int roomId,
  }) async {
    try {
      final response = await api.acceptInvite(roomId: roomId);
      Utils.debugLog('acceptInvite response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> rejectInvite({
    required int roomId,
  }) async {
    try {
      final response = await api.rejectInvite(roomId: roomId);
      Utils.debugLog('rejectInvite response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> searchRoom({
    required String q,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await api.searchRoom(
        q: q,
        cancelToken: cancelToken,
      );
      Utils.debugLog('searchRoom response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final userId = UserModuleHelper.getUserId();
        final List<RoomModel> rooms = mapData['rooms'] != null
            ? (mapData['rooms'] as List)
                .map((e) => RoomModel.fromJson(
                      e as Map<String, dynamic>,
                      userId: userId,
                    ))
                .toList()
            : [];

        return Right({
          'statusCode': statusCode,
          'rooms': rooms,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> deleteRoom({
    required int roomId,
  }) async {
    try {
      final response = await api.deleteRoom(roomId: roomId);
      Utils.debugLog('deleteRoom response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> setMemberNotification({
    required int roomId,
    required int onOff,
  }) async {
    try {
      final response =
          await api.setMemberNotification(roomId: roomId, onOff: onOff);
      Utils.debugLog('setNotification response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> searchMessage({
    required int roomId,
    required String q,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await api.searchMessage(roomId: roomId, q: q);
      Utils.debugLog('searchMessage response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final List<MessageModel> messages = mapData['messages'] != null
            ? (mapData['messages'] as List)
                .map((e) => MessageModel.fromJson(e as Map<String, dynamic>))
                .toList()
            : [];

        return Right({
          'statusCode': statusCode,
          'messages': messages,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }
}
