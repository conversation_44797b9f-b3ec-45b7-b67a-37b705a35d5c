// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageModel _$MessageModelFromJson(Map<String, dynamic> json) {
  List<ButtonModel>? buttons;
  Map<String, dynamic>? metadataJson = json['metadata'];

  if (metadataJson != null && metadataJson['buttons'] != null) {
    buttons = metadataJson['buttons'] != null
        ? (metadataJson['buttons'] as List)
            .map((e) => ButtonModel.fromJson(e as Map<String, dynamic>))
            .toList()
        : null;
  }
  return MessageModel(
    messageId: (json['messageId'] as num?)?.toInt(),
    roomId: (json['roomId'] as num?)?.toInt(),
    userId: (json['userId'] as num?)?.toInt(),
    chatbotId: (json['chatbotId'] as num?)?.toInt(),
    inputTokens: (json['inputTokens'] as num?)?.toInt(),
    outputTokens: (json['outputTokens'] as num?)?.toInt(),
    content: json['content'] as String?,
    type: json['type'] as String?,
    time: json['time'] == null ? null : DateTime.parse(json['time'] as String),
    user: json['user'] == null
        ? null
        : UserModel.fromJson(json['user'] as Map<String, dynamic>),
    command: json['command'] as String?,
    image: json['image'] as String?,
    buttons: buttons,
  );
}

Map<String, dynamic> _$MessageModelToJson(MessageModel instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      'roomId': instance.roomId,
      'userId': instance.userId,
      'chatbotId': instance.chatbotId,
      'inputTokens': instance.inputTokens,
      'outputTokens': instance.outputTokens,
      'content': instance.content,
      'type': instance.type,
      'time': instance.time?.toIso8601String(),
      'user': instance.user,
      'command': instance.command,
      'image': instance.image,
      'buttons': instance.buttons,
    };
