import 'package:equatable/equatable.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';
import 'package:saymee/modules/chat/data/models/member_model.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';

part 'room_model_json.dart';

class RoomModel extends Equatable {
  final int? roomId;
  final String? name;
  final int? chatbotId;
  final String? description;
  final String? logo;
  final int? public;
  final int? lastMessageId;
  final List<MemberModel>? members;
  final ChatbotModel? chatbot;
  final MessageModel? lastMessage;
  final MessageModel? firstMessage;
  final String? background;
  final MemberModel? myMember;
  final int? memberCount;
  final int? requestCount;
  final String? link;

  const RoomModel({
    required this.roomId,
    required this.name,
    required this.chatbotId,
    required this.description,
    required this.logo,
    required this.public,
    required this.lastMessageId,
    required this.members,
    required this.chatbot,
    required this.lastMessage,
    required this.firstMessage,
    required this.background,
    required this.myMember,
    required this.memberCount,
    required this.requestCount,
    required this.link,
  });

  @override
  List<Object?> get props => [
        roomId,
        name,
        chatbotId,
        description,
        logo,
        public,
        lastMessageId,
        members,
        chatbot,
        lastMessage,
        firstMessage,
        background,
        myMember,
        memberCount,
        requestCount,
        link,
      ];

  // Responsible for creating a instance from the json.
  factory RoomModel.fromJson(Map<String, dynamic> json, {int? userId}) =>
      _$RoomModelFromJson(json, userId: userId);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$RoomModelToJson(this);

  RoomModel copyWith({
    ChatbotModel? chatbot,
    List<MemberModel>? members,
    String? description,
    MemberModel? myMember,
    int? memberCount,
    int? requestCount,
    MessageModel? lastMessage,
    int? lastMessageId,
  }) {
    return RoomModel(
      roomId: roomId,
      name: name,
      chatbotId: chatbotId,
      description: description ?? this.description,
      logo: logo,
      public: public,
      lastMessageId: lastMessageId ?? this.lastMessageId,
      members: members ?? this.members,
      chatbot: chatbot ?? this.chatbot,
      lastMessage: lastMessage ?? this.lastMessage,
      firstMessage: firstMessage,
      background: background,
      myMember: myMember ?? this.myMember,
      memberCount: memberCount ?? this.memberCount,
      requestCount: requestCount ?? this.requestCount,
      link: link,
    );
  }
}
