import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'button_model.g.dart';

@JsonSerializable()
class ButtonModel extends Equatable {
  final String? title;
  final String? link;

  const ButtonModel({
    required this.title,
    required this.link,
  });

  @override
  List<Object?> get props => [
        title,
        link,
      ];

  // Responsible for creating a instance from the json.
  factory ButtonModel.fromJson(Map<String, dynamic> json) =>
      _$ButtonModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$ButtonModelToJson(this);
}
