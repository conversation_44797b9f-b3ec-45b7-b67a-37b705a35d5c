import 'package:equatable/equatable.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'member_model.g.dart';

@JsonSerializable()
class MemberModel extends Equatable {
  final int? memberId;
  final int? roomId;
  final int? userId;
  final String? role;
  final String? status;
  final int? notification;
  final UserModel? user;

  const MemberModel({
    required this.memberId,
    required this.roomId,
    required this.userId,
    required this.role,
    required this.status,
    required this.notification,
    required this.user,
  });

  @override
  List<Object?> get props => [
        memberId,
        roomId,
        userId,
        role,
        status,
        notification,
        user,
      ];

  // Responsible for creating a instance from the json.
  factory MemberModel.fromJson(Map<String, dynamic> json) =>
      _$MemberModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$MemberModelToJson(this);

  MemberModel copyWith({
    String? status,
    int? notification,
  }) {
    return MemberModel(
      memberId: memberId,
      roomId: roomId,
      userId: userId,
      role: role,
      status: status ?? this.status,
      notification: notification ?? this.notification,
      user: user,
    );
  }
}
