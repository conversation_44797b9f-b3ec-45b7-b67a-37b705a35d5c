// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'room_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RoomModel _$RoomModelFromJson(Map<String, dynamic> json, {int? userId}) {
  List<dynamic>? membersData = (json['members'] as List<dynamic>?);
  List<MemberModel>? members;
  MemberModel? myMember;
  int memberCount = 0;
  int requestCount = 0;

  membersData?.forEach((element) {
    final member = MemberModel.fromJson(element as Map<String, dynamic>);

    members ??= [];
    members?.add(member);
    if (myMember == null && member.userId == userId && userId != null) {
      myMember = member;
    }

    switch (member.status) {
      case MemberStatus.blocked:
        memberCount++;
        break;
      case MemberStatus.joined:
        memberCount++;
        break;
      case MemberStatus.requested:
        requestCount++;
        break;
      default:
    }
  });

  return RoomModel(
    roomId: (json['roomId'] as num?)?.toInt(),
    name: json['name'] as String?,
    chatbotId: (json['chatbotId'] as num?)?.toInt(),
    description: json['description'] as String?,
    logo: json['logo'] as String?,
    public: (json['public'] as num?)?.toInt(),
    lastMessageId: (json['lastMessageId'] as num?)?.toInt(),
    members: members,
    chatbot: json['chatbot'] == null
        ? null
        : ChatbotModel.fromJson(json['chatbot'] as Map<String, dynamic>),
    lastMessage: json['lastMessage'] == null
        ? null
        : MessageModel.fromJson(json['lastMessage'] as Map<String, dynamic>),
    firstMessage: json['firstMessage'] == null
        ? null
        : MessageModel.fromJson(json['firstMessage'] as Map<String, dynamic>),
    background: json['background'] as String?,
    myMember: myMember,
    memberCount: memberCount,
    requestCount: requestCount,
    link: json['link'] as String?,
  );
}

Map<String, dynamic> _$RoomModelToJson(RoomModel instance) => <String, dynamic>{
      'roomId': instance.roomId,
      'name': instance.name,
      'chatbotId': instance.chatbotId,
      'description': instance.description,
      'logo': instance.logo,
      'public': instance.public,
      'lastMessageId': instance.lastMessageId,
      'members': instance.members,
      'chatbot': instance.chatbot,
      'lastMessage': instance.lastMessage,
      'firstMessage': instance.firstMessage,
      'background': instance.background,
      'myMember': instance.myMember,
    };
