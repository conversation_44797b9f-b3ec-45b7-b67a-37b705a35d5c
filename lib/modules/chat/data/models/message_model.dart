import 'package:equatable/equatable.dart';
import 'package:saymee/modules/chat/data/models/button_model.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';

part 'message_model_json.dart';

class MessageModel extends Equatable {
  final int? messageId;
  final int? roomId;
  final int? userId;
  final int? chatbotId;
  final int? inputTokens;
  final int? outputTokens;
  final String? content;
  final String? type;
  final DateTime? time;
  final UserModel? user;
  final String? command;
  final String? image;
  final List<ButtonModel>? buttons;

  const MessageModel({
    required this.messageId,
    required this.roomId,
    required this.userId,
    required this.chatbotId,
    required this.inputTokens,
    required this.outputTokens,
    required this.content,
    required this.type,
    required this.time,
    required this.user,
    required this.command,
    required this.image,
    required this.buttons,
  });

  @override
  List<Object?> get props => [
        messageId,
        roomId,
        userId,
        chatbotId,
        inputTokens,
        outputTokens,
        content,
        type,
        time,
        user,
        command,
        image,
        buttons,
      ];

  // Responsible for creating a instance from the json.
  factory MessageModel.fromJson(Map<String, dynamic> json) =>
      _$MessageModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$MessageModelToJson(this);

  MessageModel copyWith({
    int? userId,
    UserModel? user,
    String? content,
    DateTime? time,
    String? command,
    String? image,
  }) {
    return MessageModel(
      messageId: messageId,
      roomId: roomId,
      userId: userId ?? this.userId,
      chatbotId: chatbotId,
      inputTokens: inputTokens,
      outputTokens: outputTokens,
      content: content ?? this.content,
      type: type,
      time: this.time ?? time,
      user: user ?? this.user,
      command: command ?? this.command,
      image: image ?? this.image,
      buttons: buttons,
    );
  }
}
