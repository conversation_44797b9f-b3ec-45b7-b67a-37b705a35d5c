import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/helpers/general_helper.dart';
import 'package:saymee/core/network/dio_exceptions.dart';
import 'package:saymee/core/network/dio_failure.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/data/datasources/app_api.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';
import 'package:saymee/modules/app/data/models/config_model.dart';
import 'package:saymee/modules/app/data/models/media_model.dart';

class AppRepository {
  final AppApi api;

  AppRepository({required this.api});

  Future<Either<DioFailure, Map<String, dynamic>>> getChatbots() async {
    try {
      final response = await api.getChatbots();
      Utils.debugLog('getChatbots response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final List<ChatbotModel> chatbots = mapData['chatbots'] != null
            ? (mapData['chatbots'] as List)
                .map((e) => ChatbotModel.fromJson(e as Map<String, dynamic>))
                .toList()
            : [];

        return Right({
          'statusCode': statusCode,
          'chatbots': chatbots,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> getConfig() async {
    try {
      final response = await api.getConfig(
        appId: GeneralHelper.packageName,
        uuid: globalUuid ?? "",
        version: GeneralHelper.appVersion,
        platform: Platform.isIOS ? 'iOS' : 'Android',
      );
      Utils.debugLog('getConfig response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final ConfigModel? config = mapData['config'] != null
          ? ConfigModel.fromJson(mapData['config'] as Map<String, dynamic>)
          : null;
      return Right({
        'statusCode': statusCode,
        'config': config,
      });
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> uploadFile({
    required List<String> paths,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await api.upload(
        paths: paths,
        cancelToken: cancelToken,
      );
      Utils.debugLog('upload response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final List<MediaModel> medias = mapData['media'] != null
            ? (mapData['media'] as List)
                .map((e) => MediaModel.fromJson(e as Map<String, dynamic>))
                .toList()
            : [];

        return Right({
          'statusCode': statusCode,
          'medias': medias,
          'isCancel': false,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      if (CancelToken.isCancel(e)) {
        return const Right({
          'medias': null,
          'isCancel': true,
        });
      }
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> uploadFileWithBytes({
    required List<int> bytes,
  }) async {
    try {
      final response = await api.uploadWithBytes(bytes: bytes);
      Utils.debugLog('upload uploadWithBytes = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final List<MediaModel> medias = mapData['media'] != null
            ? (mapData['media'] as List)
                .map((e) => MediaModel.fromJson(e as Map<String, dynamic>))
                .toList()
            : [];

        return Right({
          'statusCode': statusCode,
          'medias': medias,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }
}
