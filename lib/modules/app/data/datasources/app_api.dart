import 'package:dio/dio.dart';
import 'package:saymee/core/constants/app_environment.dart';
import 'package:saymee/core/utils/utils.dart';

class AppApi {
  final dioClient = Utils.dioClient;

  Future<Response> getChatbots() async {
    const String url = '/api/v3/chatbot';

    try {
      final Response response = await dioClient.get(url);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> getConfig({
    required String appId,
    required String uuid,
    required String version,
    required String platform,
  }) async {
    const String url = '/config/get_jesterbot_config.php';

    final params = {
      'appId': appId,
      'uuid': uuid,
      'version': version,
      'platform': platform,
    };

    try {
      final Response response = await Dio(BaseOptions(
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        baseUrl: AppEnvironment.configUrl,
        responseType: ResponseType.json,
      )).get(url, queryParameters: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> upload({
    required List<String> paths,
    CancelToken? cancelToken,
  }) async {
    const String url = '/api/v3/media/upload';
    List<MultipartFile> files = [];

    try {
      for (var path in paths) {
        String filename = path.split('/').last;
        final multipartFile =
            await MultipartFile.fromFile(path, filename: filename);
        files.add(multipartFile);
      }

      final Response response = await dioClient.post(
        url,
        data: FormData.fromMap({'files': files}),
        cancelToken: cancelToken,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> uploadWithBytes({
    required List<int> bytes,
    CancelToken? cancelToken,
  }) async {
    const String url = '/api/v3/media/upload';
    List<MultipartFile> files = [];

    try {
      final multipartFile =
          MultipartFile.fromBytes(bytes, filename: 'local.png');
      files.add(multipartFile);

      final Response response = await dioClient.post(
        url,
        data: FormData.fromMap({'files': files}),
        cancelToken: cancelToken,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }
}
