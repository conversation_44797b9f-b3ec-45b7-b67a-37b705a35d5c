import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'artwork_model.dart';
import 'offer_model.dart';

part 'attributes_model.g.dart';

@JsonSerializable()
class AttributesModel extends Equatable {
  final Map<String, dynamic>? description;
  final String? icuLocale;
  final int? isFamilyShareable;
  final int? isMerchandisedEnabled;
  final int? isMerchandisedVisibleByDefault;
  final int? isSubscription;
  final String? kind;
  final String? name;
  final String? offerName;
  final List<OfferModel>? offers;
  final String? releaseDate;
  final String? subscriptionFamilyId;
  final int? subscriptionFamilyRank;
  final String? url;
  final ArtworkModel? artwork;

  const AttributesModel({
    required this.description,
    required this.icuLocale,
    required this.isFamilyShareable,
    required this.isMerchandisedEnabled,
    required this.isMerchandisedVisibleByDefault,
    required this.isSubscription,
    required this.kind,
    required this.name,
    required this.offerName,
    required this.offers,
    required this.releaseDate,
    required this.subscriptionFamilyId,
    required this.subscriptionFamilyRank,
    required this.url,
    required this.artwork,
  });

  @override
  List<Object?> get props => [
        description,
        icuLocale,
        isFamilyShareable,
        isMerchandisedEnabled,
        isMerchandisedVisibleByDefault,
        isSubscription,
        kind,
        name,
        offerName,
        offers,
        releaseDate,
        subscriptionFamilyId,
        subscriptionFamilyRank,
        url,
        artwork,
      ];

  factory AttributesModel.fromJson(Map<String, dynamic> json) =>
      _$AttributesModelFromJson(json);

  Map<String, dynamic> toJson() => _$AttributesModelToJson(this);
}
