// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConfigModel _$ConfigModelFromJson(Map<String, dynamic> json) => ConfigModel(
      ads: json['ads'] as String?,
      adsDelay: json['ads_delay'] as String?,
      adsDuration: json['ads_duration'] as String?,
      adsIds: json['ads_ids'] as String?,
      adsInterstitialIds: json['ads_interstitial_ids'] as String?,
      adsInterstitialInterval: json['ads_interstitial_interval'] as String?,
      adsInterstitialOrder: json['ads_interstitial_order'] as String?,
      adsInterval: json['ads_interval'] as String?,
      adsOrder: json['ads_order'] as String?,
      askForRating: json['askforrating'] as String?,
      autoShowChatbox: json['auto_show_chatbox'] as String?,
      discountPercent: json['discount_percent'] as String?,
      supportEmail: json['support_email'] as String?,
      enablePromo: json['enable_promo'] as String?,
      message: json['message'] as String?,
      messageHtml: json['message_html'] as String?,
      messageText: json['message_text'] as String?,
      price: json['price'] as String?,
      rewardedOrder: json['rewarded_order'] as String?,
      theme: json['theme'] as String?,
      updateUrl: json['update_url'] as String?,
      version: json['version'] as String?,
      currentDate: json['currentDate'] as String?,
      upgradeBackground: json['upgrade_background'] as String?,
      forceOnboardingSubscription:
          json['force_onboarding_subscription'] as String?,
      reviewUrl: json['review_url'] as String?,
      upgradeBenefits: json['upgrade_benefits'] as String?,
      upgradeIntro: json['upgrade_intro'] as String?,
    );

Map<String, dynamic> _$ConfigModelToJson(ConfigModel instance) =>
    <String, dynamic>{
      'ads': instance.ads,
      'ads_delay': instance.adsDelay,
      'ads_duration': instance.adsDuration,
      'ads_ids': instance.adsIds,
      'ads_interstitial_ids': instance.adsInterstitialIds,
      'ads_interstitial_interval': instance.adsInterstitialInterval,
      'ads_interstitial_order': instance.adsInterstitialOrder,
      'ads_interval': instance.adsInterval,
      'ads_order': instance.adsOrder,
      'askforrating': instance.askForRating,
      'auto_show_chatbox': instance.autoShowChatbox,
      'discount_percent': instance.discountPercent,
      'supportEmail': instance.supportEmail,
      'enable_promo': instance.enablePromo,
      'message': instance.message,
      'message_html': instance.messageHtml,
      'message_text': instance.messageText,
      'price': instance.price,
      'rewarded_order': instance.rewardedOrder,
      'theme': instance.theme,
      'update_url': instance.updateUrl,
      'version': instance.version,
      'currentDate': instance.currentDate,
      'upgrade_background': instance.upgradeBackground,
      'force_onboarding_subscription': instance.forceOnboardingSubscription,
      'upgradeBenefits': instance.upgradeBenefits,
      'upgradeIntro': instance.upgradeIntro,
    };
