import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'artwork_model.g.dart';

@JsonSerializable()
class ArtworkModel extends Equatable {
  final int? height;
  final String? url;
  final int? width;

  const ArtworkModel({
    required this.height,
    required this.url,
    required this.width,
  });

  @override
  List<Object?> get props => [
        height,
        url,
        width,
      ];

  factory ArtworkModel.fromJson(Map<String, dynamic> json) =>
      _$ArtworkModelFromJson(json);

  Map<String, dynamic> toJson() => _$ArtworkModelToJson(this);
}
