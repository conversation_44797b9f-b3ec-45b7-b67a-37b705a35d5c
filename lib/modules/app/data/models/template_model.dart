import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'template_model.g.dart';

@JsonSerializable()
class TemplateModel extends Equatable {
  final String? title;
  final String? description;
  final String? template;

  const TemplateModel({
    required this.title,
    required this.description,
    required this.template,
  });

  @override
  List<Object?> get props => [
        title,
        description,
        template,
      ];

  // Responsible for creating a instance from the json.
  factory TemplateModel.fromJson(Map<String, dynamic> json) =>
      _$TemplateModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$TemplateModelToJson(this);
}
