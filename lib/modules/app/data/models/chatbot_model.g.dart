// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chatbot_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatbotModel _$ChatbotModelFromJson(Map<String, dynamic> json) => ChatbotModel(
      chatbotId: (json['chatbotId'] as num?)?.toInt(),
      name: json['name'] as String?,
      description: json['description'] as String?,
      avatar: json['avatar'] as String?,
      smallAvatar: json['smallAvatar'] as String?,
      mediumAvatar: json['mediumAvatar'] as String?,
      templates: (json['templates'] as List<dynamic>?)
          ?.map((e) => TemplateModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ChatbotModelToJson(ChatbotModel instance) =>
    <String, dynamic>{
      'chatbotId': instance.chatbotId,
      'name': instance.name,
      'description': instance.description,
      'avatar': instance.avatar,
      'smallAvatar': instance.smallAvatar,
      'mediumAvatar': instance.mediumAvatar,
      'templates': instance.templates,
    };
