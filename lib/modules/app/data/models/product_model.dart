import 'package:equatable/equatable.dart';
import 'package:saymee/modules/subscription/data/models/plan_model.dart';
import 'package:json_annotation/json_annotation.dart';

import 'attributes_model.dart';

part 'product_model.g.dart';

@JsonSerializable()
class ProductModel extends Equatable {
  final AttributesModel? attributes;
  final String? href;
  final String? id;
  final String? type;
  final PlanModel? plan;

  const ProductModel({
    required this.attributes,
    required this.href,
    required this.id,
    required this.type,
    required this.plan,
  });

  @override
  List<Object?> get props => [
        attributes,
        href,
        id,
        type,
        plan,
      ];

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProductModelToJson(this);

  ProductModel copyWith({
    PlanModel? plan,
  }) {
    return ProductModel(
      attributes: attributes,
      href: href,
      id: id,
      type: type,
      plan: plan ?? this.plan,
    );
  }
}
