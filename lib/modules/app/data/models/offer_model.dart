import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'offer_model.g.dart';

@JsonSerializable()
class OfferModel extends Equatable {
  final List<dynamic>? assets;
  final String? buyParams;
  final String? currencyCode;
  final int? price;
  final String? priceFormatted;
  final String? recurringSubscriptionPeriod;
  final String? type;

  const OfferModel({
    required this.assets,
    required this.buyParams,
    required this.currencyCode,
    required this.price,
    required this.priceFormatted,
    required this.recurringSubscriptionPeriod,
    required this.type,
  });

  @override
  List<Object?> get props => [
        assets,
        buyParams,
        currencyCode,
        price,
        priceFormatted,
        recurringSubscriptionPeriod,
        type,
      ];

  factory OfferModel.fromJson(Map<String, dynamic> json) =>
      _$OfferModelFromJson(json);

  Map<String, dynamic> toJson() => _$OfferModelToJson(this);
}
