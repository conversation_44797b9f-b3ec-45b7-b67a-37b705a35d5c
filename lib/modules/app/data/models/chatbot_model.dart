import 'package:equatable/equatable.dart';
import 'package:saymee/modules/app/data/models/template_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'chatbot_model.g.dart';

@JsonSerializable()
class ChatbotModel extends Equatable {
  final int? chatbotId;
  final String? name;
  final String? description;
  final String? avatar;
  final String? smallAvatar;
  final String? mediumAvatar;
  final List<TemplateModel>? templates;

  const ChatbotModel({
    required this.chatbotId,
    required this.name,
    required this.description,
    required this.avatar,
    required this.smallAvatar,
    required this.mediumAvatar,
    required this.templates,
  });

  @override
  List<Object?> get props => [
        chatbotId,
        name,
        description,
        avatar,
        smallAvatar,
        mediumAvatar,
        templates,
      ];

  // Responsible for creating a instance from the json.
  factory ChatbotModel.fromJson(Map<String, dynamic> json) =>
      _$ChatbotModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$ChatbotModelToJson(this);
}
