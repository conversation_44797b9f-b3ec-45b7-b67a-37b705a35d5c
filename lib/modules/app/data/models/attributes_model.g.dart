// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attributes_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttributesModel _$AttributesModelFromJson(Map<String, dynamic> json) =>
    AttributesModel(
      description: json['description'] as Map<String, dynamic>?,
      icuLocale: json['icuLocale'] as String?,
      isFamilyShareable: (json['isFamilyShareable'] as num?)?.toInt(),
      isMerchandisedEnabled: (json['isMerchandisedEnabled'] as num?)?.toInt(),
      isMerchandisedVisibleByDefault:
          (json['isMerchandisedVisibleByDefault'] as num?)?.toInt(),
      isSubscription: (json['isSubscription'] as num?)?.toInt(),
      kind: json['kind'] as String?,
      name: json['name'] as String?,
      offerName: json['offerName'] as String?,
      offers: (json['offers'] as List<dynamic>?)
          ?.map((e) => OfferModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      releaseDate: json['releaseDate'] as String?,
      subscriptionFamilyId: json['subscriptionFamilyId'] as String?,
      subscriptionFamilyRank: (json['subscriptionFamilyRank'] as num?)?.toInt(),
      url: json['url'] as String?,
      artwork: json['artwork'] == null
          ? null
          : ArtworkModel.fromJson(json['artwork'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AttributesModelToJson(AttributesModel instance) =>
    <String, dynamic>{
      'description': instance.description,
      'icuLocale': instance.icuLocale,
      'isFamilyShareable': instance.isFamilyShareable,
      'isMerchandisedEnabled': instance.isMerchandisedEnabled,
      'isMerchandisedVisibleByDefault': instance.isMerchandisedVisibleByDefault,
      'isSubscription': instance.isSubscription,
      'kind': instance.kind,
      'name': instance.name,
      'offerName': instance.offerName,
      'offers': instance.offers,
      'releaseDate': instance.releaseDate,
      'subscriptionFamilyId': instance.subscriptionFamilyId,
      'subscriptionFamilyRank': instance.subscriptionFamilyRank,
      'url': instance.url,
      'artwork': instance.artwork,
    };
