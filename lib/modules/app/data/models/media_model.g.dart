// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MediaModel _$MediaModelFromJson(Map<String, dynamic> json) => MediaModel(
      mediaId: (json['mediaId'] as num?)?.toInt(),
      userId: (json['userId'] as num?)?.toInt(),
      type: json['type'] as String?,
      filename: json['filename'] as String?,
      relativePath: json['relativePath'] as String?,
    );

Map<String, dynamic> _$MediaModelToJson(MediaModel instance) =>
    <String, dynamic>{
      'mediaId': instance.mediaId,
      'userId': instance.userId,
      'type': instance.type,
      'filename': instance.filename,
      'relativePath': instance.relativePath,
    };
