import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'media_model.g.dart';

@JsonSerializable()
class MediaModel extends Equatable {
  final int? mediaId;
  final int? userId;
  final String? type;
  final String? filename;
  final String? relativePath;

  const MediaModel({
    required this.mediaId,
    required this.userId,
    required this.type,
    required this.filename,
    required this.relativePath,
  });

  @override
  List<Object?> get props => [
        mediaId,
        userId,
        type,
        filename,
        relativePath,
      ];

  // Responsible for creating a instance from the json.
  factory MediaModel.fromJson(Map<String, dynamic> json) =>
      _$MediaModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$MediaModelToJson(this);
}
