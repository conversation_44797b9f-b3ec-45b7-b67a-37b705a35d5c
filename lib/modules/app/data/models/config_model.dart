import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'config_model.g.dart';

@JsonSerializable()
class ConfigModel extends Equatable {
  final String? ads;
  @J<PERSON><PERSON><PERSON>(name: "ads_delay")
  final String? adsDelay;
  @J<PERSON><PERSON><PERSON>(name: "ads_duration")
  final String? adsDuration;
  @J<PERSON><PERSON><PERSON>(name: "ads_ids")
  final String? adsIds;
  @<PERSON>son<PERSON>ey(name: "ads_interstitial_ids")
  final String? adsInterstitialIds;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ads_interstitial_interval")
  final String? adsInterstitialInterval;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ads_interstitial_order")
  final String? adsInterstitialOrder;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ads_interval")
  final String? adsInterval;
  @J<PERSON><PERSON><PERSON>(name: "ads_order")
  final String? adsOrder;
  @<PERSON><PERSON><PERSON><PERSON>(name: "askforrating")
  final String? askForRating;
  @<PERSON><PERSON><PERSON><PERSON>(name: "auto_show_chatbox")
  final String? autoShowChatbox;
  @<PERSON><PERSON><PERSON><PERSON>(name: "discount_percent")
  final String? discountPercent;
  @J<PERSON><PERSON><PERSON>(name: "support_email")
  final String? supportEmail;
  @Json<PERSON>ey(name: "enable_promo")
  final String? enablePromo;
  final String? message;
  @JsonKey(name: "message_html")
  final String? messageHtml;
  @JsonKey(name: "message_text")
  final String? messageText;
  final String? price;
  @JsonKey(name: "rewarded_order")
  final String? rewardedOrder;
  final String? theme;
  @JsonKey(name: "update_url")
  final String? updateUrl;
  final String? version;
  final String? currentDate;
  @JsonKey(name: 'upgrade_background')
  final String? upgradeBackground;
  @JsonKey(name: 'force_onboarding_subscription')
  final String? forceOnboardingSubscription;
  @JsonKey(name: 'review_url')
  final String? reviewUrl;
  @JsonKey(name: 'upgrade_benefits')
  final String? upgradeBenefits;
  @JsonKey(name: 'upgrade_intro')
  final String? upgradeIntro;

  const ConfigModel({
    required this.ads,
    required this.adsDelay,
    required this.adsDuration,
    required this.adsIds,
    required this.adsInterstitialIds,
    required this.adsInterstitialInterval,
    required this.adsInterstitialOrder,
    required this.adsInterval,
    required this.adsOrder,
    required this.askForRating,
    required this.autoShowChatbox,
    required this.discountPercent,
    required this.supportEmail,
    required this.enablePromo,
    required this.message,
    required this.messageHtml,
    required this.messageText,
    required this.price,
    required this.rewardedOrder,
    required this.theme,
    required this.updateUrl,
    required this.version,
    required this.currentDate,
    required this.upgradeBackground,
    required this.forceOnboardingSubscription,
    required this.reviewUrl,
    required this.upgradeBenefits,
    required this.upgradeIntro,
  });

  @override
  List<Object?> get props => [
        ads,
        adsDelay,
        adsDuration,
        adsIds,
        adsInterstitialIds,
        adsInterstitialInterval,
        adsInterstitialOrder,
        adsInterval,
        adsOrder,
        askForRating,
        autoShowChatbox,
        discountPercent,
        supportEmail,
        enablePromo,
        message,
        messageHtml,
        messageText,
        price,
        rewardedOrder,
        theme,
        updateUrl,
        version,
        currentDate,
        upgradeBackground,
        forceOnboardingSubscription,
        reviewUrl,
        upgradeBenefits,
        upgradeIntro,
      ];

  // Responsible for creating a instance from the json.
  factory ConfigModel.fromJson(Map<String, dynamic> json) =>
      _$ConfigModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$ConfigModelToJson(this);
}
