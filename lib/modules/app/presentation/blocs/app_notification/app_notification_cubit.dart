import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';
import 'package:permission_handler/permission_handler.dart';

class AppNotificationCubit extends Cubit<String> {
  AppNotificationCubit() : super(NotificationStatus.unknown);

  void checkPermission() async {
    final status = await Permission.notification.status;
    final userId = UserModuleHelper.getUserId();

    if (status.name == state) {
      return;
    }
    switch (status) {
      case PermissionStatus.granted:
        if (globalDeviceToken != null && globalDeviceToken!.isNotEmpty) {
          UserModuleHelper.updateDevice(
            userId: userId,
            uuid: globalUuid ?? "",
            platformToken: globalDeviceToken!,
            granted: 1,
          );
        }
        emit(NotificationStatus.granted);
        break;
      case PermissionStatus.denied:
        if (globalDeviceToken != null && globalDeviceToken!.isNotEmpty) {
          UserModuleHelper.updateDevice(
            uuid: globalUuid ?? "",
            platformToken: globalDeviceToken!,
            granted: 0,
            userId: userId,
          );
        }
        emit(NotificationStatus.denied);
        break;
      case PermissionStatus.permanentlyDenied:
        if (globalDeviceToken != null) {
          UserModuleHelper.updateDevice(
            uuid: globalUuid ?? "",
            platformToken: globalDeviceToken!,
            granted: 0,
            userId: userId,
          );
        }
        emit(NotificationStatus.permanentlyDenied);
        break;
      default:
        if (globalDeviceToken != null && globalDeviceToken!.isNotEmpty) {
          UserModuleHelper.updateDevice(
            uuid: globalUuid ?? "",
            platformToken: globalDeviceToken!,
            granted: 0,
            userId: userId,
          );
        }
        emit(NotificationStatus.unknown);
        break;
    }
  }
}
