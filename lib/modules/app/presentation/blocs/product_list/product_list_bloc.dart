import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/core/utils/channel_service.dart';
import 'package:saymee/modules/app/data/models/product_model.dart';

import 'product_list_event.dart';
import 'product_list_state.dart';

class ProductListBloc extends Bloc<ProductListEvent, ProductListState> {
  ProductListBloc() : super(const ProductListState.initial()) {
    on<ProductListEvent>((event, emit) async {
      if (event is ProductGetListRequested) {
        if (state.isRequesting) {
          return;
        }

        emit(ProductListState.pending(products: state.products));

        final result = await ChannelService.loadProducts(event.codes);

        if (result is List) {
          List<ProductModel> products = result.map((e) {
            final eJson = jsonDecode(e);
            ProductModel product =
                ProductModel.fromJson(eJson as Map<String, dynamic>);
            for (var plan in event.plans) {
              if (plan.code == product.attributes?.offerName &&
                  plan.code != null) {
                product = product.copyWith(plan: plan);
                break;
              }
            }
            return product;
          }).toList();

          products.sort((a, b) {
            if (a.plan?.planId != null && b.plan?.planId != null) {
              return a.plan!.planId!.compareTo(b.plan!.planId!);
            }
            return 0;
          });

          emit(ProductListState.fulfilled(products: products));
        } else {
          emit(ProductListState.fulfilled(products: state.products));
        }
      }
    });
  }
}
