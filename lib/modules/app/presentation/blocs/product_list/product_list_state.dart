import 'package:equatable/equatable.dart';
import 'package:saymee/modules/app/data/models/product_model.dart';

final class ProductListState extends Equatable {
  final bool isRequesting;
  final List<ProductModel>? products;

  const ProductListState._({
    this.isRequesting = false,
    this.products,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        products,
      ];

  const ProductListState.initial() : this._();
  const ProductListState.pending({List<ProductModel>? products})
      : this._(
          isRequesting: true,
          products: products,
        );
  const ProductListState.fulfilled({List<ProductModel>? products})
      : this._(products: products);
}
