import 'package:equatable/equatable.dart';
import 'package:saymee/modules/subscription/data/models/plan_model.dart';

sealed class ProductListEvent extends Equatable {
  const ProductListEvent();

  @override
  List<Object?> get props => [];
}

final class ProductGetListRequested extends ProductListEvent {
  final List<String> codes;
  final List<PlanModel> plans;

  const ProductGetListRequested({
    required this.codes,
    required this.plans,
  });
}
