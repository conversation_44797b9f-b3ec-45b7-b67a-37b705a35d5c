import 'package:equatable/equatable.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';

final class ChatbotListState extends Equatable {
  final bool isRequesting;
  final String reason;
  final String code;
  final List<ChatbotModel>? chatbots;

  const ChatbotListState._({
    this.isRequesting = false,
    this.reason = '',
    this.code = '',
    this.chatbots,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        reason,
        code,
        chatbots,
      ];

  const ChatbotListState.initial() : this._();
  const ChatbotListState.pending({
    List<ChatbotModel>? chatbots,
  }) : this._(isRequesting: true, chatbots: chatbots);
  const ChatbotListState.rejected({
    required String code,
    required String reason,
    List<ChatbotModel>? chatbots,
  }) : this._(
          reason: reason,
          code: code,
          chatbots: chatbots,
        );
  const ChatbotListState.fulfilled({List<ChatbotModel>? chatbots})
      : this._(chatbots: chatbots);
}
