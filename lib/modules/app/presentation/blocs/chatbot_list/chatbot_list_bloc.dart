import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';
import 'package:saymee/modules/app/data/repositories/app_repository.dart';

import 'chatbot_list_event.dart';
import 'chatbot_list_state.dart';

class ChatbotListBloc extends Bloc<ChatbotListEvent, ChatbotListState> {
  final AppRepository repository;

  ChatbotListBloc({required this.repository})
      : super(const ChatbotListState.initial()) {
    on<ChatbotListEvent>((event, emit) async {
      if (event is ChatbotGetListRequested) {
        if (state.isRequesting) {
          return;
        }

        emit(ChatbotListState.pending(chatbots: state.chatbots));

        final result = await repository.getChatbots();

        result.fold((l) {
          emit(ChatbotListState.rejected(
            code: l.code,
            reason: l.reason,
            chatbots: state.chatbots,
          ));
        }, (r) {
          final List<ChatbotModel>? chatbots = r['chatbots'];
          emit(ChatbotListState.fulfilled(chatbots: chatbots));
        });
      }
    });
  }
}
