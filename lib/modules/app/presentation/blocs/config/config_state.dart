import 'package:equatable/equatable.dart';
import 'package:saymee/modules/app/data/models/config_model.dart';

final class ConfigState extends Equatable {
  final bool isRequesting;
  final ConfigModel? config;

  const ConfigState._({
    this.isRequesting = false,
    this.config,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        config,
      ];

  const ConfigState.initial() : this._();
  const ConfigState.pending({ConfigModel? config})
      : this._(
          isRequesting: true,
          config: config,
        );
  const ConfigState.fulfilled({ConfigModel? config}) : this._(config: config);
}
