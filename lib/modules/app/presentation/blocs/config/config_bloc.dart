import 'dart:convert';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_keys.dart';
import 'package:saymee/core/helpers/general_helper.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/utils/ads_service.dart';
import 'package:saymee/core/utils/channel_service.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/data/models/config_model.dart';
import 'package:saymee/modules/app/data/repositories/app_repository.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';

import 'config_event.dart';
import 'config_state.dart';

class ConfigBloc extends Bloc<ConfigEvent, ConfigState> {
  final AppRepository repository;

  final preHelper = Modular.get<SharedPreferenceHelper>();

  ConfigBloc({required this.repository}) : super(const ConfigState.initial()) {
    on<ConfigEvent>((event, emit) async {
      if (event is ConfigGetInfoRequested) {
        if (state.isRequesting) {
          return;
        }

        emit(ConfigState.pending(config: state.config));

        final result = await repository.getConfig();

        result.fold((l) {
          emit(ConfigState.fulfilled(config: state.config));
        }, (r) {
          final ConfigModel? config = r['config'];

          emit(ConfigState.fulfilled(config: config));

          final newMessage = config?.message;

          if (newMessage != null) {
            final message = preHelper.getMessage();

            if (message != newMessage) {
              log(config.toString());
              preHelper.setMessage(message: newMessage);
              final jsonData = jsonDecode(newMessage);
              if (jsonData is Map<String, dynamic> &&
                  jsonData['button'] is Map<String, dynamic>) {
                ChannelService.showAlert(
                  title: jsonData['title'] ?? '',
                  message: jsonData['text'] ?? '',
                  buttonTitle: jsonData['button']['title'] ?? 'OK',
                  url: jsonData['button']['link'],
                );
              }
            }
          }

          if (!globalUpdateDialogShowing &&
              config?.version != null &&
              config!.version!.isNotEmpty &&
              config.version!.compareTo(GeneralHelper.appVersion) > 0 &&
              AppKeys.navigatorKey.currentContext != null) {
            globalUpdateDialogShowing = true;

            AppModuleHelper.showUpdateDialog();
          }

          if (config?.adsIds != null && !AdsService.instance.isFirstInit) {
            try {
              String appKey = config!.adsIds!.split('*').first;
              String? adsInterstitialIds =
                  config.adsInterstitialIds?.split('*').last;

              String? adsUnitId;
              List<String>? part = config.adsIds?.split('*');

              if (part != null && part.length > 1) {
                adsUnitId =
                    part.length > 1 ? part[1].split('|').first : part[1];
              }

              if (!AdsService.instance.isInitialized &&
                  !AdsService.instance.initializing) {
                AdsService.instance.init(
                  appKey: appKey,
                  adsInterstitialIds: adsInterstitialIds,
                  adsUnitId: adsUnitId,
                );
              }
            } catch (e) {
              Utils.debugLog(e);
            }
          }
        });
      }
    });
  }
}
