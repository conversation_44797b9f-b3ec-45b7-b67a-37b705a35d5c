import 'package:equatable/equatable.dart';

sealed class UploadFileEvent extends Equatable {
  const UploadFileEvent();

  @override
  List<Object?> get props => [];
}

final class FileUploadRequested extends UploadFileEvent {
  final String path;

  const FileUploadRequested({
    required this.path,
  });
}

final class FileUploadReset extends UploadFileEvent {
  const FileUploadReset();
}

final class CancelUpload extends UploadFileEvent {
  const CancelUpload();
}
