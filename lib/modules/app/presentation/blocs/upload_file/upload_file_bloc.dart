import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/modules/app/data/models/media_model.dart';
import 'package:saymee/modules/app/data/repositories/app_repository.dart';

import 'upload_file_event.dart';
import 'upload_file_state.dart';

class UploadFileBloc extends Bloc<UploadFileEvent, UploadFileState> {
  final AppRepository repository;
  CancelToken cancelToken = CancelToken();

  UploadFileBloc({required this.repository})
      : super(const UploadFileState.initial()) {
    on<UploadFileEvent>((event, emit) async {
      if (event is FileUploadRequested) {
        if (state.isRequesting) {
          return;
        }

        emit(UploadFileState.pending(path: event.path));

        final result = await repository.uploadFile(
          paths: [event.path],
          cancelToken: cancelToken,
        );

        result.fold((l) {
          emit(UploadFileState.rejected(
            code: l.code,
            reason: l.reason,
            path: event.path,
          ));
        }, (r) {
          final List<MediaModel>? medias = r['medias'];
          final bool isCancel = r['isCancel'];
          if (isCancel) {
            emit(const UploadFileState.initial(isCancel: true));
          } else {
            emit(UploadFileState.fulfilled(
              medias: medias,
              path: event.path,
            ));
          }
        });
      } else if (event is FileUploadReset) {
        emit(const UploadFileState.initial());
      } else if (event is CancelUpload) {
        if (state.isRequesting) {
          cancelToken.cancel();
          cancelToken = CancelToken();
        } else {
          emit(const UploadFileState.initial(isCancel: true));
        }
      }
    });
  }
}
