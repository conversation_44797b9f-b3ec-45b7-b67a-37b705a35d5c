import 'package:equatable/equatable.dart';
import 'package:saymee/modules/app/data/models/media_model.dart';

final class UploadFileState extends Equatable {
  final bool isRequesting;
  final String reason;
  final String code;
  final List<MediaModel>? medias;
  final String? path;
  final bool isCancel;

  const UploadFileState._({
    this.isRequesting = false,
    this.reason = '',
    this.code = '',
    this.medias,
    this.path,
    this.isCancel = false,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        reason,
        code,
        medias,
        path,
        isCancel,
      ];

  const UploadFileState.initial({bool isCancel = false})
      : this._(isCancel: isCancel);
  const UploadFileState.pending({required String path})
      : this._(
          isRequesting: true,
          path: path,
        );
  const UploadFileState.rejected({
    required String code,
    required String reason,
    required String path,
  }) : this._(
          reason: reason,
          code: code,
          path: path,
        );
  const UploadFileState.fulfilled({List<MediaModel>? medias, String? path})
      : this._(
          medias: medias,
          path: path,
        );
}
