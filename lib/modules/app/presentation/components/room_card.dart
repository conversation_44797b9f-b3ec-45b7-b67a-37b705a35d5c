import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';

class RoomCard extends StatelessWidget {
  const RoomCard({super.key, required this.room});
  final RoomModel room;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppColors.system0,
      borderRadius: BorderRadius.circular(16),
      child: InkWell(
        onTap: () {
          ChatModuleHelper.goToChatBotPage(
            roomId: room.roomId ?? -1,
            type: RoomType.single,
          );
        },
        customBorder: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: CachedNetworkImage(
                  imageUrl: Utils.convertImageUrl(
                      room.logo ?? room.chatbot?.smallAvatar),
                  width: 40,
                  height: 40,
                  fit: BoxFit.cover,
                  errorWidget: (context, error, stackTrace) {
                    return Container(
                      width: 40,
                      height: 40,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.system1,
                      ),
                    );
                  },
                ),
              ),
              16.horizontalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      room.chatbot?.name ?? '',
                      style: CustomStyle.large.medium
                          .copyWith(color: AppColors.system8),
                    ),
                    if (room.firstMessage != null)
                      Text(
                        room.firstMessage?.content ?? '',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: CustomStyle.medium.regular
                            .copyWith(color: AppColors.system5),
                      ),
                  ],
                ),
              ),
              16.horizontalSpace,
              SvgPicture.asset(AppIcons.iconChevronRight),
            ],
          ),
        ),
      ),
    ).paddingOnly(bottom: 8);
  }
}
