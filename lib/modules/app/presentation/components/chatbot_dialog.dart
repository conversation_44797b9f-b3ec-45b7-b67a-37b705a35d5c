import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';

class ChatbotDialog {
  static void showConfirmCreateChatbot(
    BuildContext context, {
    required ChatbotModel chatbot,
    required VoidCallback newSessionCallback,
    required RoomModel room,
  }) {
    AppDialog.show(
      context: context,
      isHorizontalButton: false,
      buttons: [
        AppButton(
          buttonTitle: context.localization.continueTitle,
          backgroundColor: AppColors.green,
          buttonColor: AppColors.system0,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0D000000), // #0000000D in Flutter
              offset: Offset(0, 14), // (horizontal, vertical)
              blurRadius: 14, // Blur radius
              spreadRadius: 0, // Spread radius
            ),
          ],
          onPressed: () {
            Modular.to.pop();
            ChatModuleHelper.goToChatBotPage(
              roomId: room.roomId ?? -1,
              type: RoomType.single,
            );
          },
        ),
        AppButton(
          buttonTitle: context.localization.newSession,
          onPressed: () {
            Modular.to.pop();
            newSessionCallback.call();
          },
        ),
        AppButton(
          buttonTitle: context.localization.cancel,
          outline: true,
          backgroundColor: AppColors.system0,
          borderColor: AppColors.system3,
          buttonColor: AppColors.system6,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0D000000), // Hexadecimal color #0000000D
              offset: Offset(0, 14), // Horizontal and vertical offset
              blurRadius: 14, // Blur radius
              spreadRadius: 0, // Spread radius
            ),
          ],
          onPressed: () {
            Modular.to.pop();
          },
        ),
      ],
      descriptionWidget: Column(
        children: [
          if (chatbot.mediumAvatar != null && chatbot.mediumAvatar!.isNotEmpty)
            Container(
              constraints: const BoxConstraints(maxWidth: 250, maxHeight: 180),
              child: CachedNetworkImage(
                imageUrl: Utils.convertImageUrl(chatbot.mediumAvatar),
                placeholder: (context, url) => Container(),
                errorWidget: (context, error, stackTrace) =>
                    Image.asset(AppImages.imagePlanBot),
              ),
            ),
          24.verticalSpace,
          Text(
            chatbot.name ?? '',
            style: CustomStyle.h5.smb.copyWith(color: AppColors.system8),
          ),
          4.verticalSpace,
          Text(
            context.localization.confirmCreateRoom(chatbot.name ?? 'Chatbot'),
            textAlign: TextAlign.center,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system5),
          ),
          8.verticalSpace,
        ],
      ),
    );
  }
}
