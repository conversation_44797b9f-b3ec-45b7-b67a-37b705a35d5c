import 'package:flutter/material.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';

class ViewOnBoarding extends StatelessWidget {
  const ViewOnBoarding({super.key, required this.index});

  final int index;

  String getTitle(BuildContext context) {
    switch (index) {
      case 0:
        return "Your Witty Companion";
      case 1:
        return context.localization.onboardingContent1;
      case 2:
        return context.localization.onboardingContent2;
      case 3:
        return context.localization.onboardingContent3;
      default:
        return "";
    }
  }

  String get image {
    switch (index) {
      case 0:
        return AppImages.imageLauncher1;
      // case 1:
      //   return AppImages.imageLauncher2;
      // case 2:
      //   return AppImages.imageLauncher3;
      // case 3:
      //   return AppImages.imageLauncher4;
      default:
        return "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          image,
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Text(
            getTitle(context),
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 57,
              fontWeight: FontWeight.w600,
              color: AppColors.system0,
              fontFamily: 'BarlowCondensed',
              height: 0.85,
              shadows: [
                Shadow(
                  offset: Offset(0, 2), // Similar to the x and y in box-shadow
                  blurRadius: 4, // The blur radius
                  color: Color(0x26000000), // 0x26000000 is #00000026 in ARGB
                ),
              ],
            ),
          ).paddingSymmetric(
            h: 24,
            v: 80 + MediaQuery.of(context).viewPadding.bottom,
          ),
        ),
      ],
    );
  }
}
