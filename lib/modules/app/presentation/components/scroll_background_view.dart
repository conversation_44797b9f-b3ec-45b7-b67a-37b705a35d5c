import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_state.dart';
import 'package:saymee/modules/app/presentation/blocs/header/header_cubit.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';

class ScrollBackgroundView extends StatefulWidget {
  const ScrollBackgroundView({super.key, this.child});

  final Widget? child;

  @override
  State<ScrollBackgroundView> createState() => _ScrollBackgroundViewState();
}

class _ScrollBackgroundViewState extends State<ScrollBackgroundView> {
  final ScrollController _scrollController = ScrollController();
  final headerCubit = Modular.get<HeaderCubit>();

  @override
  void initState() {
    _scrollController.addListener(() {
      headerCubit.updateValue((200 - _scrollController.offset) >= 0
          ? 200 - _scrollController.offset
          : 0);
    });
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<HeaderCubit>.value(value: headerCubit),
      ],
      child: Scaffold(
        backgroundColor: AppColors.system1,
        body: Stack(
          children: [
            BlocBuilder<HeaderCubit, double>(
              builder: (context, imageHeight) {
                return AnnotatedRegion<SystemUiOverlayStyle>(
                  value: const SystemUiOverlayStyle(
                    statusBarColor:
                        Colors.transparent, // Transparent status bar
                    statusBarIconBrightness:
                        Brightness.light, // Light status bar icons
                    statusBarBrightness: Brightness.dark,
                  ),
                  child: BlocBuilder<ConfigBloc, ConfigState>(
                    builder: (context, state) {
                      final upgradeBackground = state.config?.upgradeBackground;
                      if (upgradeBackground != null &&
                          upgradeBackground.isNotEmpty) {
                        return CachedNetworkImage(
                          imageUrl: Utils.convertImageUrl(upgradeBackground),
                          width: double.infinity,
                          height: imageHeight,
                          fit: BoxFit.cover,
                          alignment: Alignment.topCenter,
                          placeholder: (context, url) => Container(),
                          errorWidget: (context, url, error) {
                            return Image.asset(
                              AppImages.imageBg,
                              width: double.infinity,
                              height: imageHeight,
                              alignment: Alignment.topCenter,
                              fit: BoxFit.cover,
                            );
                          },
                        );
                      }

                      return Image.asset(
                        AppImages.imageBg,
                        width: double.infinity,
                        height: imageHeight,
                        alignment: Alignment.topCenter,
                        fit: BoxFit.cover,
                      );
                    },
                  ),
                );
              },
            ),
            SafeArea(
              child: RefreshIndicator(
                backgroundColor: AppColors.system0,
                color: AppColors.blue,
                onRefresh: () async {
                  await AppModuleHelper.getCommonDataWithRefresh();
                  await UserModuleHelper.getUserInfoWithRefresh();
                },
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  controller: _scrollController,
                  child: widget.child,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
