import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/int_extensions.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';
import 'package:saymee/modules/app/presentation/blocs/chatbot_list/chatbot_list_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/chatbot_list/chatbot_list_state.dart';
import 'package:saymee/modules/app/presentation/components/chatbot_dialog.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';

class ListChatView extends StatelessWidget {
  const ListChatView({super.key, this.onTap});

  final void Function(ChatbotModel chatbot)? onTap;

  Widget shimmerView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.localization.chatbotList,
          style: CustomStyle.h5.smb.copyWith(color: AppColors.system8),
        ),
        12.verticalSpace,
        Row(
          children: [
            Expanded(
              child: Container(
                height: 230,
                decoration: BoxDecoration(
                  color: AppColors.system4.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(16),
                ),
              )
                  .animate(
                    onPlay: (controller) => controller.repeat(),
                  )
                  .shimmer(duration: 1.toSeconds()),
            ),
            12.horizontalSpace,
            Expanded(
              child: Container(
                height: 230,
                decoration: BoxDecoration(
                  color: AppColors.system4.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(16),
                ),
              )
                  .animate(
                    onPlay: (controller) => controller.repeat(),
                  )
                  .shimmer(duration: 1.toSeconds()),
            ),
          ],
        ),
        12.verticalSpace,
        Container(
          width: double.infinity,
          height: 100,
          decoration: BoxDecoration(
            color: AppColors.system4.withOpacity(0.4),
            borderRadius: BorderRadius.circular(16),
          ),
        )
            .animate(
              onPlay: (controller) => controller.repeat(),
            )
            .shimmer(duration: 1.toSeconds()),
        12.verticalSpace,
        Container(
          width: double.infinity,
          height: 100,
          decoration: BoxDecoration(
            color: AppColors.system4.withOpacity(0.4),
            borderRadius: BorderRadius.circular(16),
          ),
        )
            .animate(
              onPlay: (controller) => controller.repeat(),
            )
            .shimmer(duration: 1.toSeconds()),
        12.verticalSpace
      ],
    );
  }

  Widget chatbotView(
    BuildContext context, {
    required ChatbotModel chatbot,
    bool isHorizontal = false,
  }) {
    return Container(
      //height: isHorizontal ? 200 : null,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        //border: Border.all(width: 1, color: AppColors.system0),
        gradient: const LinearGradient(
          begin: Alignment.bottomCenter, // Starts from the top
          end: Alignment.topCenter, // Ends at the bottom
          colors: [
            Color(0xFFFEFFFF), // #FEFFFF
            Color(0xFFFCFDFF), // #FCFDFF
          ],
          stops: [0.0, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.52), // #FFFFFF40 (with opacity)
            offset: const Offset(0, 5), // Horizontal and vertical offset
            blurRadius: 14, // Blur radius
            spreadRadius: 0, // Spread radius
            // No native inset shadow support in Flutter
          ),
          // Normal box-shadow
          BoxShadow(
            color: const Color(0xFF6486C5)
                .withOpacity(0.15), // #6486C526 with opacity
            offset: const Offset(0, 6), // Horizontal and vertical offset
            blurRadius: 12, // Blur radius
            spreadRadius: 0, // Spread radius
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: isHorizontal
            ? Column(
                children: [
                  if (chatbot.avatar != null && chatbot.avatar!.isNotEmpty)
                    Container(
                      constraints: const BoxConstraints(maxWidth: 90),
                      child: CachedNetworkImage(
                        imageUrl: Utils.convertImageUrl(chatbot.avatar),
                        errorWidget: (context, error, stackTrace) {
                          return Container();
                        },
                      ),
                    ),
                  6.verticalSpace,
                  Text(
                    chatbot.name ?? '',
                    style:
                        CustomStyle.h5.smb.copyWith(color: AppColors.system8),
                  ),
                  Text(
                    chatbot.description ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: CustomStyle.medium.bold
                        .copyWith(color: AppColors.system6.withOpacity(0.6)),
                  ),
                ],
              ).paddingAll(16)
            : Stack(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              chatbot.name ?? '',
                              style: CustomStyle.h5.smb
                                  .copyWith(color: AppColors.system8),
                            ),
                            Text(
                              chatbot.description ?? '',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: CustomStyle.medium.bold.copyWith(
                                  color: AppColors.system6.withOpacity(0.6)),
                            ),
                          ],
                        ).paddingOnly(
                          top: 24,
                          bottom: 24,
                          left: 16,
                          right: 110,
                        ),
                      ),
                    ],
                  ),
                  Positioned(
                    bottom: 0,
                    top: 0,
                    right: 0,
                    child: Container(
                      constraints:
                          const BoxConstraints(maxWidth: 110, maxHeight: 110),
                      child: CachedNetworkImage(
                        imageUrl: Utils.convertImageUrl(chatbot.avatar),
                        fit: BoxFit.fill,
                        errorWidget: (context, error, stackTrace) {
                          return Image.asset(AppImages.imagePlanBot);
                        },
                      ),
                    ),
                  ),
                ],
              ),
      ),
    ).inkwell(() {
      RoomModel? continueRoom;
      final roomListBloc = Modular.get<RoomListBloc>();
      final roomList =
          roomListBloc.state.oneToOneRooms?.reversed.toList() ?? [];
      for (var room in roomList) {
        if (room.chatbotId == chatbot.chatbotId) {
          continueRoom = room;
          break;
        }
      }

      if (continueRoom != null) {
        ChatbotDialog.showConfirmCreateChatbot(
          context,
          chatbot: chatbot,
          room: continueRoom,
          newSessionCallback: () {
            onTap?.call(chatbot);
          },
        );
      } else {
        onTap?.call(chatbot);
      }
    }, force: true, borderRadius: 16);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatbotListBloc, ChatbotListState>(
      builder: (context, state) {
        if (state.isRequesting) {
          return shimmerView(context);
        }

        final chatbots = state.chatbots;

        if (chatbots == null || chatbots.isEmpty) {
          return Container();
        }

        if (chatbots.length == 1) {
          return chatbotView(context, chatbot: chatbots[0])
              .paddingOnly(bottom: 16);
        }

        final verticalList = chatbots.length > 2 ? chatbots.sublist(2) : [];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.localization.chatbotList,
              style: CustomStyle.h5.smb.copyWith(color: AppColors.system8),
            ),
            12.verticalSpace,
            Row(
              children: [
                Expanded(
                    child: chatbotView(context,
                        chatbot: chatbots[0], isHorizontal: true)),
                12.horizontalSpace,
                Expanded(
                    child: chatbotView(context,
                        chatbot: chatbots[1], isHorizontal: true)),
              ],
            ),
            12.verticalSpace,
            ...verticalList.mapIndexed((index, element) {
              return chatbotView(context, chatbot: element)
                  .paddingOnly(top: index == 0 ? 0 : 12);
            }),
          ],
        ).paddingOnly(bottom: 16);
      },
    );
  }
}
