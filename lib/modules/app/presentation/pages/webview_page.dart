import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({
    super.key,
    required this.url,
    required this.webName,
  });

  final String url;
  final String webName;

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  WebViewController? controller;
  final loadingPercentCubit = LoadingPercentCubit();

  @override
  void initState() {
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(NavigationDelegate(
        onWebResourceError: (error) {},
        onPageStarted: (url) {
          if (mounted) {
            loadingPercentCubit.updatePercent(0);
          }
        },
        onProgress: (progress) {
          if (mounted) {
            loadingPercentCubit.updatePercent(progress);
          }
        },
        onPageFinished: (url) {
          if (mounted) {
            loadingPercentCubit.updatePercent(100);
          }
        },
      ))
      ..loadRequest(
        Uri.parse(widget.url),
      );
    super.initState();
  }

  @override
  void dispose() {
    if (mounted) {
      controller?.loadRequest(Uri.parse('about:blank'));
      controller?.runJavaScript('window.stop();');
      controller?.clearCache();
      controller?.setNavigationDelegate(NavigationDelegate());
      controller = null;
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // Transparent status bar
        statusBarIconBrightness: Brightness.dark, // Light status bar icons
        statusBarBrightness: Brightness.light,
        systemNavigationBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
      ),
      child: Scaffold(
        backgroundColor: AppColors.system1,
        appBar: AppTopBar(
          title: widget.webName,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent, // Transparent status bar
            statusBarIconBrightness: Brightness.dark, // Light status bar icons
            statusBarBrightness: Brightness.light,
            systemNavigationBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.transparent,
          ),
          leading: IconInkwell(
            path: AppIcons.iconChevronLeft,
            color: AppColors.system5,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            onTap: () {
              Modular.to.pop();
            },
          ),
        ),
        body: SafeArea(
          child: Stack(
            children: [
              if (controller != null)
                WebViewWidget(
                  controller: controller!,
                ),
              BlocProvider<LoadingPercentCubit>.value(
                value: loadingPercentCubit,
                child: BlocBuilder<LoadingPercentCubit, int>(
                  builder: (context, percent) {
                    if (percent < 100) {
                      return LinearProgressIndicator(
                        color: AppColors.blue.withOpacity(0.5),
                        backgroundColor: AppColors.system3,
                        value: percent / 100.0,
                      );
                    }

                    return Container();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class LoadingPercentCubit extends Cubit<int> {
  LoadingPercentCubit() : super(0);

  void updatePercent(int percent) {
    emit(percent);
  }
}
