import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_annotated_region.dart';
import 'package:saymee/core/components/app_keep_alive.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/utils/ads_service.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/app/presentation/components/title_navigation_bar/navigation_bar.dart';
import 'package:saymee/modules/app/presentation/components/title_navigation_bar/navigation_bar_item.dart';
import 'package:saymee/modules/app/presentation/pages/home_page.dart';
import 'package:saymee/modules/chat/presentation/pages/chat_page.dart';
import 'package:saymee/modules/chat/presentation/pages/room_page.dart';
import 'package:saymee/modules/user/presentation/pages/profile_page.dart';
import 'package:preload_page_view/preload_page_view.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  late PreloadPageController _pageController;
  final navigatorCubit = NavigatorCubit();
  final configBloc = Modular.get<ConfigBloc>();

  @override
  void initState() {
    _pageController = PreloadPageController();
    _pageController.addListener(() {
      if (_pageController.page != null) {
        navigatorCubit.updateIndex(_pageController.page!.toInt());
      }
    });
    initAds();
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void initAds() {
    final config = configBloc.state.config;
    if (config?.adsIds != null && !AdsService.instance.isFirstInit) {
      try {
        String appKey = config!.adsIds!.split('*').first;
        String? adsInterstitialIds = config.adsInterstitialIds?.split('*').last;

        String? adsUnitId;
        List<String>? part = config.adsIds?.split('*');

        if (part != null && part.length > 1) {
          adsUnitId = part.length > 1 ? part[1].split('|').first : part[1];
        }

        if (!AdsService.instance.isInitialized &&
            !AdsService.instance.initializing) {
          AdsService.instance.init(
            appKey: appKey,
            adsInterstitialIds: adsInterstitialIds,
            adsUnitId: adsUnitId,
          );
        }
      } catch (e) {
        Utils.debugLog(e);
      }
    }
  }

  List<Widget> _pageViews() {
    return [
      const AppKeepAlive(child: HomePage()),
      const AppKeepAlive(child: ChatPage()),
      const AppKeepAlive(child: RoomPage()),
      const AppKeepAlive(child: ProfilePage()),
    ];
  }

  void navigatePageView(int value) {
    _pageController.jumpToPage(value);
  }

  @override
  Widget build(BuildContext context) {
    return AppAnnotatedRegion(
      child: Scaffold(
        backgroundColor: AppColors.system1,
        body: PreloadPageView(
          controller: _pageController,
          preloadPagesCount: 4,
          physics: const NeverScrollableScrollPhysics(),
          children: _pageViews(),
        ),
        bottomNavigationBar: BlocProvider<NavigatorCubit>(
          create: (context) => navigatorCubit,
          child: BlocBuilder<NavigatorCubit, int>(
            builder: (context, currentIndex) {
              return TitledBottomNavigationBar(
                onTap: (value) {
                  navigatePageView(value);
                },
                inactiveColor: AppColors.blue,
                indicatorColor: AppColors.blue,
                indicatorHeight: 4,
                inactiveStripColor: AppColors.system0,
                currentIndex: currentIndex,
                items: [
                  TitledNavigationBarItem(
                    iconPath: AppIcons.iconTabHome,
                    activeIconPath: AppIcons.iconTabActiveHome,
                    title: context.localization.home,
                  ),
                  TitledNavigationBarItem(
                    iconPath: AppIcons.iconTabChat,
                    activeIconPath: AppIcons.iconTabActiveChat,
                    title: context.localization.chats,
                  ),
                  TitledNavigationBarItem(
                    iconPath: AppIcons.iconTabRoom,
                    activeIconPath: AppIcons.iconTabActiveRoom,
                    title: context.localization.room,
                  ),
                  TitledNavigationBarItem(
                    iconPath: AppIcons.iconTabProfile,
                    activeIconPath: AppIcons.iconTabActiveProfile,
                    title: context.localization.profile,
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

class NavigatorCubit extends Cubit<int> {
  NavigatorCubit() : super(0);

  void updateIndex(int index) {
    emit(index);
  }
}
