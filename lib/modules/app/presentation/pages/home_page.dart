import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/modules/app/data/models/chatbot_model.dart';
import 'package:saymee/modules/app/presentation/components/list_chat_view.dart';
import 'package:saymee/modules/app/presentation/components/room_card.dart';
import 'package:saymee/modules/app/presentation/components/scroll_background_view.dart';
import 'package:saymee/modules/chat/data/models/room_model.dart';
import 'package:saymee/modules/chat/data/repositories/chat_repository.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_state.dart';
import 'package:saymee/modules/user/presentation/components/account_info_view.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  void _createChatbotRoom(
    BuildContext context, {
    required ChatbotModel chatbot,
  }) async {
    final chatRepository = Modular.get<ChatRepository>();
    AppLoading.show(context: context);
    final result = await chatRepository.createRoom(
      chatbotId: chatbot.chatbotId ?? -1,
      public: 0,
    );
    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      RoomModel? room = r['room'];
      if (room != null) {
        room = room.copyWith(chatbot: chatbot);
        ChatModuleHelper.goToChatBotPage(
          roomId: room.roomId ?? -1,
          type: RoomType.single,
        );
        ChatModuleHelper.getRoomList();
      }
    });
  }

  Widget listRoom() {
    return BlocBuilder<RoomListBloc, RoomListState>(
      builder: (context, state) {
        if (state.oneToOneRooms == null || state.oneToOneRooms!.isEmpty) {
          return Container();
        }

        final oneToOneRoomsData = state.oneToOneRooms!.reversed.toList();

        final oneToOneRooms = oneToOneRoomsData.length > 5
            ? oneToOneRoomsData.sublist(0, 6)
            : oneToOneRoomsData;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  context.localization.recent,
                  style: CustomStyle.h5.smb.copyWith(color: AppColors.system8),
                ),
                // TextInkwell(
                //   text: 'See all',
                //   color: AppColors.blue,
                //   style: CustomStyle.large.medium,
                //   icon: AppIcons.iconChevronRight,
                // ),
              ],
            ),
            8.verticalSpace,
            ...oneToOneRooms.map(
              (e) => RoomCard(room: e),
            ),
          ],
        ).paddingOnly(bottom: 8);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ScrollBackgroundView(
      child: Column(
        children: [
          16.verticalSpace,
          const AccountInfoView(isHomePage: true),
          ListChatView(
            onTap: (chatbot) => _createChatbotRoom(context, chatbot: chatbot),
          ),
          listRoom(),
        ],
      ).paddingSymmetric(h: 16),
    );
  }
}
