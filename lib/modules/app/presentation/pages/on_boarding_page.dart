import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/modules/app/presentation/components/view_on_boarding.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';

class OnBoardingPage extends StatefulWidget {
  const OnBoardingPage({super.key});

  @override
  State<OnBoardingPage> createState() => _OnBoardingPageState();
}

class _OnBoardingPageState extends State<OnBoardingPage> {
  late PageController pageController;
  int indexPage = 0;

  @override
  void initState() {
    pageController = PageController();
    super.initState();
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    _preloadImages();
    super.didChangeDependencies();
  }

  void _preloadImages() {
    precacheImage(const AssetImage(AppImages.imageLauncher1), context);
    // precacheImage(const AssetImage(AppImages.imageLauncher2), context);
    // precacheImage(const AssetImage(AppImages.imageLauncher3), context);
    // precacheImage(const AssetImage(AppImages.imageLauncher4), context);
  }

  nextPage() {
    // if (indexPage < 3) {
    //   pageController.animateToPage(indexPage + 1,
    //       duration: const Duration(milliseconds: 200), curve: Curves.linear);
    // } else if (indexPage == 3) {
    //   AuthModuleHelper.navigateToSignInPage();
    // }

    AuthModuleHelper.navigateToSignInPage();
  }

  skip() {
    AuthModuleHelper.navigateToSignInPage();
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // Transparent status bar
        statusBarIconBrightness: Brightness.light, // Light status bar icons
        statusBarBrightness: Brightness.dark,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            PageView(
              controller: pageController,
              onPageChanged: (value) {
                setState(() {
                  indexPage = value;
                });
              },
              children: const [
                ViewOnBoarding(index: 0),
                // ViewOnBoarding(index: 1),
                // ViewOnBoarding(index: 2),
                // ViewOnBoarding(index: 3),
              ],
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // SmoothPageIndicator(
                  //   controller: pageController,
                  //   count: 4,
                  //   effect: ExpandingDotsEffect(
                  //     dotColor: AppColors.system0.withOpacity(0.5),
                  //     activeDotColor: AppColors.system0,
                  //     dotHeight: 6,
                  //     dotWidth: 6,
                  //     expansionFactor: 6,
                  //     spacing: 6,
                  //   ),
                  // ),
                  AppButton(
                    buttonTitle: context.localization.continueTitle,
                    backgroundColor: AppColors.system2,
                    titleStyle: CustomStyle.large.smb,
                    buttonColor: AppColors.system8,
                    onPressed: nextPage,
                  ),
                ],
              ).paddingOnly(
                  top: 16,
                  right: 16,
                  left: 16,
                  bottom: 16 + MediaQuery.of(context).viewPadding.bottom),
            ),
            // SafeArea(
            //   child: Row(
            //     mainAxisAlignment: MainAxisAlignment.end,
            //     children: [
            //       AppButton(
            //         buttonTitle: context.localization.skip,
            //         backgroundColor: AppColors.system2,
            //         titleStyle: CustomStyle.medium.smb,
            //         buttonColor: AppColors.system6,
            //         buttonSize: ButtonSize.small,
            //         paddingX: 16,
            //         onPressed: skip,
            //       ),
            //       24.horizontalSpace,
            //     ],
            //   ).paddingOnly(top: 24),
            // ),
          ],
        ),
      ),
    );
  }
}
