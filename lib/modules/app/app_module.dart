import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/modules/app/data/datasources/app_api.dart';
import 'package:saymee/modules/app/data/repositories/app_repository.dart';
import 'package:saymee/modules/app/general/app_module_routes.dart';
import 'package:saymee/modules/app/presentation/blocs/app_banner/app_banner_cubit.dart';
import 'package:saymee/modules/app/presentation/blocs/app_notification/app_notification_cubit.dart';
import 'package:saymee/modules/app/presentation/blocs/chatbot_list/chatbot_list_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/header/header_cubit.dart';
import 'package:saymee/modules/app/presentation/blocs/product_list/product_list_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/upload_file/upload_file_bloc.dart';
import 'package:saymee/modules/app/presentation/pages/main_page.dart';
import 'package:saymee/modules/app/presentation/pages/on_boarding_page.dart';
import 'package:saymee/modules/app/presentation/pages/webview_page.dart';

class AppModule extends Module {
  @override
  // ignore: unnecessary_overrides
  void binds(Injector i) {
    super.binds(i);
  }

  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);

    i.addSingleton(() => AppApi());
    i.addSingleton(() => AppRepository(api: Modular.get<AppApi>()));

    i.addSingleton(
        () => ChatbotListBloc(repository: Modular.get<AppRepository>()));
    i.addSingleton(() => AppNotificationCubit());
    i.addSingleton(() => ConfigBloc(repository: Modular.get<AppRepository>()));
    i.addSingleton(() => ProductListBloc());
    i.add(() => UploadFileBloc(repository: Modular.get<AppRepository>()));
    i.add(() => HeaderCubit());
    i.addSingleton(() => AppBannerCubit());
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);

    r.child(AppModuleRoutes.main, child: (context) => const MainPage());
    r.child(AppModuleRoutes.onboarding,
        child: (context) => const OnBoardingPage());
    r.child(AppModuleRoutes.webview,
        child: (context) => WebViewPage(
              url: r.args.data['url'],
              webName: r.args.data['webName'],
            ));
  }
}
