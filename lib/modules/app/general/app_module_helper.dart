import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/constants/app_keys.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/modules/app/general/app_module_routes.dart';
import 'package:saymee/modules/app/presentation/blocs/chatbot_list/chatbot_list_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/chatbot_list/chatbot_list_event.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_event.dart';
import 'package:saymee/modules/subscription/presentation/blocs/plan_list/plan_list_bloc.dart';
import 'package:saymee/modules/subscription/presentation/blocs/plan_list/plan_list_event.dart';

class AppModuleHelper {
  AppModuleHelper._();

  static void navigateToMainPage() {
    Modular.to.navigate('${AppRoutes.moduleApp}${AppModuleRoutes.main}');
  }

  static void getCommonData() {
    Modular.get<ConfigBloc>().add(ConfigGetInfoRequested());
    Modular.get<ChatbotListBloc>().add(ChatbotGetListRequested());
    Modular.get<PlanListBloc>().add(PlanGetListRequested());
  }

  static Future<void> getCommonDataWithRefresh() async {
    final configBloc = Modular.get<ConfigBloc>();
    Future configStream = configBloc.stream.first;
    configBloc.add(ConfigGetInfoRequested());
    await configStream;

    final chatbotListBloc = Modular.get<ChatbotListBloc>();
    Future chatbotListStream = chatbotListBloc.stream.first;
    chatbotListBloc.add(ChatbotGetListRequested());
    await chatbotListStream;

    final planListBloc = Modular.get<PlanListBloc>();
    final planListStream = planListBloc.stream.first;
    planListBloc.add(PlanGetListRequested());
    await planListStream;
  }

  static void getBackgroundData() {
    Modular.get<ConfigBloc>().add(ConfigGetInfoRequested());
    Modular.get<PlanListBloc>().add(PlanGetListRequested());
  }

  static void showUpdateDialog() {
    AppDialog.show(
      context: AppKeys.navigatorKey.currentContext!,
      buttons: [
        AppButton(
          buttonTitle: "Update",
          titleStyle: CustomStyle.large.smb,
          onPressed: () {
            globalUpdateDialogShowing = false;
            Modular.to.pop();
          },
        ),
      ],
      title: 'Update Available!',
      description:
          'We’ve improved performance and fixed bugs for a smoother experience. Update now for a better app!',
    );
  }

  static void goToWebviewPage({
    required String url,
    required String webName,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleApp}${AppModuleRoutes.webview}',
      arguments: {
        'url': url,
        'webName': webName,
      },
    );
  }
}
