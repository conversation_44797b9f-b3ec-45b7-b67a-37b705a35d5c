import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/network/dio_exceptions.dart';
import 'package:saymee/core/network/dio_failure.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/subscription/data/datasources/subscription_api.dart';
import 'package:saymee/modules/subscription/data/models/plan_model.dart';
import 'package:saymee/modules/subscription/data/models/subscription_model.dart';

class SubscriptionRepository {
  final SubscriptionApi api;

  SubscriptionRepository({required this.api});

  Future<Either<DioFailure, Map<String, dynamic>>> getPlans() async {
    try {
      final response = await api.getPlans();
      Utils.debugLog('getPlans response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final List<PlanModel> plans = mapData['plans'] != null
            ? (mapData['plans'] as List)
                .map((e) => PlanModel.fromJson(e as Map<String, dynamic>))
                .toList()
            : [];

        return Right({
          'statusCode': statusCode,
          'plans': plans,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> getMySubscriptions({
    required CancelToken cancelToken,
  }) async {
    try {
      final response = await api.getMySubscriptions(
        cancelToken: cancelToken,
      );
      Utils.debugLog('getMySubscriptions response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final List<SubscriptionModel> subscriptions =
            mapData['subscriptions'] != null
                ? (mapData['subscriptions'] as List)
                    .map((e) =>
                        SubscriptionModel.fromJson(e as Map<String, dynamic>))
                    .toList()
                : [];

        final SubscriptionModel? activeSubscription =
            mapData['activeSubscription'] != null
                ? SubscriptionModel.fromJson(
                    mapData['activeSubscription'] as Map<String, dynamic>)
                : null;

        return Right({
          'statusCode': statusCode,
          'subscriptions': subscriptions,
          'activeSubscription': activeSubscription,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      if (CancelToken.isCancel(e)) {
        return const Right({
          'isCancel': true,
        });
      }
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> subscribe({
    required int planId,
    required String receipt,
  }) async {
    try {
      final response = await api.subscribe(planId: planId, receipt: receipt);
      Utils.debugLog('subscribe response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final activeSubscription = mapData['activeSubscription'] != null
            ? SubscriptionModel.fromJson(
                mapData['activeSubscription'] as Map<String, dynamic>)
            : null;
        return Right({
          'statusCode': statusCode,
          'activeSubscription': activeSubscription,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }
}
