import 'package:dio/dio.dart';
import 'package:saymee/core/utils/utils.dart';

class SubscriptionApi {
  final dioClient = Utils.dioClient;

  Future<Response> getPlans() async {
    const String url = '/api/v3/subscription/plans';

    try {
      final Response response = await dioClient.get(url);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> getMySubscriptions({
    required CancelToken cancelToken,
  }) async {
    const String url = '/api/v3/subscription/my';

    try {
      final Response response = await dioClient.get(
        url,
        cancelToken: cancelToken,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> subscribe({
    required int planId,
    required String receipt,
  }) async {
    const String url = '/api/v3/subscription/subscribe';

    final params = {
      'planId': planId,
      'receipt': receipt,
      'platform': "Apple",
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }
}
