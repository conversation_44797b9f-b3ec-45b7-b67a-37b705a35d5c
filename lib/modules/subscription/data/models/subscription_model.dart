import 'package:equatable/equatable.dart';
import 'package:saymee/modules/subscription/data/models/plan_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'subscription_model.g.dart';

@JsonSerializable()
class SubscriptionModel extends Equatable {
  final int? subscriptionId;
  final int? userId;
  final int? planId;
  final String? receipt;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? inputTokens;
  final int? outputTokens;
  final int? totalTokens;
  final PlanModel? plan;

  const SubscriptionModel({
    required this.subscriptionId,
    required this.userId,
    required this.planId,
    required this.receipt,
    required this.startDate,
    required this.endDate,
    required this.inputTokens,
    required this.outputTokens,
    required this.totalTokens,
    required this.plan,
  });

  @override
  List<Object?> get props => [
        subscriptionId,
        userId,
        planId,
        receipt,
        startDate,
        endDate,
        inputTokens,
        outputTokens,
        totalTokens,
        plan,
      ];

  // Responsible for creating a instance from the json.
  factory SubscriptionModel.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$SubscriptionModelToJson(this);
}
