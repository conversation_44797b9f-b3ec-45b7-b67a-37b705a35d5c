import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'plan_model.g.dart';

@JsonSerializable()
class PlanModel extends Equatable {
  final int? planId;
  final String? name;
  final String? description;
  final double? price;
  final String? currency;
  final String? code;
  final int? duration;
  final int? tokenCount;

  const PlanModel({
    required this.planId,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.code,
    required this.duration,
    required this.tokenCount,
  });

  @override
  List<Object?> get props => [
        planId,
        name,
        description,
        price,
        currency,
        code,
        duration,
        tokenCount,
      ];

  // Responsible for creating a instance from the json.
  factory PlanModel.fromJson(Map<String, dynamic> json) =>
      _$PlanModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$PlanModelToJson(this);
}
