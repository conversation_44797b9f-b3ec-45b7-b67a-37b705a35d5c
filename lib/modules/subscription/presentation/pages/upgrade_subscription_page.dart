import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/text_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_configs.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/analytic_service.dart';
import 'package:saymee/core/utils/channel_service.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/data/models/product_model.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_state.dart';
import 'package:saymee/modules/app/presentation/blocs/product_list/product_list_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/product_list/product_list_state.dart';
import 'package:saymee/modules/subscription/data/models/subscription_model.dart';
import 'package:saymee/modules/subscription/data/repositories/subscription_repository.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_bloc.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_event.dart';
import 'package:saymee/modules/subscription/presentation/components/subscription_card.dart';

class UpgradeSubscriptionPage extends StatefulWidget {
  const UpgradeSubscriptionPage({super.key, required this.isForce});
  final bool isForce;

  @override
  State<UpgradeSubscriptionPage> createState() =>
      _UpgradeSubscriptionPageState();
}

class _UpgradeSubscriptionPageState extends State<UpgradeSubscriptionPage> {
  ProductModel? selectedProduct;
  final subscriptionRepository = Modular.get<SubscriptionRepository>();
  final productListBloc = Modular.get<ProductListBloc>();
  final mySubscriptionBloc = Modular.get<MySubscriptionBloc>();

  @override
  void initState() {
    getInitProduct();
    super.initState();
  }

  void getInitProduct() {
    final products = productListBloc.state.products ?? [];
    for (var product in products) {
      if (product.plan?.duration == 12) {
        setState(() {
          selectedProduct = product;
        });
        break;
      }
    }
  }

  void _subscribe() async {
    if (selectedProduct != null) {
      AppLoading.show(context: context);
      final receipt =
          await ChannelService.subscribe(selectedProduct?.plan?.code ?? "");
      if (receipt is String) {
        final result = await subscriptionRepository.subscribe(
          planId: selectedProduct?.plan?.planId ?? -1,
          receipt: receipt,
        );

        result.fold((l) {
          _showPaymentErrorDialog(l.reason);
        }, (r) {
          AnalyticService.instance.logEvent(
            event: AFEvent.afSubscribe,
            data: {
              'af_price': selectedProduct?.plan?.price,
              'af_currency': selectedProduct?.plan?.currency,
              'af_revenue': selectedProduct?.plan?.price,
              'plan_id': selectedProduct?.plan?.planId,
            },
          );
          SubscriptionModel? activeSubscription = r['activeSubscription'];
          mySubscriptionBloc.add(SubscriptionUpdateRequested(
              activeSubscription: activeSubscription));
          _showPaymentSuccessDialog();
        });

        if (mounted) {
          AppLoading.turnOff(context: context);
        }
      } else {
        if (mounted) {
          AppLoading.turnOff(context: context);
          _showPaymentErrorDialog(
              'Your payment was unsuccessful. Please retry.');
        }
      }
    }
  }

  void _showPaymentSuccessDialog() {
    AppDialog.show(
      context: context,
      buttons: [
        AppButton(
          buttonTitle: 'Dismiss',
          onPressed: () {
            Modular.to.pop();
            AppModuleHelper.navigateToMainPage();
          },
          outline: true,
          backgroundColor: AppColors.system0,
          borderColor: AppColors.system3,
          buttonColor: AppColors.system6,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0D000000), // Hexadecimal color #0000000D
              offset: Offset(0, 14), // Horizontal and vertical offset
              blurRadius: 14, // Blur radius
              spreadRadius: 0, // Spread radius
            ),
          ],
        ),
      ],
      descriptionWidget: Column(
        children: [
          Image.asset(AppImages.imagePaymentSuccess),
          16.verticalSpace,
          Text(
            'Payment Successfully',
            style: CustomStyle.large.smb.copyWith(color: AppColors.blue),
          ),
          Text(
            'Thank you for using our service',
            textAlign: TextAlign.center,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system6),
          ),
        ],
      ),
    );
  }

  void _showPaymentErrorDialog(String error) {
    AppDialog.show(
      context: context,
      buttons: [
        AppButton(
          buttonTitle: context.localization.cancel,
          onPressed: () {
            Modular.to.pop();
          },
          outline: true,
          backgroundColor: AppColors.system0,
          borderColor: AppColors.system3,
          buttonColor: AppColors.system6,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0D000000), // Hexadecimal color #0000000D
              offset: Offset(0, 14), // Horizontal and vertical offset
              blurRadius: 14, // Blur radius
              spreadRadius: 0, // Spread radius
            ),
          ],
        ),
        AppButton(
          buttonTitle: 'Retry',
          onPressed: () {
            Modular.to.pop();
            _subscribe();
          },
        ),
      ],
      descriptionWidget: Column(
        children: [
          Image.asset(AppImages.imagePaymentFailed),
          16.verticalSpace,
          Text(
            'Payment Failed',
            style: CustomStyle.large.smb.copyWith(color: AppColors.blue),
          ),
          Text(
            error,
            textAlign: TextAlign.center,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system6),
          ),
        ],
      ),
    );
  }

  Widget _header(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              16.horizontalSpace,
              Image.asset(AppImages.imageMiniChatbot),
              4.horizontalSpace,
              Text(
                'Saymee',
                style:
                    CustomStyle.medium.bold.copyWith(color: AppColors.system0),
              ),
            ],
          ),
          TextInkwell(
            text: context.localization.later,
            color: AppColors.system0,
            style: CustomStyle.medium.regular,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            onTap: () {
              if (widget.isForce) {
                AppModuleHelper.navigateToMainPage();
              } else {
                Modular.to.pop();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _upgradeInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            text: context.localization.upgradeTo,
            style: TextStyle(
              fontSize: 50,
              fontWeight: FontWeight.w500,
              color: Colors.white.withOpacity(0.6),
            ),
            children: [
              TextSpan(
                text: context.localization.premium,
                style: const TextStyle(
                  fontSize: 50,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ),
              TextSpan(
                text: context.localization.plan,
              ),
            ],
          ),
        ),
        32.verticalSpace,
        BlocBuilder<ConfigBloc, ConfigState>(
          builder: (context, state) {
            final upgradeIntro =
                state.config?.upgradeIntro ?? AppConfigs.upgradeIntro;
            final upgradeBenefits =
                state.config?.upgradeBenefits ?? AppConfigs.upgradeBenefits;

            final List<String> lines = upgradeBenefits.split('\r\n');

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  upgradeIntro,
                  style: CustomStyle.medium.regular
                      .copyWith(color: AppColors.system0),
                ),
                8.verticalSpace,
                ...lines.map(
                  (e) => Text(
                    '  •  $e',
                    style: CustomStyle.medium.regular
                        .copyWith(color: AppColors.system0),
                  ).paddingOnly(bottom: 8),
                ),
              ],
            );
          },
        ),
      ],
    ).paddingOnly(bottom: 24);
  }

  Widget _subCards(BuildContext context) {
    return BlocBuilder<ProductListBloc, ProductListState>(
      builder: (context, state) {
        final products = state.products ?? [];
        return Column(
          children: [
            ...products.map(
              (e) => SubscriptionCard(
                product: e,
                isSelected: e == selectedProduct,
                onTap: () {
                  setState(() {
                    selectedProduct = e;
                  });
                },
              ),
            ),
            4.verticalSpace,
            AppButton(
              backgroundColor: AppColors.system0,
              buttonTitle: context.localization.continueTitle,
              buttonColor: AppColors.system8,
              titleStyle: CustomStyle.large.smb,
              onPressed: _subscribe,
            ),
            16.verticalSpace,
            AppButton(
              backgroundColor: AppColors.system0.withOpacity(0.2),
              buttonTitle: context.localization.restorePurchases,
              buttonColor: AppColors.system0,
              titleStyle: CustomStyle.large.bold,
              onPressed: () => ChannelService.restore(),
            ),
          ],
        );
      },
    ).paddingOnly(bottom: 16);
  }

  Widget _policyView(BuildContext context) {
    return Column(
      children: [
        8.verticalSpace,
        TextInkwell(
          onTap: () {
            AppModuleHelper.goToWebviewPage(
              url: AppConfigs.privacyPolicyUrl,
              webName: context.localization.privacyPolicy,
            );
          },
          text: context.localization.privacyPolicy,
          color: AppColors.system0,
          style: CustomStyle.small.regular.copyWith(
            color: AppColors.system0,
            decoration: TextDecoration.underline,
            decorationColor: AppColors.system0,
          ),
        ),
        4.verticalSpace,
        TextInkwell(
          onTap: () {
            AppModuleHelper.goToWebviewPage(
              url: AppConfigs.termsAndConditionsUrl,
              webName: context.localization.termAndConditions,
            );
          },
          text: context.localization.termAndConditions,
          color: AppColors.system0,
          style: CustomStyle.small.regular.copyWith(
            color: AppColors.system0,
            decoration: TextDecoration.underline,
            decorationColor: AppColors.system0,
          ),
        ),
        16.verticalSpace,
        _dotContent(context.localization.subContent1),
        _dotContent(context.localization.subContent2),
        _dotContent(context.localization.subContent3),
        _dotContent(context.localization.subContent4),
        _dotContent(context.localization.subContent5),
      ],
    );
  }

  Widget _dotContent(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '•  ',
          style: CustomStyle.small.regular
              .copyWith(color: AppColors.system0.withOpacity(0.7)),
        ),
        Expanded(
          child: Text(
            text,
            style: CustomStyle.small.regular
                .copyWith(color: AppColors.system0.withOpacity(0.7)),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConfigBloc, ConfigState>(
      builder: (context, state) {
        final upgradeBackground = state.config?.upgradeBackground;
        return GestureDetector(
          onTap: () => Utils.hideKeyboard(),
          child: AnnotatedRegion<SystemUiOverlayStyle>(
            value: const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent, // Transparent status bar
              statusBarIconBrightness:
                  Brightness.light, // Light status bar icons
              statusBarBrightness: Brightness.dark,
            ),
            child: Scaffold(
              body: Stack(
                children: [
                  (upgradeBackground == null || upgradeBackground.isEmpty)
                      ? Image.asset(
                          AppImages.imageSubBg,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          alignment: Alignment.topCenter,
                        )
                      : CachedNetworkImage(
                          imageUrl: Utils.convertImageUrl(upgradeBackground),
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          alignment: Alignment.topCenter,
                          placeholder: (context, url) => Container(),
                          errorWidget: (context, url, error) {
                            return Image.asset(
                              AppImages.imageSubBg,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                              alignment: Alignment.topCenter,
                            );
                          },
                        ),
                  SafeArea(
                    child: Column(
                      children: [
                        _header(context),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                _upgradeInfo(context),
                                _subCards(context),
                                _policyView(context),
                              ],
                            ).paddingSymmetric(h: 16),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
