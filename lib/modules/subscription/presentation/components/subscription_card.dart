import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/data/models/product_model.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_state.dart';

class SubscriptionCard extends StatelessWidget {
  const SubscriptionCard({
    super.key,
    required this.product,
    this.isSelected = false,
    this.onTap,
  });

  final bool isSelected;
  final ProductModel product;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(
              sigmaX: isSelected ? 0 : 15.0, sigmaY: isSelected ? 0 : 15.0),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.system2
                  : Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.plan?.name ?? (product.attributes?.name ?? ""),
                        style: CustomStyle.h5.smb.copyWith(
                          color: isSelected
                              ? AppColors.system8
                              : AppColors.system0,
                        ),
                      ),
                      Text(
                        product.plan?.tokenCount != null
                            ? '${Utils.formaCurrency(product.plan?.tokenCount)} ${context.localization.credits}'
                            : (product.plan?.description ?? ''),
                        style: CustomStyle.medium.regular.copyWith(
                            color: isSelected
                                ? AppColors.system6
                                : AppColors.system3),
                      ),
                      Text(
                        '${context.localization.forTitle} ${product.plan?.duration} ${context.localization.months}',
                        style: CustomStyle.medium.regular.copyWith(
                            color: isSelected
                                ? AppColors.system6
                                : AppColors.system3),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        product.attributes?.offers?.first.priceFormatted ?? '',
                        style: CustomStyle.h5.smb.copyWith(
                          color: isSelected
                              ? AppColors.system8
                              : AppColors.system0,
                        ),
                      ),
                      BlocBuilder<ConfigBloc, ConfigState>(
                        builder: (context, state) {
                          final discountPercent = state.config?.discountPercent;
                          if (discountPercent == null ||
                              discountPercent == "0") {
                            return Container();
                          }

                          return Text(
                            '-$discountPercent%',
                            style: CustomStyle.small.regular.copyWith(
                              color: isSelected
                                  ? AppColors.system5
                                  : AppColors.system4,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ).paddingOnly(bottom: 12);
  }
}
