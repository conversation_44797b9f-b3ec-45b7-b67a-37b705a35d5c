import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/modules/app/presentation/blocs/product_list/product_list_bloc.dart';
import 'package:saymee/modules/app/presentation/blocs/product_list/product_list_event.dart';
import 'package:saymee/modules/subscription/data/models/plan_model.dart';
import 'package:saymee/modules/subscription/data/repositories/subscription_repository.dart';

import 'plan_list_event.dart';
import 'plan_list_state.dart';

class PlanListBloc extends Bloc<PlanListEvent, PlanListState> {
  final SubscriptionRepository repository;

  List<String> planCodes = [];

  PlanListBloc({required this.repository})
      : super(const PlanListState.initial()) {
    on<PlanListEvent>((event, emit) async {
      if (event is PlanGetListRequested) {
        if (state.isRequesting) {
          return;
        }

        emit(PlanListState.pending(plans: state.plans));

        final result = await repository.getPlans();

        result.fold((l) {
          emit(PlanListState.rejected(
            code: l.code,
            reason: l.reason,
            plans: state.plans,
          ));
        }, (r) {
          final List<PlanModel>? plans = r['plans'];
          emit(PlanListState.fulfilled(plans: plans));

          if (plans != null && plans.isNotEmpty && Platform.isIOS) {
            for (var element in plans) {
              if (element.code != null && element.code!.isNotEmpty) {
                planCodes.add(element.code!);
              }
            }

            Modular.get<ProductListBloc>()
                .add(ProductGetListRequested(codes: planCodes, plans: plans));
          }
        });
      }
    });
  }
}
