import 'package:equatable/equatable.dart';
import 'package:saymee/modules/subscription/data/models/plan_model.dart';

final class PlanListState extends Equatable {
  final bool isRequesting;
  final String reason;
  final String code;
  final List<PlanModel>? plans;

  const PlanListState._({
    this.isRequesting = false,
    this.reason = '',
    this.code = '',
    this.plans,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        reason,
        code,
        plans,
      ];

  const PlanListState.initial() : this._();
  const PlanListState.pending({List<PlanModel>? plans})
      : this._(
          isRequesting: true,
          plans: plans,
        );
  const PlanListState.rejected({
    required String code,
    required String reason,
    List<PlanModel>? plans,
  }) : this._(
          reason: reason,
          code: code,
          plans: plans,
        );
  const PlanListState.fulfilled({List<PlanModel>? plans})
      : this._(plans: plans);
}
