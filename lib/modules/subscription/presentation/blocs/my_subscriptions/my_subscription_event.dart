import 'package:equatable/equatable.dart';
import 'package:saymee/modules/subscription/data/models/subscription_model.dart';

sealed class MySubscriptionEvent extends Equatable {
  const MySubscriptionEvent();

  @override
  List<Object?> get props => [];
}

final class SubscriptionGetListRequested extends MySubscriptionEvent {}

final class SubscriptionResetRequested extends MySubscriptionEvent {}

final class SubscriptionUpdateRequested extends MySubscriptionEvent {
  final SubscriptionModel? activeSubscription;

  const SubscriptionUpdateRequested({required this.activeSubscription});
}
