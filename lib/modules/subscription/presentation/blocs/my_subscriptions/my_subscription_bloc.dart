import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:saymee/modules/subscription/data/models/subscription_model.dart';
import 'package:saymee/modules/subscription/data/repositories/subscription_repository.dart';

import 'my_subscription_event.dart';
import 'my_subscription_state.dart';

class MySubscriptionBloc
    extends Bloc<MySubscriptionEvent, MySubscriptionState> {
  final SubscriptionRepository repository;
  CancelToken cancelToken = CancelToken();

  MySubscriptionBloc({required this.repository})
      : super(const MySubscriptionState.initial()) {
    on<MySubscriptionEvent>((event, emit) async {
      if (event is SubscriptionGetListRequested) {
        if (state.isRequesting) {
          return;
        }

        emit(MySubscriptionState.pending(
          subscriptions: state.subscriptions,
          activeSubscription: state.activeSubscription,
        ));

        final result = await repository.getMySubscriptions(
          cancelToken: cancelToken,
        );

        result.fold((l) {
          emit(MySubscriptionState.rejected(
            code: l.code,
            reason: l.reason,
            subscriptions: state.subscriptions,
            activeSubscription: state.activeSubscription,
          ));
        }, (r) {
          final List<SubscriptionModel>? subscriptions = r['subscriptions'];
          final SubscriptionModel? activeSubscription = r['activeSubscription'];
          bool? isCancel = r['isCancel'];
          if (isCancel == true) {
            emit(const MySubscriptionState.initial());
          } else {
            emit(MySubscriptionState.fulfilled(
              subscriptions: subscriptions,
              activeSubscription: activeSubscription,
            ));
          }
        });
      } else if (event is SubscriptionResetRequested) {
        if (state.isRequesting) {
          cancelToken.cancel();
          cancelToken = CancelToken();
        } else {
          emit(const MySubscriptionState.initial());
        }
      } else if (event is SubscriptionUpdateRequested) {
        emit(MySubscriptionState.fulfilled(
          subscriptions: state.subscriptions,
          activeSubscription: event.activeSubscription,
        ));
      }
    });
  }
}
