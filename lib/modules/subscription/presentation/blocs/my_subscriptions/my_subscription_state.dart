import 'package:equatable/equatable.dart';
import 'package:saymee/modules/subscription/data/models/subscription_model.dart';

final class MySubscriptionState extends Equatable {
  final bool isRequesting;
  final String reason;
  final String code;
  final List<SubscriptionModel>? subscriptions;
  final SubscriptionModel? activeSubscription;

  const MySubscriptionState._({
    this.isRequesting = false,
    this.reason = '',
    this.code = '',
    this.subscriptions,
    this.activeSubscription,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        reason,
        code,
        subscriptions,
        activeSubscription,
      ];

  const MySubscriptionState.initial() : this._();
  const MySubscriptionState.pending({
    List<SubscriptionModel>? subscriptions,
    SubscriptionModel? activeSubscription,
  }) : this._(
          isRequesting: true,
          subscriptions: subscriptions,
          activeSubscription: activeSubscription,
        );
  const MySubscriptionState.rejected({
    required String code,
    required String reason,
    List<SubscriptionModel>? subscriptions,
    SubscriptionModel? activeSubscription,
  }) : this._(
          reason: reason,
          code: code,
          subscriptions: subscriptions,
          activeSubscription: activeSubscription,
        );
  const MySubscriptionState.fulfilled({
    List<SubscriptionModel>? subscriptions,
    SubscriptionModel? activeSubscription,
  }) : this._(
          subscriptions: subscriptions,
          activeSubscription: activeSubscription,
        );
}
