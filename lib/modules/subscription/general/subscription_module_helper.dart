import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/modules/subscription/general/subscription_module_routes.dart';

class SubscriptionModuleHelper {
  SubscriptionModuleHelper._();

  static void _pushedIfNotCurrent(String routeName, {Object? arguments}) {
    if (Modular.to.path != routeName) {
      Modular.to.pushNamed(routeName, arguments: arguments);
    }
  }

  static void goToUpgradeSubscriptionPage({bool isForce = false}) {
    _pushedIfNotCurrent(
      '${AppRoutes.moduleSub}${SubscriptionModuleRoutes.upgrade}',
      arguments: {
        'isForce': isForce,
      },
    );
  }

  static void navigateToUpgradeSubscriptionPage({bool isForce = false}) {
    Modular.to.navigate(
      '${AppRoutes.moduleSub}${SubscriptionModuleRoutes.upgrade}',
      arguments: {
        'isForce': isForce,
      },
    );
  }
}
