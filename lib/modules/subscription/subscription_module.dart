import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/modules/subscription/data/datasources/subscription_api.dart';
import 'package:saymee/modules/subscription/data/repositories/subscription_repository.dart';
import 'package:saymee/modules/subscription/general/subscription_module_routes.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_bloc.dart';
import 'package:saymee/modules/subscription/presentation/blocs/plan_list/plan_list_bloc.dart';
import 'package:saymee/modules/subscription/presentation/pages/upgrade_subscription_page.dart';

class SubscriptionModule extends Module {
  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);

    i.addSingleton(() => SubscriptionApi());
    i.addSingleton(
        () => SubscriptionRepository(api: Modular.get<SubscriptionApi>()));
    i.addSingleton(
        () => PlanListBloc(repository: Modular.get<SubscriptionRepository>()));
    i.addSingleton(() =>
        MySubscriptionBloc(repository: Modular.get<SubscriptionRepository>()));
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);

    r.child(
      SubscriptionModuleRoutes.upgrade,
      child: (context) => UpgradeSubscriptionPage(
        isForce: r.args.data['isForce'],
      ),
    );
  }
}
