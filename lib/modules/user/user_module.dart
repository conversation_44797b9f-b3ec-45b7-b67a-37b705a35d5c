import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/modules/user/data/datasources/user_api.dart';
import 'package:saymee/modules/user/data/repositories/user_repository.dart';
import 'package:saymee/modules/user/general/user_module_routes.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';
import 'package:saymee/modules/user/presentation/pages/account_info_page.dart';

class UserModule extends Module {
  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);

    i.addSingleton(() => UserApi());
    i.addSingleton(() => UserRepository(api: Modular.get<UserApi>()));
    i.addSingleton(
        () => UserInfoBloc(repository: Modular.get<UserRepository>()));
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);

    r.child(
      UserModuleRoutes.accountInfo,
      child: (context) => const AccountInfoPage(),
    );
  }
}
