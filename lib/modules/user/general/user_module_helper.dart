import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_event.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_bloc.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_event.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';
import 'package:saymee/modules/user/data/repositories/user_repository.dart';
import 'package:saymee/modules/user/general/user_module_routes.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_event.dart';

class UserModuleHelper {
  UserModuleHelper._();

  static void getUserData({UserModel? user}) {
    final userInfoBloc = Modular.get<UserInfoBloc>();
    final mySubscriptionBloc = Modular.get<MySubscriptionBloc>();
    final roomListBloc = Modular.get<RoomListBloc>();
    if (AuthModuleHelper.checkAccessTokenValid()) {
      if (user != null) {
        userInfoBloc.add(UserSetRequest(user: user));
      } else {
        userInfoBloc.add(UserGetInfoRequested());
      }
      mySubscriptionBloc.add(SubscriptionGetListRequested());
      roomListBloc.add(RoomGetListRequested());
    }
  }

  static Future<void> getUserInfoWithRefresh() async {
    final userInfoBloc = Modular.get<UserInfoBloc>();
    final mySubscriptionBloc = Modular.get<MySubscriptionBloc>();
    final roomListBloc = Modular.get<RoomListBloc>();
    if (AuthModuleHelper.checkAccessTokenValid()) {
      final userInfoStream = userInfoBloc.stream.first;
      userInfoBloc.add(UserGetInfoRequested());
      await userInfoStream;
      final mySubscriptionStream = mySubscriptionBloc.stream.first;
      mySubscriptionBloc.add(SubscriptionGetListRequested());
      await mySubscriptionStream;
      final roomListStream = roomListBloc.stream.first;
      roomListBloc.add(RoomGetListRequested());
      await roomListStream;
    }
  }

  static int? getUserId() {
    final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();
    return sharedPreferenceHelper.getUserId();
  }

  static void updateDevice({
    int? userId,
    required String uuid,
    required String platformToken,
    required int granted,
  }) {
    final userRepository = Modular.get<UserRepository>();

    userRepository.updateDevice(
      userId: userId,
      uuid: uuid,
      platformToken: platformToken,
      granted: granted,
    );
  }

  static void goToAccountInfoPage() {
    Modular.to
        .pushNamed('${AppRoutes.moduleUser}${UserModuleRoutes.accountInfo}');
  }
}
