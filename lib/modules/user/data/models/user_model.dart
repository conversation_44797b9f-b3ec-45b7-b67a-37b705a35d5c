import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends Equatable {
  final int? userId;
  final String? fullname;
  final String? username;
  final String? type;
  final String? email;
  final String? phone;
  final String? avatar;
  final String? role;
  final String? status;
  final String? externalUserId;

  const UserModel({
    required this.userId,
    required this.fullname,
    required this.username,
    required this.type,
    required this.email,
    required this.phone,
    required this.avatar,
    required this.role,
    required this.status,
    required this.externalUserId,
  });

  @override
  List<Object?> get props => [
        userId,
        fullname,
        username,
        type,
        email,
        phone,
        avatar,
        role,
        status,
        externalUserId,
      ];

  // Responsible for creating a instance from the json.
  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  // Responsible for converting the map to json.
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? fullname,
    String? username,
    String? avatar,
  }) {
    return UserModel(
      userId: userId,
      fullname: fullname ?? this.fullname,
      username: username ?? this.username,
      type: type,
      email: email,
      phone: phone,
      avatar: avatar ?? this.avatar,
      role: role,
      status: status,
      externalUserId: externalUserId,
    );
  }
}
