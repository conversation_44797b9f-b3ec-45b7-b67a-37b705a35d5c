import 'dart:io';

import 'package:dio/dio.dart';
import 'package:saymee/core/utils/utils.dart';

class UserApi {
  final dioClient = Utils.dioClient;

  Future<Response> getProfile({
    required CancelToken cancelToken,
  }) async {
    const String url = '/api/v3/user/profile';

    try {
      final Response response = await dioClient.get(
        url,
        cancelToken: cancelToken,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }

  void updateDevice({
    int? userId,
    required String uuid,
    required String platformToken,
    required int granted,
  }) async {
    const String url = '/api/v3/user/updateDevice';

    final params = {
      'userId': userId,
      'uuid': uuid,
      'platform': Platform.isIOS ? 'iOS' : 'Android',
      'platformToken': platformToken,
      'granted': granted,
    };

    Utils.debugLogSuccess(params);

    try {
      await dioClient.post(url, data: params);
    } on DioException catch (e) {
      Utils.debugLog(e);
    }
  }

  Future<Response> updateProfile({
    required String fullname,
    required String username,
    String? avatar,
  }) async {
    const String url = '/api/v3/user/update';

    Map<String, dynamic> params = {
      'fullname': fullname,
      'username': username,
    };

    if (avatar != null) {
      params['avatar'] = avatar;
    }

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> deleteAccount({
    required int userId,
    required String type,
    String? password,
    String? externalUserId,
  }) async {
    const String url = '/api/v3/user/delete';

    final params = {
      'userId': userId,
      'type': type,
      'password': password,
      'externalUserId': externalUserId,
    };

    try {
      final Response response = await dioClient.post(url, data: params);

      return response;
    } catch (e) {
      rethrow;
    }
  }
}
