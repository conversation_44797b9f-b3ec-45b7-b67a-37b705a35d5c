import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/network/dio_exceptions.dart';
import 'package:saymee/core/network/dio_failure.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/user/data/datasources/user_api.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';

class UserRepository {
  final UserApi api;

  UserRepository({required this.api});

  Future<Either<DioFailure, Map<String, dynamic>>> getUserInfo(
      {required CancelToken cancelToken}) async {
    try {
      final response = await api.getProfile(
        cancelToken: cancelToken,
      );
      Utils.debugLog('getProfile response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final UserModel? user = mapData['user'] != null
            ? UserModel.fromJson(mapData['user'])
            : null;

        return Right({
          'statusCode': statusCode,
          'user': user,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      if (CancelToken.isCancel(e)) {
        return const Right({
          'isCancel': true,
        });
      }
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  void updateDevice({
    int? userId,
    required String uuid,
    required String platformToken,
    required int granted,
  }) async {
    try {
      api.updateDevice(
        userId: userId,
        uuid: uuid,
        platformToken: platformToken,
        granted: granted,
      );
      // ignore: unused_catch_clause
    } catch (e) {
      //
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> updateProfile({
    required String fullname,
    required String username,
    String? avatar,
  }) async {
    try {
      final response = await api.updateProfile(
        fullname: fullname,
        username: username,
        avatar: avatar,
      );
      Utils.debugLog('updateProfile response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> deleteAccount({
    required int userId,
    required String type,
    String? password,
    String? externalUserId,
  }) async {
    try {
      final response = await api.deleteAccount(
        userId: userId,
        type: type,
        password: password,
        externalUserId: externalUserId,
      );
      Utils.debugLog('deleteAccount response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      Utils.debugLog(e.message);
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }
}
