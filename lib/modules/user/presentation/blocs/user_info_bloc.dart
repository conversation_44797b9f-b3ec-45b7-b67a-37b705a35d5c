import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/helpers/web_socket_helper.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/modules/app/presentation/blocs/app_notification/app_notification_cubit.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';
import 'package:saymee/modules/user/data/repositories/user_repository.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';

import 'user_info_event.dart';
import 'user_info_state.dart';

class UserInfoBloc extends Bloc<UserInfoEvent, UserInfoState> {
  final UserRepository repository;
  String tag = 'UserInfoBloc';
  WebSocketHelper webSocketHelper = WebSocketHelper();
  int? userId;
  CancelToken cancelToken = CancelToken();

  UserInfoBloc({required this.repository})
      : super(const UserInfoState.initial()) {
    on<UserInfoEvent>((event, emit) async {
      if (event is UserGetInfoRequested) {
        if (state.isRequesting) {
          return;
        }

        emit(UserInfoState.pending(user: state.user));

        final result = await repository.getUserInfo(
          cancelToken: cancelToken,
        );

        result.fold((l) {
          emit(UserInfoState.rejected(
            code: l.code,
            reason: l.reason,
            user: state.user,
          ));
        }, (r) {
          final UserModel? user = r['user'];
          final cubit = Modular.get<AppNotificationCubit>();
          bool? isCancel = r['isCancel'];
          if (isCancel == true) {
            emit(const UserInfoState.initial());
            userId = null;
          } else {
            if (userId == null && globalDeviceToken != null) {
              userId = user?.userId;
              UserModuleHelper.updateDevice(
                userId: userId,
                uuid: globalUuid ?? "",
                platformToken: globalDeviceToken!,
                granted: cubit.state == NotificationStatus.granted ? 1 : 0,
              );
            }

            final accessToken =
                Modular.get<SharedPreferenceHelper>().getAccessToken();
            if (user != null && webSocketHelper.isUserNotConnect) {
              webSocketHelper.sendUserLoginEvent(
                userId: user.userId ?? -1,
                token: accessToken ?? "",
              );
            }
            emit(UserInfoState.fulfilled(user: user));
          }
        });
      } else if (event is UserResetInfoRequested) {
        if (state.isRequesting) {
          cancelToken.cancel();
          cancelToken = CancelToken();
        } else {
          emit(const UserInfoState.initial());
          userId = null;
        }
      } else if (event is UserUpdateRequested) {
        UserModel? newUser = state.user?.copyWith(
          fullname: event.fullname,
          username: event.username,
          avatar: event.avatar,
        );

        emit(UserInfoState.fulfilled(user: newUser));
      } else if (event is UserSetRequest) {
        final user = event.user;
        final cubit = Modular.get<AppNotificationCubit>();
        if (userId == null && globalDeviceToken != null) {
          userId = user.userId;
          UserModuleHelper.updateDevice(
            userId: userId,
            uuid: globalUuid ?? "",
            platformToken: globalDeviceToken!,
            granted: cubit.state == NotificationStatus.granted ? 1 : 0,
          );
        }

        final accessToken =
            Modular.get<SharedPreferenceHelper>().getAccessToken();
        if (webSocketHelper.isUserNotConnect) {
          webSocketHelper.sendUserLoginEvent(
            userId: user.userId ?? -1,
            token: accessToken ?? "",
          );
        }
        emit(UserInfoState.fulfilled(user: user));
      }
    });
  }
}
