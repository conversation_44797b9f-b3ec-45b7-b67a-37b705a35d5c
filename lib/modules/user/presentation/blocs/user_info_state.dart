import 'package:equatable/equatable.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';

final class UserInfoState extends Equatable {
  final bool isRequesting;
  final String reason;
  final String code;
  final UserModel? user;

  const UserInfoState._({
    this.isRequesting = false,
    this.reason = '',
    this.code = '',
    this.user,
  });

  @override
  List<Object?> get props => [
        isRequesting,
        reason,
        code,
        user,
      ];

  const UserInfoState.initial() : this._();
  const UserInfoState.pending({UserModel? user})
      : this._(isRequesting: true, user: user);
  const UserInfoState.rejected({
    required String code,
    required String reason,
    UserModel? user,
  }) : this._(
          reason: reason,
          code: code,
          user: user,
        );
  const UserInfoState.fulfilled({UserModel? user}) : this._(user: user);
}
