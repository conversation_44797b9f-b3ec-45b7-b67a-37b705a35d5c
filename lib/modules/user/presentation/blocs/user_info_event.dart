import 'package:equatable/equatable.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';

sealed class UserInfoEvent extends Equatable {
  const UserInfoEvent();

  @override
  List<Object?> get props => [];
}

final class UserGetInfoRequested extends UserInfoEvent {}

final class UserResetInfoRequested extends UserInfoEvent {}

final class UserUpdateRequested extends UserInfoEvent {
  final String? fullname;
  final String? username;
  final String? avatar;

  const UserUpdateRequested({
    required this.fullname,
    required this.username,
    required this.avatar,
  });
}

final class UserSetRequest extends UserInfoEvent {
  final UserModel user;

  const UserSetRequest({required this.user});
}
