import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';

class SettingCard extends StatelessWidget {
  const SettingCard({super.key, this.title, required this.list});

  final List<KeyIconSetting> list;
  final String? title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Text(
            title!,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system5),
          ).paddingOnly(bottom: 8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.system0,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              ...list.mapIndexed((index, e) {
                return Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: e.onTap,
                    customBorder: RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: index == 0
                            ? const Radius.circular(16)
                            : Radius.zero,
                        topRight: index == 0
                            ? const Radius.circular(16)
                            : Radius.zero,
                        bottomLeft: index == list.length - 1
                            ? const Radius.circular(16)
                            : Radius.zero,
                        bottomRight: index == list.length - 1
                            ? const Radius.circular(16)
                            : Radius.zero,
                      ),
                    ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        border: index == list.length - 1
                            ? null
                            : const Border(
                                bottom: BorderSide(
                                    width: 1, color: AppColors.system2),
                              ),
                      ),
                      child: Row(
                        children: [
                          SvgPicture.asset(e.icon),
                          8.horizontalSpace,
                          Expanded(
                            child: Text(
                              e.key,
                              style: CustomStyle.medium.regular
                                  .copyWith(color: AppColors.system8),
                            ),
                          ),
                          e.suffixWidget ??
                              (e.isShowRightIndicator
                                  ? SvgPicture.asset(AppIcons.iconChevronRight)
                                  : Container()),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }
}

class KeyIconSetting {
  final String icon;
  final String key;
  final VoidCallback? onTap;
  final Widget? suffixWidget;
  final bool isShowRightIndicator;

  KeyIconSetting({
    required this.icon,
    required this.key,
    this.onTap,
    this.suffixWidget,
    this.isShowRightIndicator = true,
  });
}
