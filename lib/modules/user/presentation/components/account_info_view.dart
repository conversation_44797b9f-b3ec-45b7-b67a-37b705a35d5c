import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/int_extensions.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/subscription/general/subscription_module_helper.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_bloc.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_state.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_state.dart';

class AccountInfoView extends StatelessWidget {
  const AccountInfoView({super.key, required this.isHomePage});

  final bool isHomePage;

  Widget creditCard(BuildContext context) {
    return BlocBuilder<MySubscriptionBloc, MySubscriptionState>(
      builder: (context, state) {
        if (state.isRequesting) {
          return Container(
            width: double.infinity,
            height: 84,
            decoration: BoxDecoration(
              color: AppColors.system3,
              borderRadius: BorderRadius.circular(16),
            ),
          )
              .animate(
                onPlay: (controller) => controller.repeat(),
              )
              .shimmer(duration: 1.toSeconds())
              .paddingOnly(top: 36);
        }

        final subscription = state.activeSubscription;
        return Stack(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10),
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(width: 1, color: AppColors.system0),
                gradient: const LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Color(0xFFFEFFFF),
                    Color(0xFFFCFDFF),
                  ],
                  stops: [0.0, 1.0],
                ),
                boxShadow: const [
                  // Outset shadow
                  BoxShadow(
                    color: Color(0x266486C5), // #6486C526 (40% opacity)
                    offset: Offset(0, 6), // X: 0px, Y: 6px
                    blurRadius: 12, // Spread: 12px
                  ),
                  // Inset shadow (workaround using a gradient overlay)
                  BoxShadow(
                    color: Color(0x40FFFFFF), // #FFFFFF40 (25% opacity)
                    offset: Offset(0, -5), // X: 0px, Y: -5px
                    blurRadius: 14, // Spread: 14px
                    spreadRadius: -10, // Negative spread to mimic inset
                  ),
                ],
              ),
              child: (subscription != null)
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              subscription.plan?.name ?? '',
                              style: CustomStyle.h5.bold
                                  .copyWith(color: AppColors.system8),
                            ),
                            if (subscription.endDate != null && !isHomePage)
                              RichText(
                                text: TextSpan(
                                  text:
                                      '${subscription.endDate!.toLocal().isBefore(DateTime.now()) ? context.localization.expired : context.localization.expiryDate}: ',
                                  style: CustomStyle.medium.bold
                                      .copyWith(color: AppColors.system6),
                                  children: [
                                    TextSpan(
                                        text: DateFormat('dd/MM/yyyy').format(
                                            subscription.endDate!.toLocal()),
                                        style: CustomStyle.medium.medium
                                            .copyWith(
                                                color: AppColors.system6)),
                                  ],
                                ),
                              ),
                            RichText(
                              text: TextSpan(
                                text: '${context.localization.creditsTitle}: ',
                                style: CustomStyle.medium.bold
                                    .copyWith(color: AppColors.system6),
                                children: [
                                  TextSpan(
                                      text: Utils.formaCurrency(
                                          (subscription.totalTokens ?? 0) -
                                              (subscription.inputTokens ?? 0) -
                                              (subscription.outputTokens ?? 0)),
                                      style: CustomStyle.medium.medium
                                          .copyWith(color: AppColors.system6)),
                                ],
                              ),
                            ),
                            // if (isHomePage &&
                            //     subscription.plan?.code != 'TRIAL')
                            //   16.verticalSpace,
                          ],
                        ).paddingSymmetric(
                            h: 16,
                            v: (isHomePage &&
                                    subscription.plan?.code != 'TRIAL')
                                ? 8
                                : 0),
                        if (subscription.plan?.code == 'TRIAL')
                          Container(
                            padding: const EdgeInsets.only(
                                top: 8, left: 16, right: 16),
                            decoration: const BoxDecoration(
                              border: Border(
                                top: BorderSide(
                                  width: 1,
                                  color: AppColors.system2,
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    context.localization.upgradeToPremium,
                                    style: CustomStyle.medium.smb
                                        .copyWith(color: AppColors.system6),
                                  ),
                                ),
                                4.verticalSpace,
                                AppButton(
                                  backgroundColor: AppColors.green,
                                  buttonTitle: context.localization.upgrade,
                                  buttonSize: ButtonSize.small,
                                  onPressed: () {
                                    SubscriptionModuleHelper
                                        .goToUpgradeSubscriptionPage();
                                  },
                                ),
                              ],
                            ),
                          ).paddingOnly(top: 6),
                      ],
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.localization.upgradeToPremium.toUpperCase(),
                          style: CustomStyle.medium.smb
                              .copyWith(color: AppColors.system6),
                        ),
                        4.verticalSpace,
                        Row(
                          children: [
                            AppButton(
                              backgroundColor: AppColors.green,
                              buttonTitle: context.localization.upgrade,
                              onPressed: () {
                                SubscriptionModuleHelper
                                    .goToUpgradeSubscriptionPage();
                              },
                            ),
                          ],
                        ),
                      ],
                    ).paddingSymmetric(h: 16),
            ).paddingOnly(top: 40),
            Positioned(
              top: 0,
              right: 16,
              child: Image.asset(
                AppImages.imageSubChatbot,
                width: 90,
                height: 100,
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserInfoBloc, UserInfoState>(
      builder: (context, state) {
        final user = state.user;
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(21),
                        child: Container(
                          color: Colors.white,
                          child: CachedNetworkImage(
                            imageUrl: Utils.convertImageUrl(user?.avatar),
                            width: 42,
                            height: 42,
                            fit: BoxFit.cover,
                            errorWidget: (context, error, stackTrace) {
                              return Image.asset(
                                width: 42,
                                height: 42,
                                AppImages.imageDefaultAvatar,
                              );
                            },
                          ),
                        ),
                      ).paddingOnly(right: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              Utils.getTimeOfDay(context),
                              style: CustomStyle.large.smb
                                  .copyWith(color: AppColors.green),
                            ),
                            Row(
                              children: [
                                Flexible(
                                  child: Text(
                                    user?.fullname ??
                                        (user?.username ??
                                            (user?.email != null
                                                ? Utils.getNameFromEmail(
                                                    user!.email!)
                                                : '')),
                                    style: CustomStyle.h4.smb
                                        .copyWith(color: AppColors.system0),
                                  ),
                                ),
                                8.horizontalSpace,
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // 16.horizontalSpace,
                // IconInkwell(
                //   path: AppIcons.iconNotification,
                //   color: AppColors.system0,
                //   onTap: () {
                //     AdsService.instance.showInterstitialAd();
                //   },
                // ),
              ],
            ),
            creditCard(context),
          ],
        );
      },
    ).paddingOnly(bottom: 16);
  }
}
