import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/inputs/app_text_field.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/app_validator_type.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';

class DeleteAccountContent extends StatefulWidget {
  const DeleteAccountContent({super.key, required this.onSubmitted});
  final void Function(String value) onSubmitted;

  @override
  State<DeleteAccountContent> createState() => _DeleteAccountContentState();
}

class _DeleteAccountContentState extends State<DeleteAccountContent> {
  final TextEditingController passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          Image.asset(AppImages.imageDelete),
          24.verticalSpace,
          Text(
            'Delete Account',
            style: CustomStyle.h5.smb.copyWith(color: AppColors.error),
          ),
          Text(
            'Are you sure you want to delete your account? This action is irreversible. Your data will be permanently erased.',
            textAlign: TextAlign.center,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system5),
          ),
          16.verticalSpace,
          AppTextField(
            label: 'Password',
            controller: passwordController,
            formKey: _formKey,
            hintText: 'Enter password',
            validatorType: AppTextFieldValidatorType.password,
            prefixIcon: AppIcons.iconPassword,
            isPassword: true,
          ),
          24.verticalSpace,
          Row(
            children: [
              Expanded(
                child: AppButton(
                  buttonTitle: context.localization.cancel,
                  outline: true,
                  backgroundColor: AppColors.system0,
                  borderColor: AppColors.system3,
                  buttonColor: AppColors.system6,
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x0D000000), // Hexadecimal color #0000000D
                      offset: Offset(0, 14), // Horizontal and vertical offset
                      blurRadius: 14, // Blur radius
                      spreadRadius: 0, // Spread radius
                    ),
                  ],
                  onPressed: () {
                    Modular.to.pop();
                  },
                ),
              ),
              16.horizontalSpace,
              Expanded(
                child: AppButton(
                  buttonTitle: context.localization.yes,
                  backgroundColor: AppColors.error,
                  buttonColor: AppColors.system0,
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      Modular.to.pop();
                      widget.onSubmitted.call(passwordController.text);
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
