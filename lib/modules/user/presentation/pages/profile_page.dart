import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_configs.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/helpers/general_helper.dart';
import 'package:saymee/core/helpers/web_socket_helper.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/app/presentation/components/scroll_background_view.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/auth/presentation/blocs/email_cubit.dart';
import 'package:saymee/modules/subscription/general/subscription_module_helper.dart';
import 'package:saymee/modules/user/data/repositories/user_repository.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';
import 'package:saymee/modules/user/presentation/components/account_info_view.dart';
import 'package:saymee/modules/user/presentation/components/delete_account_content.dart';
import 'package:saymee/modules/user/presentation/components/setting_card.dart';
import 'package:share_plus/share_plus.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  void _signOut() {
    WebSocketHelper().disconnect();
    AuthModuleHelper.signOut();
    AuthModuleHelper.navigateToSignInPage();
  }

  void _deleteAccount(
    BuildContext context, {
    String? password,
  }) async {
    final user = Modular.get<UserInfoBloc>().state.user;
    final userRepository = Modular.get<UserRepository>();
    AppLoading.show(context: context);

    final result = await userRepository.deleteAccount(
      userId: user?.userId ?? -1,
      type: user?.type ?? '',
      password: password,
      externalUserId: user?.externalUserId,
    );

    if (context.mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      Modular.get<EmailCubit>().setNewEmail('');
      _signOut();
    });
  }

  void _confirmLogout(BuildContext context) {
    AppDialog.show(
      context: context,
      buttons: [
        AppButton(
          backgroundColor: AppColors.system0,
          borderColor: AppColors.system3,
          outline: true,
          buttonColor: AppColors.system6,
          titleStyle: CustomStyle.large.smb,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0D000000), // Hexadecimal color #0000000D
              offset: Offset(0, 14), // Horizontal and vertical offset
              blurRadius: 14, // Blur radius
              spreadRadius: 0, // Spread radius
            ),
          ],
          buttonTitle: context.localization.cancel,
          onPressed: () {
            Modular.to.pop();
          },
        ),
        AppButton(
          backgroundColor: AppColors.error,
          buttonColor: AppColors.system0,
          titleStyle: CustomStyle.large.smb,
          buttonTitle: context.localization.yes,
          onPressed: () {
            Modular.to.pop();
            _signOut();
          },
        ),
      ],
      descriptionWidget: Column(
        children: [
          Image.asset(AppImages.imageLogout),
          24.verticalSpace,
          Text(
            context.localization.logout,
            style: CustomStyle.h5.smb.copyWith(color: AppColors.error),
          ),
          Text(
            context.localization.confirmLogout,
            textAlign: TextAlign.center,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system5),
          ),
          16.verticalSpace,
        ],
      ),
    );
  }

  Widget _settingCard(BuildContext context) {
    return Column(
      children: [
        SettingCard(
          title: context.localization.profile,
          list: [
            KeyIconSetting(
              icon: AppIcons.iconFullname,
              key: context.localization.accountInfo,
              onTap: () {
                UserModuleHelper.goToAccountInfoPage();
              },
            ),
          ],
        ),
        12.verticalSpace,
        SettingCard(
          title: 'Subscription',
          list: [
            KeyIconSetting(
              icon: AppIcons.iconStore,
              key: 'Plans',
              onTap: () {
                SubscriptionModuleHelper.goToUpgradeSubscriptionPage();
              },
            ),
            // KeyIconSetting(
            //   icon: AppIcons.iconManage,
            //   key: 'Manage subscriptions',
            //   onTap: () {},
            // ),
          ],
        ),
        12.verticalSpace,
        SettingCard(
          title: context.localization.enhanceYourExperience,
          list: [
            KeyIconSetting(
              icon: AppIcons.iconRate,
              key: context.localization.rateApp,
              onTap: () {
                final configBloc = Modular.get<ConfigBloc>();
                final reviewUrl = configBloc.state.config?.reviewUrl;
                if (reviewUrl != null && reviewUrl.isNotEmpty) {
                  Utils.launchURL(urlString: reviewUrl);
                }
              },
            ),
            KeyIconSetting(
              icon: AppIcons.iconShare,
              key: context.localization.shareWithFriend,
              onTap: () {
                final configBloc = Modular.get<ConfigBloc>();
                final updateUrl = configBloc.state.config?.updateUrl;
                if (updateUrl != null && updateUrl.isNotEmpty) {
                  Share.share(updateUrl);
                }
              },
            ),
          ],
        ),
        12.verticalSpace,
        SettingCard(
          title: context.localization.helpAndSupport,
          list: [
            KeyIconSetting(
              icon: AppIcons.iconFaq,
              key: context.localization.faq,
              onTap: () {
                AppModuleHelper.goToWebviewPage(
                  url: AppConfigs.faqUrl,
                  webName: context.localization.faq,
                );
              },
            ),
            KeyIconSetting(
              icon: AppIcons.iconPolicy,
              key: context.localization.privacyPolicy,
              onTap: () {
                AppModuleHelper.goToWebviewPage(
                  url: AppConfigs.privacyPolicyUrl,
                  webName: context.localization.privacyPolicy,
                );
              },
            ),
            KeyIconSetting(
              icon: AppIcons.iconContact,
              key: context.localization.contactUs,
              onTap: () {
                final configBloc = Modular.get<ConfigBloc>();
                final emailSupport = configBloc.state.config?.supportEmail;
                if (emailSupport != null && emailSupport.isNotEmpty) {
                  Utils.openEmail(emailSupport);
                }
              },
            ),
          ],
        ),
        16.verticalSpace,
        SettingCard(
          list: [
            KeyIconSetting(
              icon: AppIcons.iconLogout,
              key: context.localization.logout,
              onTap: () => _confirmLogout(context),
            ),
          ],
        ),
        16.verticalSpace,
        SettingCard(
          list: [
            KeyIconSetting(
              icon: AppIcons.iconDelete,
              key: "Delete Account",
              onTap: () => _confirmDeleteAccount(context),
            ),
          ],
        ),
      ],
    ).paddingOnly(bottom: 16);
  }

  void _confirmDeleteAccount(BuildContext context) {
    final userInfoBloc = Modular.get<UserInfoBloc>();
    if (userInfoBloc.state.user?.type == AccountType.email) {
      AppDialog.show(
        context: context,
        buttons: [],
        descriptionWidget: DeleteAccountContent(
          onSubmitted: (value) {
            _deleteAccount(context, password: value);
          },
        ),
      );
    } else {
      _confirmDeleteAppleAccount(context);
    }
  }

  void _confirmDeleteAppleAccount(BuildContext context) {
    AppDialog.show(
      context: context,
      buttons: [
        AppButton(
          buttonTitle: context.localization.cancel,
          outline: true,
          backgroundColor: AppColors.system0,
          borderColor: AppColors.system3,
          buttonColor: AppColors.system6,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0D000000), // Hexadecimal color #0000000D
              offset: Offset(0, 14), // Horizontal and vertical offset
              blurRadius: 14, // Blur radius
              spreadRadius: 0, // Spread radius
            ),
          ],
          onPressed: () {
            Modular.to.pop();
          },
        ),
        AppButton(
          buttonTitle: context.localization.delete,
          backgroundColor: AppColors.error,
          buttonColor: AppColors.system0,
          onPressed: () {
            Modular.to.pop();
            _deleteAccount(context);
          },
        ),
      ],
      descriptionWidget: Column(
        children: [
          Image.asset(AppImages.imageDelete),
          24.verticalSpace,
          Text(
            "Delete Account",
            style: CustomStyle.h5.smb.copyWith(color: AppColors.error),
          ),
          4.verticalSpace,
          Text(
            "Are you sure you want to delete your account? This action is irreversible. Your data will be permanently erased.",
            textAlign: TextAlign.center,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system5),
          ),
          8.verticalSpace,
        ],
      ),
    );
  }

  Widget _version(BuildContext context) {
    return Column(
      children: [
        Text(
          "${GeneralHelper.appVersion} (${GeneralHelper.buildNumber})",
          style: CustomStyle.medium.regular.copyWith(color: AppColors.system5),
        ),
        Text(
          context.localization.copyRight,
          style: CustomStyle.medium.regular.copyWith(color: AppColors.system5),
        ),
      ],
    ).paddingOnly(bottom: 16);
  }

  @override
  Widget build(BuildContext context) {
    return ScrollBackgroundView(
      child: Column(
        children: [
          16.verticalSpace,
          const AccountInfoView(isHomePage: false).paddingSymmetric(h: 16),
          _settingCard(context).paddingSymmetric(h: 16),
          _version(context).paddingSymmetric(h: 16),
        ],
      ),
    );
  }
}
