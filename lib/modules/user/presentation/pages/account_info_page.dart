import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/components/inputs/app_text_field.dart';
import 'package:saymee/core/components/text_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_configs.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_validator_type.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/data/models/media_model.dart';
import 'package:saymee/modules/app/data/repositories/app_repository.dart';
import 'package:saymee/modules/user/data/repositories/user_repository.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_event.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_state.dart';

class AccountInfoPage extends StatefulWidget {
  const AccountInfoPage({super.key});

  @override
  State<AccountInfoPage> createState() => _AccountInfoPageState();
}

class _AccountInfoPageState extends State<AccountInfoPage> {
  late TextEditingController fullnameController, usernameController;
  final userInfoBloc = Modular.get<UserInfoBloc>();
  XFile? _selectedImage;
  final ImagePicker _imagePicker = ImagePicker();
  bool _isEditMode = false;
  final _formKey = GlobalKey<FormState>();
  final appRepository = Modular.get<AppRepository>();
  final userRepository = Modular.get<UserRepository>();

  @override
  void initState() {
    super.initState();

    final user = userInfoBloc.state.user;
    fullnameController = TextEditingController(text: user?.fullname);
    usernameController = TextEditingController(text: user?.username);
  }

  @override
  void dispose() {
    fullnameController.dispose();
    usernameController.dispose();
    super.dispose();
  }

  void _updateProfile() async {
    if (_formKey.currentState!.validate()) {
      AppLoading.show(
        context: context,
        title: '${context.localization.updating}...',
      );

      if (_selectedImage != null) {
        //upload file image

        final uploadResult =
            await appRepository.uploadFile(paths: [_selectedImage!.path]);

        uploadResult.fold((l) {
          if (mounted) {
            AppLoading.turnOff(context: context);
            AppDialog.showErrorDialog(context: context, error: l.reason);
            return;
          }
        }, (r) {
          List<MediaModel> medias = r['medias'];
          if (medias.isEmpty) {
            AppLoading.turnOff(context: context);
            AppDialog.showErrorDialog(
              context: context,
              error: context.localization.pleaseTryAgain,
            );
            return;
          } else {
            _update(path: medias.first.relativePath);
          }
        });
      } else {
        _update();
      }
    }
  }

  void _update({String? path}) async {
    final result = await userRepository.updateProfile(
      fullname: fullnameController.text.trim(),
      username: usernameController.text.trim(),
      avatar: path,
    );

    if (mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppDialog.showErrorDialog(context: context, error: l.reason);
    }, (r) {
      userInfoBloc.add(UserUpdateRequested(
        fullname: fullnameController.text.trim(),
        username: usernameController.text.trim(),
        avatar: path,
      ));

      if (mounted) {
        setState(() {
          _selectedImage = null;
          _isEditMode = false;
        });
      }
    });
  }

  Widget _avatarView(BuildContext context) {
    if (_selectedImage != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(55),
        child: Container(
          color: Colors.white,
          child: Image.file(
            File(_selectedImage!.path),
            width: 110,
            height: 110,
          ),
        ),
      ).paddingOnly(bottom: 16);
    }

    return BlocBuilder<UserInfoBloc, UserInfoState>(
      builder: (context, state) {
        final avatar = state.user?.avatar;
        if (avatar == null) {
          return Container(
            width: 110,
            height: 110,
            decoration: BoxDecoration(
              color: const Color(0xFF1969FE).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: SvgPicture.asset(AppIcons.iconNewAvatar),
            ),
          );
        }

        return ClipRRect(
          borderRadius: BorderRadius.circular(55),
          child: Container(
            color: Colors.white,
            child: CachedNetworkImage(
              imageUrl: Utils.convertImageUrl(avatar),
              width: 110,
              height: 110,
              errorWidget: (context, error, stackTrace) {
                return Container();
              },
            ),
          ),
        );
      },
    ).paddingOnly(bottom: 16);
  }

  void _pickImage() async {
    final XFile? image = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      requestFullMetadata: false,
    );

    if (image != null) {
      final size = await image.length();
      if (size > 20 * 1024 * 1024) {
        if (mounted) {
          AppDialog.showErrorDialog(
            context: context,
            error: AppConfigs.maxFileSizeError,
          );
        }
      } else {
        setState(() {
          _selectedImage = image;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.system1,
      appBar: AppTopBar(
        title: context.localization.accountInfo,
        leading: IconInkwell(
          path: AppIcons.iconChevronLeft,
          color: AppColors.system5,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          onTap: () {
            Modular.to.pop();
          },
        ),
        actions: [
          TextInkwell(
            text: _isEditMode
                ? context.localization.save
                : context.localization.edit,
            color: AppColors.blue,
            style: CustomStyle.large.medium,
            padding: const EdgeInsets.only(right: 16),
            onTap: () {
              if (!_isEditMode) {
                setState(() {
                  _isEditMode = true;
                });
              } else {
                _updateProfile();
              }
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              16.verticalSpace,
              _avatarView(context),
              if (_isEditMode)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AppButton(
                      buttonTitle: context.localization.setNewPhoto,
                      prefixIconPath: AppIcons.iconNewPhoto,
                      buttonGap: 8,
                      paddingX: 16,
                      onPressed: _pickImage,
                    ).paddingOnly(bottom: 16),
                  ],
                ),
              AppTextField(
                label: context.localization.fullname,
                hintText: context.localization.inputYourFullname,
                controller: fullnameController,
                validatorType: AppTextFieldValidatorType.fullname,
                prefixIcon: AppIcons.iconFullname,
                disabled: !_isEditMode,
                formKey: _formKey,
              ),
              16.verticalSpace,
              AppTextField(
                label: context.localization.username,
                hintText: context.localization.chooseAnUsername,
                controller: usernameController,
                validatorType: AppTextFieldValidatorType.username,
                prefixIcon: AppIcons.iconName,
                disabled: !_isEditMode,
                formKey: _formKey,
              ),
            ],
          ).paddingSymmetric(h: 16),
        ),
      ),
    );
  }
}
