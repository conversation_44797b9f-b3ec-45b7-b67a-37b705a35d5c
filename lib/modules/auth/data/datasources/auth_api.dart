import 'package:dio/dio.dart';
import 'package:saymee/core/utils/utils.dart';

class AuthApi {
  final dioClient = Utils.dioClient;

  // ---- methods ----
  Future<Response> login({
    required String email,
    String? password,
    required String type,
    String? appleUserId,
    String? fullname,
    String? language,
  }) async {
    const String url = '/api/v3/user/login';
    final params = {
      'email': email,
      'password': password,
      'type': type,
      'appleUserId': appleUserId,
      'fullname': fullname,
      'language': language,
    };

    Utils.debugLog(params);

    try {
      final response = await dioClient.post(url, data: params);
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> register({
    required String email,
    required String password,
    required String fullname,
    required String username,
  }) async {
    const String url = '/api/v3/user/register';
    final params = {
      'email': email,
      'password': password,
      'fullname': fullname,
      'username': username,
      'type': 'EMAIL',
    };

    try {
      final response = await dioClient.post(url, data: params);
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> activate({
    required String email,
    required String otp,
  }) async {
    const String url = '/api/v3/user/activate';
    final params = {
      'email': email,
      'otp': otp,
    };

    Utils.debugLogSuccess(params);

    try {
      final response = await dioClient.post(url, data: params);
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> forgotPassword({
    required String email,
  }) async {
    const String url = '/api/v3/user/resetPassword';
    final params = {
      'email': email,
    };

    try {
      final response = await dioClient.post(url, data: params);
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> setNewPassword({
    required String email,
    required String otp,
    required String password,
  }) async {
    const String url = '/api/v3/user/setNewPassword';
    final params = {
      'email': email,
      'otp': otp,
      'password': password,
    };

    try {
      final response = await dioClient.post(url, data: params);
      return response;
    } catch (e) {
      rethrow;
    }
  }
}
