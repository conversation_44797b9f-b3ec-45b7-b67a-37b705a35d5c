import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/network/dio_exceptions.dart';
import 'package:saymee/core/network/dio_failure.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/auth/data/datasources/auth_api.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';

class AuthRepository {
  // ---- params ----
  final AuthApi api;

  AuthRepository({required this.api});

  // ---- methods ----
  Future<Either<DioFailure, Map<String, dynamic>>> login({
    required String email,
    String? password,
    required String type,
    String? appleUserId,
    String? fullname,
    String? language,
  }) async {
    try {
      final response = await api.login(
        email: email,
        password: password,
        type: type,
        appleUserId: appleUserId,
        fullname: fullname,
        language: language,
      );
      Utils.debugLog('login response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        final String? accessToken = mapData['accessToken'] as String?;
        final UserModel? user = mapData['user'] != null
            ? UserModel.fromJson(mapData['user'] as Map<String, dynamic>)
            : null;

        return Right({
          'statusCode': statusCode,
          'accessToken': accessToken,
          'user': user,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  // ---- methods ----
  Future<Either<DioFailure, Map<String, dynamic>>> register({
    required String email,
    required String password,
    required String fullname,
    required String username,
  }) async {
    try {
      final response = await api.register(
        email: email,
        password: password,
        fullname: fullname,
        username: username,
      );
      Utils.debugLog('register response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> activate({
    required String email,
    required String otp,
  }) async {
    try {
      final response = await api.activate(email: email, otp: otp);
      Utils.debugLog('activate response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> forgotPassword({
    required String email,
  }) async {
    try {
      final response = await api.forgotPassword(email: email);
      Utils.debugLog('forgotPassword response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }

  Future<Either<DioFailure, Map<String, dynamic>>> setNewPassword({
    required String email,
    required String otp,
    required String password,
  }) async {
    try {
      final response = await api.setNewPassword(
        email: email,
        otp: otp,
        password: password,
      );
      Utils.debugLog('setNewPassword response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['result'] as String?;
      if (result == ApiResult.success) {
        return Right({
          'statusCode': statusCode,
        });
      } else {
        final reason = mapData['reason'] ?? '';
        final code = mapData['code'] ?? '';
        return Left(ApiFailure(
          reason: reason,
          statusCode: statusCode,
          code: code,
        ));
      }
    } on DioException catch (e) {
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    }
  }
}
