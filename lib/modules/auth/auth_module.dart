import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/guards/auth_guard.dart';
import 'package:saymee/modules/auth/data/datasources/auth_api.dart';
import 'package:saymee/modules/auth/data/repositories/auth_repository.dart';
import 'package:saymee/modules/auth/general/auth_module_routes.dart';
import 'package:saymee/modules/auth/presentation/blocs/auth/auth_bloc.dart';
import 'package:saymee/modules/auth/presentation/blocs/email_cubit.dart';
import 'package:saymee/modules/auth/presentation/pages/activate_account_page.dart';
import 'package:saymee/modules/auth/presentation/pages/forgot_password_page.dart';
import 'package:saymee/modules/auth/presentation/pages/register_page.dart';
import 'package:saymee/modules/auth/presentation/pages/reset_password_page.dart';
import 'package:saymee/modules/auth/presentation/pages/sign_in_page.dart';

class AuthModule extends Module {
  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);
    i.addSingleton(() => AuthApi());
    i.addSingleton(() => AuthRepository(api: Modular.get<AuthApi>()));

    // bloc
    i.addSingleton(() => AuthBloc(repository: Modular.get<AuthRepository>()));
    i.addSingleton(() => EmailCubit());
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);

    r.child(
      AuthModuleRoutes.register,
      child: (context) => const RegisterPage(),
    );
    r.child(
      AuthModuleRoutes.signIn,
      child: (context) => const SignInPage(),
      guards: [AuthGuard()],
    );
    r.child(
      AuthModuleRoutes.activateAccount,
      child: (context) => ActivateAccountPage(
        email: r.args.data['email'],
        otp: r.args.data['otp'],
        isLoggedIn: r.args.data['isLoggedIn'],
      ),
    );
    r.child(
      AuthModuleRoutes.forgotPassword,
      child: (context) => const ForgotPasswordPage(),
    );
    r.child(
      AuthModuleRoutes.resetPassword,
      child: (context) => ResetPasswordPage(
        email: r.args.data['email'],
      ),
    );
  }
}
