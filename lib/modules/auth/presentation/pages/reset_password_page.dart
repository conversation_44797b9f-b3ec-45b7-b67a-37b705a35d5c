import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_annotated_region.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/components/inputs/app_text_field.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_dimensions.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/app_validator_type.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/auth/data/repositories/auth_repository.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/auth/presentation/blocs/email_cubit.dart';

class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key, required this.email});

  final String email;

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  final formKey = GlobalKey<FormState>();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _rePasswordController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();

  final _authRepository = Modular.get<AuthRepository>();

  @override
  void dispose() {
    _passwordController.dispose();
    _rePasswordController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  void reset() async {
    Utils.hideKeyboard();
    if (formKey.currentState!.validate()) {
      if (mounted) {
        AppLoading.show(context: context);
      }

      final result = await _authRepository.setNewPassword(
        email: widget.email,
        password: _passwordController.text.trim(),
        otp: _otpController.text.trim(),
      );

      if (mounted) {
        AppLoading.turnOff(context: context);
      }

      result.fold((l) {
        AppDialog.showErrorDialog(context: context, error: l.reason);
      }, (r) {
        _showSuccessDialog(context);
      });
    } else {
      return;
    }
  }

  void _showSuccessDialog(BuildContext context) {
    AppDialog.show(
      context: context,
      barrierDismissible: false,
      buttons: [
        AppButton(
          buttonTitle: context.localization.signIn,
          onPressed: () {
            Modular.to.pop();
            Modular.get<EmailCubit>().setNewEmail(widget.email);
            AuthModuleHelper.goToSignInPage();
          },
        ),
      ],
      descriptionWidget: Column(
        children: [
          Image.asset(AppImages.imageSuccess),
          Text(
            context.localization.success,
            style: CustomStyle.h5.smb.copyWith(color: AppColors.green),
          ),
          Text(
            context.localization.resetPasswordSuccess,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system6),
          ),
          8.verticalSpace,
        ],
      ),
    );
  }

  Widget _bottomWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 16,
        left: 16,
        right: 16,
        bottom: 16 +
            (Platform.isIOS
                ? (MediaQuery.of(context).viewInsets.bottom >
                        MediaQuery.of(context).viewPadding.bottom +
                            MediaQuery.of(context).viewPadding.top
                    ? 0
                    : MediaQuery.of(context).viewPadding.bottom)
                : 0),
      ),
      decoration: BoxDecoration(
        color: AppColors.system0,
        border: const Border(
          top: BorderSide(width: 1, color: AppColors.system3),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6C6C7E).withOpacity(0.1),
            offset: Offset.zero,
            blurRadius: 10,
          ),
        ],
      ),
      child: AppButton(
        buttonTitle: context.localization.continueTitle,
        titleStyle: CustomStyle.large.smb,
        onPressed: reset,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Utils.hideKeyboard(),
      child: AppAnnotatedRegion(
        child: Scaffold(
          backgroundColor: AppColors.system1,
          appBar: AppTopBar(
            backgroundColor: AppColors.system1,
            title: context.localization.resetPassword,
            showShadow: false,
            leading: IconInkwell(
              path: AppIcons.iconChevronLeft,
              color: AppColors.neutral60,
              onTap: () {
                Modular.to.pop();
              },
              padding: const EdgeInsets.only(left: 8),
            ).paddingOnly(left: AppDimensions.baseMargin),
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Form(
                    key: formKey,
                    child: Column(
                      children: [
                        32.verticalSpace,
                        Image.asset(AppImages.imageResetPassword),
                        40.verticalSpace,
                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            text: context.localization.otpSentToEmail,
                            style: CustomStyle.medium.regular
                                .copyWith(color: AppColors.system5),
                            children: [
                              TextSpan(
                                  text: Utils.hideEmail(widget.email),
                                  style: CustomStyle.medium.medium
                                      .copyWith(color: AppColors.blue)),
                              TextSpan(
                                  text:
                                      context.localization.otpToResetPassword),
                            ],
                          ),
                        ),
                        8.verticalSpace,
                        AppTextField(
                          label: context.localization.otpCode,
                          hintText: context.localization.inputOtpCode,
                          controller: _otpController,
                          validatorType: AppTextFieldValidatorType.otp,
                          formKey: formKey,
                        ),
                        const SizedBox(
                          height: AppDimensions.baseMargin,
                        ),
                        AppTextField(
                          label: context.localization.password,
                          autofillHints: const [AutofillHints.newPassword],
                          hintText: context.localization.enterPassword,
                          prefixIcon: AppIcons.iconPassword,
                          controller: _passwordController,
                          isPassword: true,
                          keyboardType: TextInputType.visiblePassword,
                          validatorType: AppTextFieldValidatorType.password,
                          formKey: formKey,
                        ),
                        const SizedBox(
                          height: AppDimensions.baseMargin,
                        ),
                        AppTextField(
                          label: context.localization.password,
                          hintText: context.localization.confirmPassword,
                          prefixIcon: AppIcons.iconPassword,
                          controller: _rePasswordController,
                          isPassword: true,
                          passwordController: _passwordController,
                          keyboardType: TextInputType.visiblePassword,
                          validatorType: AppTextFieldValidatorType.password,
                          formKey: formKey,
                        ),
                        // 12.verticalSpace,
                        // Text(
                        //   'Password requires at least 8 characters, containing letters and numbers.',
                        //   textAlign: TextAlign.start,
                        //   style: CustomStyle.medium.regular
                        //       .copyWith(color: AppColors.system5),
                        // ),
                        16.verticalSpace,
                      ],
                    ).paddingSymmetric(h: 16),
                  ),
                ),
              ),
              _bottomWidget(context),
            ],
          ),
        ),
      ),
    );
  }
}
