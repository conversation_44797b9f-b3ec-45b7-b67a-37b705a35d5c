import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_annotated_region.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/components/inputs/app_text_field.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_dimensions.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/app_validator_type.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/auth/data/repositories/auth_repository.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _emailController = TextEditingController();
  final _authRepository = Modular.get<AuthRepository>();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  void _forgot() async {
    Utils.hideKeyboard();
    if (_formKey.currentState!.validate()) {
      if (mounted) {
        AppLoading.show(context: context);
      }

      final result = await _authRepository.forgotPassword(
        email: _emailController.text.trim(),
      );

      if (mounted) {
        AppLoading.turnOff(context: context);
      }

      result.fold((l) {
        AppDialog.showErrorDialog(context: context, error: l.reason);
      }, (r) {
        AuthModuleHelper.goToResetPasswordPage(
            email: _emailController.text.trim());
      });
    } else {
      return;
    }
  }

  Widget _bottomWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 16,
        left: 16,
        right: 16,
        bottom: 16 +
            (Platform.isIOS
                ? (MediaQuery.of(context).viewInsets.bottom >
                        MediaQuery.of(context).viewPadding.bottom +
                            MediaQuery.of(context).viewPadding.top
                    ? 0
                    : MediaQuery.of(context).viewPadding.bottom)
                : 0),
      ),
      decoration: BoxDecoration(
        color: AppColors.system0,
        border: const Border(
          top: BorderSide(width: 1, color: AppColors.system3),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6C6C7E).withOpacity(0.1),
            offset: Offset.zero,
            blurRadius: 10,
          ),
        ],
      ),
      child: AppButton(
        buttonTitle: context.localization.continueTitle,
        titleStyle: CustomStyle.large.smb,
        onPressed: _forgot,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Utils.hideKeyboard(),
      child: AppAnnotatedRegion(
        child: Scaffold(
          backgroundColor: AppColors.system1,
          appBar: AppTopBar(
            backgroundColor: AppColors.system1,
            title: context.localization.forgotPassword,
            showShadow: false,
            leading: IconInkwell(
              path: AppIcons.iconChevronLeft,
              color: AppColors.neutral60,
              onTap: () {
                Modular.to.pop();
              },
              padding: const EdgeInsets.only(left: 8),
            ).paddingOnly(left: AppDimensions.baseMargin),
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        34.verticalSpace,
                        Image.asset(AppImages.imageForgotPassword),
                        40.verticalSpace,
                        Text(
                          context.localization.enterEmailForResetPassword,
                          textAlign: TextAlign.center,
                          style: CustomStyle.medium.regular
                              .copyWith(color: AppColors.system5),
                        ),
                        8.verticalSpace,
                        AppTextField(
                          label: 'Email',
                          prefixIcon: AppIcons.iconEmail,
                          hintText: context.localization.inputYourEmail,
                          controller: _emailController,
                          validatorType: AppTextFieldValidatorType.email,
                          formKey: _formKey,
                        ),
                        16.verticalSpace,
                      ],
                    ).paddingSymmetric(h: 16),
                  ),
                ),
              ),
              _bottomWidget(context),
            ],
          ),
        ),
      ),
    );
  }
}
