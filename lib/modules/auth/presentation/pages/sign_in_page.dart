import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_annotated_region.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/inputs/app_text_field.dart';
import 'package:saymee/core/components/text_inkwell.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_configs.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/app_validator_type.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/helpers/web_socket_helper.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/general/app_module_helper.dart';
import 'package:saymee/modules/app/presentation/blocs/config/config_bloc.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/auth/presentation/blocs/auth/auth_bloc.dart';
import 'package:saymee/modules/auth/presentation/blocs/auth/auth_event.dart';
import 'package:saymee/modules/auth/presentation/blocs/auth/auth_state.dart';
import 'package:saymee/modules/auth/presentation/blocs/email_cubit.dart';
import 'package:saymee/modules/subscription/data/models/subscription_model.dart';
import 'package:saymee/modules/subscription/data/repositories/subscription_repository.dart';
import 'package:saymee/modules/subscription/general/subscription_module_helper.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> {
  late TextEditingController _emailController;
  late TextEditingController _passwordController;
  final _formKey = GlobalKey<FormState>();

  final _authBloc = Modular.get<AuthBloc>();
  final _preHelper = Modular.get<SharedPreferenceHelper>();
  final configBloc = Modular.get<ConfigBloc>();
  final subscriptionRepository = Modular.get<SubscriptionRepository>();

  @override
  void initState() {
    final localEmail = _preHelper.getEmail();
    _emailController = TextEditingController(text: (localEmail ?? ''));
    _passwordController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _signIn() {
    if (_formKey.currentState!.validate()) {
      Utils.hideKeyboard();
      _authBloc.add(AuthSignInRequested(
        email: _emailController.text.trim(),
        password: _passwordController.text.trim(),
        type: AccountType.email,
      ));
    } else {
      return;
    }
  }

  void _signInWithApple() async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final email = Utils.decodeAppleIDToken(credential.identityToken ?? '');

      final givenName = credential.givenName;
      final familyName = credential.familyName;

      final fullName = '${givenName ?? ''} ${familyName ?? ''}';

      _authBloc.add(AuthSignInRequested(
        email: credential.email ?? (email ?? ''),
        fullname: fullName.trim().isNotEmpty ? fullName : null,
        appleUserId: credential.userIdentifier,
        type: AccountType.apple,
      ));
    } catch (e) {
      Utils.debugLogError(e);
    }
  }

  void _getMySubscription() async {
    final result = await subscriptionRepository.getMySubscriptions(
        cancelToken: CancelToken());

    if (mounted) {
      AppLoading.turnOff(context: context);
    }

    result.fold((l) {
      AppModuleHelper.navigateToMainPage();
    }, (r) {
      SubscriptionModel? activeSubscription = r['activeSubscription'];
      if (activeSubscription == null ||
          activeSubscription.plan?.code == 'TRIAL') {
        if (configBloc.state.config?.forceOnboardingSubscription == "1") {
          SubscriptionModuleHelper.navigateToUpgradeSubscriptionPage(
              isForce: true);
        } else {
          AppModuleHelper.navigateToMainPage();
        }
      } else {
        AppModuleHelper.navigateToMainPage();
      }
    });
  }

  _listeners() {
    return [
      BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthStateSignIn) {
            if (state.isRequesting) {
              AppLoading.show(
                  context: context,
                  title: "${context.localization.signing}...");
            } else {
              final String reason = state.reason;
              final accessToken = state.accessToken;

              if (accessToken != null) {
                WebSocketHelper().connect();
                UserModuleHelper.getUserData(user: state.user);
                _getMySubscription();
              } else {
                AppLoading.turnOff(context: context);
                AppDialog.showErrorDialog(context: context, error: reason);
              }
            }
          }
        },
      ),
      BlocListener<EmailCubit, String?>(
        listener: (context, email) {
          if (email != null) {
            setState(() {
              _emailController.text = email;
            });
          }
        },
      ),
    ];
  }

  Widget _orLine(BuildContext context) {
    if (Platform.isAndroid) {
      return Container();
    }
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            color: AppColors.system3,
          ),
        ),
        Text(
          context.localization.or,
          style: CustomStyle.medium.medium.copyWith(color: AppColors.system5),
        ).paddingSymmetric(h: 16),
        Expanded(
          child: Container(
            height: 1,
            color: AppColors.system3,
          ),
        ),
      ],
    ).paddingOnly(bottom: 24);
  }

  _goToRegister() {
    AuthModuleHelper.goToRegisterPage();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Utils.hideKeyboard(),
      child: MultiBlocListener(
        listeners: _listeners(),
        child: AppAnnotatedRegion(
          child: Scaffold(
            backgroundColor: AppColors.system1,
            body: SafeArea(
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      24.verticalSpace,
                      Image.asset(
                        AppImages.imageSignUp,
                        width: 200,
                        height: 170,
                      ),
                      24.verticalSpace,
                      Text(
                        context.localization.signInWithEmail,
                        style: CustomStyle.h4.smb
                            .copyWith(color: AppColors.system8),
                      ),
                      // Text(
                      //   'Input your account!',
                      //   style: CustomStyle.large.medium
                      //       .copyWith(color: AppColors.system5),
                      // ),
                      24.verticalSpace,
                      AppTextField(
                        label: "Email",
                        prefixIcon: AppIcons.iconEmail,
                        hintText: context.localization.enterEmail,
                        autofillHints: const [AutofillHints.email],
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        validatorType: AppTextFieldValidatorType.email,
                        formKey: _formKey,
                      ),
                      16.verticalSpace,
                      AppTextField(
                        label: context.localization.password,
                        hintText: context.localization.enterPassword,
                        autofillHints: const [AutofillHints.password],
                        prefixIcon: AppIcons.iconPassword,
                        controller: _passwordController,
                        isPassword: true,
                        keyboardType: TextInputType.visiblePassword,
                        validatorType: AppTextFieldValidatorType.password,
                        formKey: _formKey,
                      ),
                      16.verticalSpace,
                      AppButton(
                        buttonTitle: context.localization.signIn,
                        titleStyle: CustomStyle.large.smb,
                        onPressed: _signIn,
                      ),
                      16.verticalSpace,
                      TextInkwell(
                        text: '${context.localization.forgotPassword}?',
                        color: AppColors.blue,
                        style: CustomStyle.large.smb,
                        onTap: () => AuthModuleHelper.goToForgotPasswordPage(),
                      ),
                      24.verticalSpace,
                      _orLine(context),
                      if (Platform.isIOS)
                        AppButton(
                          buttonTitle: context.localization.signInWithApple,
                          buttonSize: ButtonSize.large,
                          titleStyle: CustomStyle.large.smb,
                          buttonColor: AppColors.system6,
                          borderColor: AppColors.system3,
                          outline: true,
                          buttonGap: 8,
                          backgroundColor: AppColors.system0,
                          prefixIconPath: AppIcons.iconApple,
                          onPressed: _signInWithApple,
                        ).paddingOnly(bottom: 16),
                      RichText(
                        text: TextSpan(
                          text: context.localization.notAccount,
                          style: CustomStyle.medium.medium
                              .copyWith(color: AppColors.system6),
                          children: [
                            TextSpan(
                              text: context.localization.signUpHere,
                              style: CustomStyle.medium.bold
                                  .copyWith(color: AppColors.blue),
                              recognizer: TapGestureRecognizer()
                                ..onTap = _goToRegister,
                            ),
                          ],
                        ),
                      ),
                      16.verticalSpace,
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          text: context.localization.byUsingService,
                          style: CustomStyle.medium.regular
                              .copyWith(color: AppColors.system5),
                          children: [
                            TextSpan(
                              text: context.localization.terms,
                              style: CustomStyle.medium.regular
                                  .copyWith(color: AppColors.blue),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  AppModuleHelper.goToWebviewPage(
                                    url: AppConfigs.termsAndConditionsUrl,
                                    webName:
                                        context.localization.termAndConditions,
                                  );
                                },
                            ),
                            TextSpan(text: context.localization.and),
                            TextSpan(
                              text: context.localization.privacyPolicy,
                              style: CustomStyle.medium.regular
                                  .copyWith(color: AppColors.blue),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  AppModuleHelper.goToWebviewPage(
                                    url: AppConfigs.privacyPolicyUrl,
                                    webName: context.localization.privacyPolicy,
                                  );
                                },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ).paddingSymmetric(h: 16),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
