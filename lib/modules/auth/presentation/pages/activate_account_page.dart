import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_annotated_region.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/components/inputs/app_text_field.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_dimensions.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/app_validator_type.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/deep_link_service.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/auth/data/repositories/auth_repository.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/auth/presentation/blocs/email_cubit.dart';

class ActivateAccountPage extends StatefulWidget {
  const ActivateAccountPage({
    super.key,
    required this.email,
    this.otp,
    required this.isLoggedIn,
  });

  final String email;
  final String? otp;
  final bool isLoggedIn;

  @override
  State<ActivateAccountPage> createState() => _ActivateAccountPageState();
}

class _ActivateAccountPageState extends State<ActivateAccountPage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _otpController;
  final _authRepository = Modular.get<AuthRepository>();
  late String email;
  StreamSubscription<Uri>? _subscription;
  final deepLinkService = DeepLinkService();

  @override
  void initState() {
    email = widget.email;
    _otpController = TextEditingController(text: widget.otp);
    _subscription = deepLinkService.linkStream.listen((uri) {
      if (uri
          .toString()
          .startsWith('https://jesterbot.net/universal/user/activate')) {
        final queryParameters = uri.queryParameters;
        if (queryParameters.containsKey('email') &&
            queryParameters.containsKey('otp')) {
          final activateEmail = queryParameters['email'];
          final otp = queryParameters['otp'];

          if (activateEmail != null && otp != null) {
            setState(() {
              email = activateEmail;
              _otpController.text = otp;
            });

            _formKey.currentState?.validate();
          }
        }
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _otpController.dispose();
    _subscription?.cancel();
    super.dispose();
  }

  void _activate() async {
    Utils.hideKeyboard();
    if (_formKey.currentState!.validate()) {
      if (mounted) {
        AppLoading.show(
            context: context, title: '${context.localization.activating}...');
      }

      final result = await _authRepository.activate(
        email: email,
        otp: _otpController.text.trim(),
      );

      result.fold((l) {
        AppDialog.showErrorDialog(context: context, error: l.reason);
      }, (r) {
        _showSuccessDialog(context);
      });

      if (mounted) {
        AppLoading.turnOff(context: context);
      }
    } else {
      return;
    }
  }

  void _showSuccessDialog(BuildContext context) {
    AppDialog.show(
      context: context,
      barrierDismissible: false,
      buttons: [
        !widget.isLoggedIn
            ? AppButton(
                buttonTitle: context.localization.signIn,
                onPressed: () {
                  Modular.to.pop();
                  Modular.get<EmailCubit>().setNewEmail(widget.email);
                  AuthModuleHelper.goToSignInPage();
                },
              )
            : AppButton(
                buttonTitle: context.localization.close,
                onPressed: () {
                  Modular.to.pop();
                  Modular.to.pop();
                },
              ),
      ],
      descriptionWidget: Column(
        children: [
          Image.asset(AppImages.imageSuccess),
          Text(
            context.localization.success,
            style: CustomStyle.h5.smb.copyWith(color: AppColors.green),
          ),
          Text(
            context.localization.activateAccountSuccess,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.system6),
          ),
          8.verticalSpace,
        ],
      ),
    );
  }

  Widget _bottomWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 16,
        left: 16,
        right: 16,
        bottom: 16 +
            (Platform.isIOS
                ? (MediaQuery.of(context).viewInsets.bottom >
                        MediaQuery.of(context).viewPadding.bottom +
                            MediaQuery.of(context).viewPadding.top
                    ? 0
                    : MediaQuery.of(context).viewPadding.bottom)
                : 0),
      ),
      decoration: BoxDecoration(
        color: AppColors.system0,
        border: const Border(
          top: BorderSide(width: 1, color: AppColors.system3),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6C6C7E).withOpacity(0.1),
            offset: Offset.zero,
            blurRadius: 10,
          ),
        ],
      ),
      child: AppButton(
        buttonTitle: context.localization.continueTitle,
        titleStyle: CustomStyle.large.smb,
        onPressed: _activate,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Utils.hideKeyboard(),
      child: AppAnnotatedRegion(
        child: Scaffold(
          backgroundColor: AppColors.system1,
          appBar: AppTopBar(
            backgroundColor: AppColors.system1,
            title: context.localization.activateAccount,
            showShadow: false,
            leading: IconInkwell(
              path: AppIcons.iconChevronLeft,
              color: AppColors.neutral60,
              onTap: () {
                Modular.to.pop();
              },
              padding: const EdgeInsets.only(left: 8),
            ).paddingOnly(left: AppDimensions.baseMargin),
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        34.verticalSpace,
                        Image.asset(AppImages.imageSignUp),
                        40.verticalSpace,
                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            text: context.localization.otpSentToEmail,
                            style: CustomStyle.medium.regular
                                .copyWith(color: AppColors.system5),
                            children: [
                              TextSpan(
                                  text: Utils.hideEmail(email),
                                  style: CustomStyle.medium.medium
                                      .copyWith(color: AppColors.blue)),
                              TextSpan(
                                  text: context
                                      .localization.otpToActivateAccount),
                            ],
                          ),
                        ),
                        8.verticalSpace,
                        AppTextField(
                          label: context.localization.otpCode,
                          prefixIcon: AppIcons.iconPassword,
                          hintText: context.localization.inputOtpCode,
                          controller: _otpController,
                          validatorType: AppTextFieldValidatorType.otp,
                          formKey: _formKey,
                        ),
                        16.verticalSpace,
                      ],
                    ).paddingSymmetric(h: 16),
                  ),
                ),
              ),
              _bottomWidget(context),
            ],
          ),
        ),
      ),
    );
  }
}
