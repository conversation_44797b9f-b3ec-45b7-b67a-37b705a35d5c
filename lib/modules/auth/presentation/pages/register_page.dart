import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_annotated_region.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/app_loading.dart';
import 'package:saymee/core/components/app_top_bar.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/components/icon_inkwell.dart';
import 'package:saymee/core/components/inputs/app_text_field.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_dimensions.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_images.dart';
import 'package:saymee/core/constants/app_validator_type.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';
import 'package:saymee/core/extensions/num_extension.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/auth/data/repositories/auth_repository.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final formKey = GlobalKey<FormState>();
  final TextEditingController _fullnameController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _rePasswordController = TextEditingController();

  final _authRepository = Modular.get<AuthRepository>();

  @override
  void dispose() {
    _fullnameController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _rePasswordController.dispose();
    super.dispose();
  }

  void _register() async {
    Utils.hideKeyboard();
    if (formKey.currentState!.validate()) {
      if (mounted) {
        AppLoading.show(
            context: context, title: '${context.localization.registering}...');
      }

      final result = await _authRepository.register(
        email: _emailController.text.trim(),
        password: _passwordController.text.trim(),
        fullname: _fullnameController.text.trim(),
        username: _usernameController.text.trim(),
      );

      if (mounted) {
        AppLoading.turnOff(context: context);
      }

      result.fold((l) {
        AppDialog.showErrorDialog(context: context, error: l.reason);
      }, (r) {
        AuthModuleHelper.goToActivateAccountPage(
            email: _emailController.text.trim());
      });
    } else {
      return;
    }
  }

  Widget _bottomWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 16,
        left: 16,
        right: 16,
        bottom: 16 +
            (Platform.isIOS
                ? (MediaQuery.of(context).viewInsets.bottom >
                        MediaQuery.of(context).viewPadding.bottom +
                            MediaQuery.of(context).viewPadding.top
                    ? 0
                    : MediaQuery.of(context).viewPadding.bottom)
                : 0),
      ),
      decoration: BoxDecoration(
        color: AppColors.system0,
        border: const Border(
          top: BorderSide(width: 1, color: AppColors.system3),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6C6C7E).withOpacity(0.1),
            offset: Offset.zero,
            blurRadius: 10,
          ),
        ],
      ),
      child: AppButton(
        buttonTitle: context.localization.signUp,
        titleStyle: CustomStyle.large.smb,
        onPressed: _register,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Utils.hideKeyboard(),
      child: AppAnnotatedRegion(
        child: Scaffold(
          backgroundColor: AppColors.system1,
          appBar: AppTopBar(
            backgroundColor: AppColors.system1,
            title: context.localization.signUp,
            showShadow: false,
            leading: IconInkwell(
              path: AppIcons.iconChevronLeft,
              color: AppColors.neutral60,
              onTap: () {
                Modular.to.pop();
              },
              padding: const EdgeInsets.only(left: 8),
            ).paddingOnly(left: AppDimensions.baseMargin),
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Form(
                    key: formKey,
                    child: Column(
                      children: [
                        12.verticalSpace,
                        Image.asset(AppImages.imageSignUp),
                        40.verticalSpace,
                        Text(
                          context.localization.signUpWithEmail,
                          style: CustomStyle.h5.smb
                              .copyWith(color: AppColors.system8),
                        ),
                        // Text(
                        //   "To sign up, you'll need to enter your email and create a password.",
                        //   textAlign: TextAlign.center,
                        //   style: CustomStyle.medium.regular
                        //       .copyWith(color: AppColors.system5),
                        // ),
                        16.verticalSpace,
                        AppTextField(
                          label: context.localization.fullname,
                          prefixIcon: AppIcons.iconFullname,
                          hintText: context.localization.inputYourFullname,
                          controller: _fullnameController,
                          validatorType: AppTextFieldValidatorType.fullname,
                          formKey: formKey,
                        ),
                        const SizedBox(
                          height: AppDimensions.baseMargin,
                        ),
                        AppTextField(
                          label: context.localization.username,
                          prefixIcon: AppIcons.iconName,
                          hintText: context.localization.chooseAnUsername,
                          controller: _usernameController,
                          validatorType: AppTextFieldValidatorType.username,
                          formKey: formKey,
                        ),
                        const SizedBox(
                          height: AppDimensions.baseMargin,
                        ),
                        AppTextField(
                          label: "Email",
                          prefixIcon: AppIcons.iconEmail,
                          autofillHints: const [AutofillHints.email],
                          hintText: context.localization.enterEmail,
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          validatorType: AppTextFieldValidatorType.email,
                          formKey: formKey,
                        ),
                        const SizedBox(
                          height: AppDimensions.baseMargin,
                        ),
                        AppTextField(
                          label: context.localization.password,
                          autofillHints: const [AutofillHints.newPassword],
                          hintText: context.localization.enterPassword,
                          prefixIcon: AppIcons.iconPassword,
                          controller: _passwordController,
                          isPassword: true,
                          keyboardType: TextInputType.visiblePassword,
                          validatorType: AppTextFieldValidatorType.password,
                          formKey: formKey,
                        ),
                        const SizedBox(
                          height: AppDimensions.baseMargin,
                        ),
                        AppTextField(
                          label: context.localization.password,
                          hintText: context.localization.confirmPassword,
                          prefixIcon: AppIcons.iconPassword,
                          controller: _rePasswordController,
                          isPassword: true,
                          passwordController: _passwordController,
                          keyboardType: TextInputType.visiblePassword,
                          validatorType: AppTextFieldValidatorType.password,
                          formKey: formKey,
                        ),
                        16.verticalSpace,
                      ],
                    ).paddingSymmetric(h: 16),
                  ),
                ),
              ),
              _bottomWidget(context),
            ],
          ),
        ),
      ),
    );
  }
}
