import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/helpers/general_helper.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/utils/fcm_service.dart';
import 'package:saymee/core/utils/push_ios_notification_service.dart';
import 'package:saymee/modules/auth/data/repositories/auth_repository.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';

import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository repository;

  final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();

  AuthBloc({required this.repository}) : super(AuthStateInitial()) {
    on<AuthEvent>((event, emit) async {
      if (event is AuthSignInRequested) {
        emit(const AuthStateSignIn.pending());

        final result = await repository.login(
          email: event.email,
          password: event.password,
          type: event.type,
          appleUserId: event.appleUserId,
          fullname: event.fullname,
          language: GeneralHelper.deviceLanguageCode,
        );

        result.fold((l) {
          emit(AuthStateSignIn.rejected(
            statusCode: l.statusCode,
            code: l.code,
            reason: l.reason,
          ));
        }, (r) {
          String? accessToken = r['accessToken'];
          UserModel? user = r['user'];
          sharedPreferenceHelper.setAccessToken(token: accessToken ?? '');
          sharedPreferenceHelper.setEmail(email: event.email);
          if (user?.userId != null) {
            sharedPreferenceHelper.setUserId(userId: user!.userId!);
          }

          emit(AuthStateSignIn.fulfilled(
            accessToken: accessToken,
            user: user,
          ));

          if (Platform.isIOS) {
            PushIOSNotificationService.requestNotificationPermission();
          } else {
            FcmService.requestPermission();
          }
        });
      } else if (event is AuthSignOutRequested) {
        emit(AuthStateInitial());
      }
    });
  }
}
