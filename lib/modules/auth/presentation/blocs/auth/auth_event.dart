import 'package:equatable/equatable.dart';

sealed class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

final class AuthSignInRequested extends AuthEvent {
  final String email;
  final String? password;
  final String type;
  final String? appleUserId;
  final String? fullname;

  const AuthSignInRequested({
    required this.email,
    this.password,
    required this.type,
    this.appleUserId,
    this.fullname,
  });
}

final class AuthRegisterRequested extends AuthEvent {
  final String email;
  final String password;
  final String type;
  final String fullname;

  const AuthRegisterRequested({
    required this.email,
    required this.password,
    required this.type,
    required this.fullname,
  });
}

final class AuthSignOutRequested extends AuthEvent {}
