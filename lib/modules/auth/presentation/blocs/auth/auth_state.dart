import 'package:equatable/equatable.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';

sealed class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

final class AuthStateInitial extends AuthState {}

final class AuthStateSignIn extends AuthState {
  final bool isRequesting;
  final String reason;
  final String statusCode;
  final String code;
  final String? accessToken;
  final UserModel? user;

  @override
  List<Object?> get props => [
        isRequesting,
        reason,
        statusCode,
        code,
        accessToken,
        user,
      ];

  const AuthStateSignIn._({
    this.isRequesting = false,
    this.reason = '',
    this.statusCode = '',
    this.code = '',
    this.accessToken,
    this.user,
  });

  const AuthStateSignIn.initial() : this._();
  const AuthStateSignIn.pending() : this._(isRequesting: true);
  const AuthStateSignIn.rejected({
    required String statusCode,
    required String code,
    required String reason,
  }) : this._(
          statusCode: statusCode,
          code: code,
          reason: reason,
        );
  const AuthStateSignIn.fulfilled({
    String? accessToken,
    UserModel? user,
  }) : this._(
          accessToken: accessToken,
          user: user,
        );
}
