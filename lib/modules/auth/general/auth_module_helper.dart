import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/modules/auth/general/auth_module_routes.dart';
import 'package:saymee/modules/auth/presentation/blocs/auth/auth_bloc.dart';
import 'package:saymee/modules/auth/presentation/blocs/auth/auth_event.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_bloc.dart';
import 'package:saymee/modules/chat/presentation/blocs/room_list/room_list_event.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_bloc.dart';
import 'package:saymee/modules/subscription/presentation/blocs/my_subscriptions/my_subscription_event.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_event.dart';

class AuthModuleHelper {
  AuthModuleHelper._();

  static bool checkAccessTokenValid() {
    final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();

    final String accessToken = sharedPreferenceHelper.getAccessToken() ?? '';

    if (accessToken.isNotEmpty) {
      return true;
    }

    return false;
  }

  static void signOut() {
    final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();
    final authBloc = Modular.get<AuthBloc>();
    final userInfoBloc = Modular.get<UserInfoBloc>();
    final roomListBloc = Modular.get<RoomListBloc>();
    final mySubscriptionBloc = Modular.get<MySubscriptionBloc>();
    authBloc.add(AuthSignOutRequested());
    userInfoBloc.add(UserResetInfoRequested());
    roomListBloc.add(RoomResetRequested());
    mySubscriptionBloc.add(SubscriptionResetRequested());

    sharedPreferenceHelper.removeAccessToken();
    sharedPreferenceHelper.removeUserId();
  }

  static void navigateToSignInPage() {
    Modular.to.navigate(
      '${AppRoutes.moduleAuth}${AuthModuleRoutes.signIn}',
    );
  }

  static void goToRegisterPage() {
    Modular.to.pushNamed('${AppRoutes.moduleAuth}${AuthModuleRoutes.register}');
  }

  static void goToActivateAccountPage({
    required String email,
    String? otp,
    bool isLoggedIn = false,
  }) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleAuth}${AuthModuleRoutes.activateAccount}',
      arguments: {
        'email': email,
        'otp': otp,
        'isLoggedIn': isLoggedIn,
      },
    );
  }

  static void goToSignInPage({String? email}) {
    Modular.to.pushNamedAndRemoveUntil(
        '${AppRoutes.moduleAuth}${AuthModuleRoutes.signIn}', (route) => false);
  }

  static void goToForgotPasswordPage() {
    Modular.to
        .pushNamed('${AppRoutes.moduleAuth}${AuthModuleRoutes.forgotPassword}');
  }

  static void goToResetPasswordPage({required String email}) {
    Modular.to.pushNamed(
      '${AppRoutes.moduleAuth}${AuthModuleRoutes.resetPassword}',
      arguments: {
        'email': email,
      },
    );
  }
}
