import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_environment.dart';
import 'package:saymee/core/constants/app_language.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/network/dio_client.dart';
import 'package:saymee/modules/app/app_module.dart';
import 'package:saymee/modules/auth/auth_module.dart';
import 'package:saymee/modules/chat/chat_module.dart';
import 'package:saymee/modules/subscription/subscription_module.dart';
import 'package:saymee/modules/user/user_module.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MainModule extends Module {
  final SharedPreferences sharedPreferences;

  MainModule({required this.sharedPreferences});

  @override
  void binds(Injector i) {
    super.binds(i);

    i.addSingleton(
        () => SharedPreferenceHelper(sharedPreferences: sharedPreferences));

    i.addSingleton(() => Dio());

    i.addSingleton(() => DioClient(Modular.get<Dio>(), AppEnvironment.apiUrl));

    i.addSingleton(() => AppLanguageBloc());
  }

  @override
  List<Module> get imports => [
        AuthModule(),
      ];

  @override
  void routes(RouteManager r) {
    super.routes(r);

    r.module(AppRoutes.moduleAuth, module: AuthModule());
  }
}
