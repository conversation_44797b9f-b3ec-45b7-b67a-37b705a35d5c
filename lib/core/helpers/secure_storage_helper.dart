import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:uuid/uuid.dart';

class SecureStorageHelper {
  static Future<void> init() async {
    var uuid = const Uuid();
    const secureStorage = FlutterSecureStorage();
    String kUUID = "UUID";
    AndroidOptions aOptions = const AndroidOptions();
    IOSOptions iOptions =
        const IOSOptions(accessibility: KeychainAccessibility.first_unlock);

    final id = await secureStorage.read(
      key: kUUID,
      aOptions: aOptions,
      iOptions: iOptions,
    );
    if (id == null) {
      String newId = uuid.v4();
      await secureStorage.write(
        key: kUUID,
        value: newId,
        aOptions: aOptions,
        iOptions: iOptions,
      );
      globalUuid = newId;
    } else {
      globalUuid = id;
    }

    Utils.debugLogSuccess(globalUuid, tagName: 'SecureStorageHelper');
  }
}
