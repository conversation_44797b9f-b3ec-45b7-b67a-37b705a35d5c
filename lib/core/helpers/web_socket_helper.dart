import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_environment.dart';
import 'package:saymee/core/constants/app_keys.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/utils/channel_service.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/chat/data/models/button_model.dart';
import 'package:saymee/modules/chat/data/models/message_model.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/user/data/models/user_model.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';

final class WebSocketHelper {
  static final WebSocketHelper _instance = WebSocketHelper._internal();
  factory WebSocketHelper() => _instance;

  WebSocketHelper._internal();
  WebSocket? _webSocket;
  bool _isConnected = false;
  bool _shouldReconnect = true;
  bool _isAuthorizeUser = false;
  bool _isSendLoginEvent = false;
  final int _retryDelay = 3; // Initial retry delay in seconds
  int _retryInterval = 0;
  final int maxRetryInterval = 10;
  String tagName = 'WebSocketHelper';
  final _messageController =
      StreamController<MessageModel>.broadcast(sync: true);

  Stream<MessageModel> get messages => _messageController.stream;

  bool get isUserNotConnect => (!_isAuthorizeUser && !_isSendLoginEvent);

  Future<void> connect() async {
    if (_isConnected) return;

    try {
      Utils.debugLogSuccess('Connecting to WebSocket...', tagName: tagName);
      _webSocket = await WebSocket.connect(AppEnvironment.wsUrl);
      _webSocket?.pingInterval = Duration(seconds: _retryDelay);
      _isConnected = true;
      _shouldReconnect = true;
      if (_retryInterval > 1) {
        Utils.showToast("Connection restored");
      }
      _retryInterval = 0;
      Utils.debugLogSuccess('WebSocket connected.', tagName: tagName);

      if (!_isAuthorizeUser && !_isSendLoginEvent) {
        final user = Modular.get<UserInfoBloc>().state.user;
        final accessToken =
            Modular.get<SharedPreferenceHelper>().getAccessToken();
        if (user != null) {
          sendUserLoginEvent(
            userId: user.userId ?? -1,
            token: accessToken ?? "",
          );
        }
      }

      _webSocket?.listen(
        (data) {
          //add data to blo
          try {
            final response = jsonDecode(data);
            Utils.debugLogSuccess('WebSocket message: $response',
                tagName: tagName);
            if (response is Map<String, dynamic>) {
              String command = response['command'];
              switch (command) {
                case WebSocketCommand.userLogin:
                  if (response['result'] == 'success') {
                    _isAuthorizeUser = true;
                  } else {
                    _isSendLoginEvent = false;
                    _isAuthorizeUser = false;
                  }
                  break;
                case WebSocketCommand.userMessage:
                  Utils.debugLogSuccess('WebSocket message: $response',
                      tagName: tagName);
                  if (response['result'] == null) {
                    // Utils.debugLogSuccess('WebSocket message: $data',
                    //     tagName: tagName);
                    MessageModel message = MessageModel.fromJson(
                        response["message"] as Map<String, dynamic>);
                    UserModel? user = response['user'] != null
                        ? UserModel.fromJson(
                            response['user'] as Map<String, dynamic>)
                        : null;
                    message = message.copyWith(
                      userId: user?.userId,
                      user: user,
                      time: DateTime.now(),
                      command: WebSocketCommand.userMessage,
                    );
                    _messageController.add(message);
                    ChatModuleHelper.addMessageToRoom(message);
                  }
                  break;
                case WebSocketCommand.botMessageChunk:
                  // Utils.debugLogSuccess('WebSocket message: $data',
                  //     tagName: tagName);
                  MessageModel message = MessageModel.fromJson(response);
                  message = message.copyWith(time: DateTime.now());
                  _messageController.add(message);
                  break;
                case WebSocketCommand.botMessageComplete:
                  // Utils.debugLogSuccess('WebSocket message: $data',
                  //     tagName: tagName);
                  MessageModel message = MessageModel.fromJson(response);
                  message = message.copyWith(time: DateTime.now());
                  _messageController.add(message);
                  ChatModuleHelper.addMessageToRoom(message);
                  break;
                case WebSocketCommand.alert:
                  String title = response['title'] ?? '';
                  String content = response['content'] ?? '';
                  ButtonModel? button = response['button'] != null
                      ? ButtonModel.fromJson(response['button'])
                      : null;
                  FocusScope.of(AppKeys.navigatorKey.currentContext!).unfocus();
                  ChannelService.showAlert(
                    title: title,
                    message: content,
                    buttonTitle: button?.title ?? '',
                    url: button?.link ?? '',
                  );
                  break;
                default:
              }
            }
          } catch (e) {
            Utils.debugLogError('Decode message error: $e', tagName: tagName);
          }
        },
        onDone: () {
          Utils.debugLogWarning(
            'WebSocket closed. ${_shouldReconnect ? 'Retrying...' : ''}',
            tagName: tagName,
          );
          _isConnected = false;
          _isAuthorizeUser = false;
          _isSendLoginEvent = false;
          if (_retryInterval == maxRetryInterval) {
            Utils.showToast("Connection error. Check your network!");
          }
          _retryInterval++;
          _retryConnection();
        },
        onError: (error) {
          Utils.debugLogError('WebSocket error: $error', tagName: tagName);
          _isConnected = false;
          _isAuthorizeUser = false;
          _isSendLoginEvent = false;
          if (_retryInterval == maxRetryInterval) {
            Utils.showToast("Connection error. Check your network!");
          }
          _retryInterval++;
          _retryConnection();
        },
        cancelOnError: true,
      );
    } catch (e) {
      Utils.debugLogError('Failed to connect: $e', tagName: tagName);
      _isConnected = false;
      _isAuthorizeUser = false;
      if (_retryInterval == maxRetryInterval) {
        Utils.showToast("Connection error. Check your network!");
      }
      _retryInterval++;
      _retryConnection();
    }
  }

  void _retryConnection() {
    if (_isConnected || !_shouldReconnect) {
      return;
    }

    Utils.debugLogWarning(
      'Retrying connection in $_retryDelay seconds...',
      tagName: tagName,
    );
    Future.delayed(Duration(seconds: _retryDelay), () {
      if (!_isConnected) {
        connect();
      }
    });
  }

  void disconnect() {
    _shouldReconnect = false;
    if (_webSocket != null) {
      _webSocket!.close();
      _isConnected = false;
      _webSocket = null;
      _isAuthorizeUser = false;
      _isSendLoginEvent = false;
      Utils.debugLogWarning('WebSocket disconnected.', tagName: tagName);
    }
  }

  void sendUserLoginEvent({
    required int userId,
    required String token,
  }) {
    if (_isConnected && _webSocket != null) {
      final data = {
        'command': WebSocketCommand.userLogin,
        'userId': userId,
        'token': token,
        'uuid': globalUuid,
      };

      Utils.debugLogWarning('WebSocket sendUserLoginEvent: $data',
          tagName: tagName);

      _webSocket?.add(jsonEncode(data));
      _isSendLoginEvent = true;
    } else {
      Utils.debugLogError(
        'Unable to send message. WebSocket not connected.',
        tagName: tagName,
      );
    }
  }

  void sendUserMessage({
    required int roomId,
    required String content,
    required String type,
    String? image,
  }) {
    if (_isConnected && _webSocket != null) {
      final data = {
        'command': WebSocketCommand.userMessage,
        'roomId': roomId,
        'content': content,
        'type': type,
        'image': image,
      };

      _webSocket?.add(jsonEncode(data));
    } else {
      Utils.debugLogError(
        'Unable to send message. WebSocket not connected.',
        tagName: tagName,
      );
    }
  }
}
