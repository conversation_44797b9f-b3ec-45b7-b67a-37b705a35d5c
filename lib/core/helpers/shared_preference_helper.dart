import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferenceHelper {
  static const String kAccessToken = "ACCESS_TOKEN";
  static const String kAppLanguage = "APP_LANGUAGE";
  static const String kFirstOpenApp = "FIRST_OPEN_APP";
  static const String kNotificationPermission = 'NOTIFICATION_PERMISSION';
  static const String kEmail = 'EMAIL';
  static const String kMessageId = 'MESSAGE_ID';
  static const String kUserId = 'USER_ID';

  final SharedPreferences sharedPreferences;

  SharedPreferenceHelper({required this.sharedPreferences});

  Future<void> setAccessToken({required String token}) async {
    await sharedPreferences.setString(kAccessToken, token);
  }

  Future<void> removeAccessToken() async {
    await sharedPreferences.remove(kAccessToken);
  }

  String? getAccessToken() {
    final token = sharedPreferences.getString(kAccessToken);
    return token;
  }

  // app language
  String? getAppLanguage() {
    final appLanguage = sharedPreferences.getString(kAppLanguage);
    return appLanguage;
  }

  Future<void> setAppLanguage({required String appLanguage}) async {
    await sharedPreferences.setString(kAppLanguage, appLanguage);
  }

  // first open app
  bool checkFirstOpenApp() {
    final firstOpenApp = sharedPreferences.getBool(kFirstOpenApp) ?? true;
    return firstOpenApp;
  }

  Future<void> setFirstOpenApp({required bool firstOpenApp}) async {
    await sharedPreferences.setBool(kFirstOpenApp, firstOpenApp);
  }

  // app language
  String? getNotificationPermission() {
    final permission = sharedPreferences.getString(kNotificationPermission);
    return permission;
  }

  Future<void> setNotificationPermission({required String status}) async {
    await sharedPreferences.setString(kNotificationPermission, status);
  }

  String? getEmail() {
    final email = sharedPreferences.getString(kEmail);
    return email;
  }

  Future<void> setEmail({required String email}) async {
    await sharedPreferences.setString(kEmail, email);
  }

  String? getMessage() {
    final message = sharedPreferences.getString(kMessageId);
    return message;
  }

  Future<void> setMessage({required String message}) async {
    await sharedPreferences.setString(kMessageId, message);
  }

  int? getUserId() {
    final userId = sharedPreferences.getInt(kUserId);
    return userId;
  }

  Future<void> setUserId({required int userId}) async {
    await sharedPreferences.setInt(kUserId, userId);
  }

  Future<void> removeUserId() async {
    await sharedPreferences.remove(kUserId);
  }
}
