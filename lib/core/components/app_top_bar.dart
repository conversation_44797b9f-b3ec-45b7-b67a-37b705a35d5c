import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_dimensions.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';

class AppTopBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final String? subTitle;
  final Widget? leading;
  final List<Widget>? actions;
  final Size height;
  final Color backgroundColor;
  final Color titleColor;
  final Color subTitleColor;
  final LinearGradient? linearGradient;
  final bool showShadow;
  final bool overlayStatusbar;
  final double? leadingWidth;
  final SystemUiOverlayStyle? systemOverlayStyle;
  final String? avatar;

  const AppTopBar({
    this.title,
    this.subTitle,
    this.leading,
    this.actions,
    this.height = const Size.fromHeight(AppDimensions.appBarHeight),
    this.backgroundColor = Colors.white,
    this.titleColor = AppColors.neutral90,
    this.subTitleColor = AppColors.primaryMain,
    this.linearGradient,
    this.showShadow = true,
    this.overlayStatusbar = true,
    this.leadingWidth = 120,
    this.systemOverlayStyle,
    this.avatar,
    super.key,
  }) : preferredSize = height;

  @override
  final Size preferredSize;

  @override
  Widget build(BuildContext context) {
    return overlayStatusbar
        ? Container(
            decoration: showShadow
                ? BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 5,
                        blurRadius: 5,
                        offset:
                            const Offset(0, 2), // changes position of shadow
                      ),
                    ],
                  )
                : null,
            child: AppBar(
              titleSpacing: 0,
              systemOverlayStyle: systemOverlayStyle,
              scrolledUnderElevation: 0,
              leadingWidth: leadingWidth,
              backgroundColor: linearGradient != null ? null : backgroundColor,
              flexibleSpace: linearGradient != null
                  ? Container(
                      decoration: BoxDecoration(
                        gradient: linearGradient,
                      ),
                    )
                  : Container(),
              title: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: leadingWidth != null
                    ? CrossAxisAlignment.center
                    : CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (avatar != null && avatar!.isNotEmpty)
                        ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: CachedNetworkImage(
                            imageUrl: Utils.convertImageUrl(avatar),
                            width: 40,
                            height: 40,
                            placeholder: (context, url) => Container(),
                            errorWidget: (context, error, stackTrace) {
                              return Container();
                            },
                          ),
                        ).paddingOnly(right: 8),
                      Column(
                        crossAxisAlignment: leadingWidth != null
                            ? CrossAxisAlignment.center
                            : CrossAxisAlignment.start,
                        children: [
                          if (title != null)
                            Text(
                              title!,
                              maxLines: 3,
                              textAlign: TextAlign.center,
                              style: CustomStyle.large.smb
                                  .copyWith(color: titleColor),
                            ),
                          if (subTitle != null) ...[
                            Text(
                              subTitle!,
                              style: CustomStyle.small.regular
                                  .copyWith(color: subTitleColor),
                            ),
                          ]
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              actions: actions,
              centerTitle: leadingWidth != null,
              leading: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [leading ?? const Text("")]),
            ),
          )
        : SafeArea(
            child: Container(
              decoration: showShadow
                  ? BoxDecoration(
                      boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            spreadRadius: 5,
                            blurRadius: 5,
                            offset: const Offset(
                                0, 2), // changes position of shadow
                          ),
                        ],
                      border: const Border(
                          bottom:
                              BorderSide(width: 1, color: Color(0xffE5E9EC))))
                  : null,
              child: AppBar(
                titleSpacing: 0,
                systemOverlayStyle: systemOverlayStyle,
                scrolledUnderElevation: 0,
                leadingWidth: leadingWidth,
                backgroundColor:
                    linearGradient != null ? null : backgroundColor,
                flexibleSpace: linearGradient != null
                    ? Container(
                        decoration: BoxDecoration(
                          gradient: linearGradient,
                        ),
                      )
                    : Container(),
                title: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: leadingWidth != null
                        ? CrossAxisAlignment.center
                        : CrossAxisAlignment.start,
                    children: [
                      if (title != null)
                        Text(
                          title!,
                          maxLines: 3,
                          textAlign: TextAlign.center,
                          style:
                              CustomStyle.large.smb.copyWith(color: titleColor),
                        ),
                      if (subTitle != null)
                        Text(
                          subTitle!,
                          style: CustomStyle.small.smb
                              .copyWith(color: subTitleColor),
                        )
                    ]),
                actions: actions,
                centerTitle: true,
                leading: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [leading ?? const Text("")]),
              ),
            ),
          );
  }
}
