import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:saymee/core/constants/app_colors.dart';

class AppAnnotatedRegion extends StatelessWidget {
  const AppAnnotatedRegion(
      {super.key, required this.child, this.isDarkMode = false});

  final Widget child;
  final bool isDarkMode;

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: isDarkMode
          ? const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent, // Transparent status bar
              statusBarIconBrightness:
                  Brightness.light, // Light status bar icons
              statusBarBrightness: Brightness.dark,
              systemNavigationBarIconBrightness: Brightness.light,
              systemNavigationBarColor: AppColors.neutral30,
            )
          : const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent, // Transparent status bar
              statusBarIconBrightness:
                  Brightness.dark, // Light status bar icons
              statusBarBrightness: Brightness.light,
              systemNavigationBarIconBrightness: Brightness.dark,
              systemNavigationBarColor: Colors.transparent,
            ),
      child: child,
    );
  }
}
