import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:saymee/core/constants/app_colors.dart';

class AppSwitcher extends StatefulWidget {
  final bool switchValue;
  final void Function(bool) callback; // hàm setState của swicher
  final Color activeColor;
  final Color thumbColor;
  final Color trackColor;
  final double scale; // không thể chỉnh dài rộng của <PERSON>, chỉ có thể scale

  const AppSwitcher({
    required this.switchValue,
    required this.callback,
    this.activeColor = const Color(0xFF6CCC09),
    this.thumbColor = Colors.white,
    this.trackColor = AppColors.system4,
    this.scale = 1,
    super.key,
  });

  @override
  State<AppSwitcher> createState() => _AppSwitcherState();
}

class _AppSwitcherState extends State<AppSwitcher> {
  late bool switchValue;

  @override
  void initState() {
    switchValue = widget.switchValue;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Transform.scale(
      scale: widget.scale,
      child: CupertinoSwitch(
          activeColor: widget.activeColor,
          thumbColor: widget.thumbColor,
          trackColor: widget.trackColor,
          value: switchValue,
          onChanged: (bool? value) {
            if (value != null) {
              widget.callback(value);
              setState(() {
                switchValue = value;
              });
            }
          }),
    );
  }
}
