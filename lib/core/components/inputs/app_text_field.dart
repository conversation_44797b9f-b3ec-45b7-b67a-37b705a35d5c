import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_icons.dart';
import 'package:saymee/core/constants/app_validator_type.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/widget_extension.dart';
import 'package:saymee/core/utils/utils.dart';

String? outlineType() {
  return "outline";
}

String? solidType() {
  return "solid";
}

enum AppTextFieldStyleType {
  outline(textFieldStyleTypeFunction: outlineType),
  solid(textFieldStyleTypeFunction: solidType);

  final String? Function() textFieldStyleTypeFunction;

  const AppTextFieldStyleType({required this.textFieldStyleTypeFunction});
}

class AppTextField extends StatefulWidget {
  // --- props: ---
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final bool isPassword;
  final String? prefixIcon;
  final Widget? suffixIcon;
  final AppTextFieldValidatorType? validatorType;
  final String label;
  final AppTextFieldStyleType? styleType;
  final String? hintText;
  final void Function()? onPressedSuffixIcon;
  final bool disabled;
  final bool autoFocus;
  final int maxLines;
  final TextStyle? hintStyle;
  final TextStyle? labelStyle;
  final List<TextInputFormatter>? inputFormatters;
  final TextEditingController? passwordController;
  final Iterable<String>? autofillHints;
  final GlobalKey<FormState>? formKey;

  // --- constructor: ---
  const AppTextField({
    super.key,
    this.disabled = false,
    this.onPressedSuffixIcon,
    this.suffixIcon,
    this.hintText,
    this.styleType = AppTextFieldStyleType.outline,
    required this.label,
    this.validatorType,
    this.prefixIcon,
    this.isPassword = false,
    this.keyboardType = TextInputType.text,
    this.controller,
    this.autoFocus = false,
    this.maxLines = 1,
    this.hintStyle,
    this.labelStyle,
    this.inputFormatters,
    this.passwordController,
    this.autofillHints,
    this.formKey,
  });

  @override
  State<AppTextField> createState() => _AppTextFieldState();
}

class _AppTextFieldState extends State<AppTextField> {
  // --- params: ---
  late FocusNode _focus;
  bool _focusState = false;
  bool _showSuffixIcon = false;
  bool _obscureText = false;
  bool _isError = false;
  String _errorMessage = "";

  // --- methods ---
  void _setFocusState() {
    if (_focus.hasFocus == false) {
      setState(() {
        _focusState = false;
      });
    } else {
      setState(() {
        _focusState = true;
      });
    }
  }

  // show or hide suffix icon when focus or blur input or when input is empty
  void _setShowSuffixIcon() {
    if (_focus.hasFocus == false || widget.controller?.text == "") {
      setState(() {
        _showSuffixIcon = false;
      });
    } else {
      setState(() {
        _showSuffixIcon = true;
      });
    }
  }

  // show content of input
  void _showInputContent() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  void _clearInput() {
    widget.controller?.clear();
  }

  Color? _getFloatingLabelColor() {
    return _isError
        ? AppColors.redMain
        : _focusState
            ? AppColors.blue
            : AppColors.system8;
  }

  @override
  void initState() {
    _focus = FocusNode();
    _focus.addListener(() {
      if (mounted) {
        _setFocusState();
        _setShowSuffixIcon();
      }
    });
    widget.controller?.addListener(() {
      if (mounted) {
        _setShowSuffixIcon();
        _setFocusState();
      }
      // _onSubmit();
    });

    // set content to *** when input is password
    if (widget.isPassword == true) {
      _obscureText = true;
    }

    super.initState();
  }

  @override
  void dispose() {
    _focus.dispose();
    super.dispose();
  }

  // @override
  // void didUpdateWidget(covariant AppTextField oldWidget) {
  //   if (oldWidget.controller != widget.controller) {
  //     setState(() {});
  //   }
  //   super.didUpdateWidget(oldWidget);
  // }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.only(left: widget.prefixIcon != null ? 0 : 16),
          decoration: BoxDecoration(
            color: widget.disabled
                ? AppColors.system3
                : (widget.styleType == AppTextFieldStyleType.outline
                    ? AppColors.neutral
                    : _isError
                        ? AppColors.redMain
                        : (_focusState ? AppColors.blue : AppColors.neutral)),
            borderRadius: BorderRadius.circular(16),
            border: widget.disabled
                ? null
                : (widget.styleType == AppTextFieldStyleType.outline
                    ? Border.all(
                        color: _isError
                            ? AppColors.redMain
                            : (_focusState
                                ? AppColors.blue
                                : AppColors.system3),
                        width: 1,
                      )
                    : null),
          ),
          child: TextFormField(
            autofocus: widget.autoFocus,
            autofillHints: widget.autofillHints,
            enabled: !widget.disabled,
            controller: widget.controller,
            maxLines: widget.maxLines,
            minLines: 1,
            style:
                CustomStyle.medium.regular.copyWith(color: AppColors.neutral90),
            onTapOutside: (event) => Utils.hideKeyboard(),
            cursorColor: AppColors.blue,
            focusNode: _focus,
            keyboardType: widget.keyboardType,
            obscureText: _obscureText,
            inputFormatters: widget.inputFormatters,
            onChanged: (value) {
              widget.formKey?.currentState?.validate();
            },
            validator: (value) {
              String? errorMessage = widget.validatorType?.validatorFunction(
                  value,
                  password: widget.passwordController?.text.trim());
              setState(() {
                _isError = errorMessage != null;
                _errorMessage = errorMessage ?? "";
              });
              if (errorMessage != null) {
                return errorMessage;
              }
              return null;
            },

            // --- style of input ---
            decoration: InputDecoration(
              floatingLabelBehavior:
                  widget.hintText != null ? FloatingLabelBehavior.always : null,
              hintText: widget.hintText,
              hintStyle: widget.hintStyle ??
                  CustomStyle.medium.regular.copyWith(color: AppColors.system5),
              floatingLabelStyle: widget.labelStyle
                      ?.copyWith(color: _getFloatingLabelColor()) ??
                  CustomStyle.large.medium
                      .copyWith(color: _getFloatingLabelColor()),
              labelText: widget.label,
              errorStyle: const TextStyle(height: 0.01, fontSize: 0),
              border: InputBorder.none,
              prefixIcon: widget.prefixIcon != null
                  ? Container(
                      height: 21,
                      width: 21,
                      alignment: Alignment.center,
                      child: SvgPicture.asset(
                        widget.prefixIcon!,
                        colorFilter: ColorFilter.mode(
                          _isError ? AppColors.redMain : AppColors.blue,
                          BlendMode.srcIn,
                        ),
                      ),
                    )
                  : null,
              suffixIcon: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween, // added line
                mainAxisSize: MainAxisSize.min, // added line
                children: <Widget>[
                  if (_showSuffixIcon) ...[
                    IconButton(
                      iconSize: 21,
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      icon: const Icon(
                        Icons.close,
                        color: AppColors.neutral80,
                        size: 20,
                      ),
                      onPressed: () => {_clearInput()},
                    ),
                  ],
                  if ((widget.isPassword || widget.suffixIcon != null) &&
                      _showSuffixIcon) ...[
                    const SizedBox(
                        height: 35,
                        width: 1,
                        child: VerticalDivider(
                          color: AppColors.neutral80,
                        )),
                  ],
                  if (widget.isPassword) ...[
                    IconButton(
                      iconSize: 21,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      icon: _obscureText
                          ? SvgPicture.asset(
                              AppIcons.iconGrayEye,
                              colorFilter: const ColorFilter.mode(
                                  AppColors.neutral80, BlendMode.srcIn),
                            )
                          : SvgPicture.asset(
                              AppIcons.iconGrayEyeSlash,
                              colorFilter: const ColorFilter.mode(
                                  AppColors.neutral80, BlendMode.srcIn),
                            ),
                      onPressed: () {
                        _showInputContent();
                      },
                    )
                  ],
                  if (widget.suffixIcon != null && !widget.isPassword) ...[
                    IconButton(
                      iconSize: 21,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      icon: widget.suffixIcon!,
                      onPressed: () => {widget.onPressedSuffixIcon?.call()},
                    )
                  ]
                ],
              ),
            ),
          ),
        ),
        Container(
          child: _isError
              ? Text(
                  _errorMessage,
                  style:
                      const TextStyle(fontSize: 11, color: AppColors.redMain),
                ).paddingOnly(top: 2)
              : const SizedBox.shrink(),
        ),
      ],
    );
  }
}
