import 'package:flutter/material.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_dimensions.dart';
import 'package:saymee/core/constants/custom_style.dart';

class AppLoading {
  DialogRoute? dialogRoute;

  static final AppLoading _instance = AppLoading._internal();

  AppLoading._internal();

  static void show({
    required BuildContext context,
    String title = "",
    bool isTransparent = false,
    bool barrierDismissible = false,
  }) {
    if (_instance.dialogRoute != null && _instance.dialogRoute!.isActive) {
      return;
    }

    _instance.dialogRoute = DialogRoute(
      context: context,
      barrierColor: Colors.black54,
      barrierDismissible:
          barrierDismissible, // User must not close the dialog manually
      builder: (context) => Center(
          child: Container(
        constraints:
            BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 1 / 2),
        padding: const EdgeInsets.all(AppDimensions.baseMargin),
        decoration: BoxDecoration(
          color: AppColors.neutral,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              color: AppColors.blue,
            ),
            if (title.isNotEmpty)
              Column(
                children: [
                  const SizedBox(
                    height: AppDimensions.base,
                  ),
                  DefaultTextStyle(
                    style: CustomStyle.medium.regular
                        .copyWith(color: AppColors.neutral80),
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
          ],
        ),
      )),
    );
    Navigator.of(context).push(_instance.dialogRoute!);
  }

  static void turnOff({required BuildContext context}) {
    if (_instance.dialogRoute != null && _instance.dialogRoute!.isActive) {
      Navigator.of(context).removeRoute(_instance.dialogRoute!);
    }
  }
}
