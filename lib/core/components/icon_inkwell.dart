import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class IconInkwell extends StatefulWidget {
  const IconInkwell({
    super.key,
    required this.path,
    required this.color,
    this.onTap,
    this.padding,
  });

  final String path;
  final Color color;
  final VoidCallback? onTap;
  final EdgeInsets? padding;

  @override
  State<IconInkwell> createState() => _IconInkwellState();
}

class _IconInkwellState extends State<IconInkwell> {
  bool _onPressed = false;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      splashFactory: NoSplash.splashFactory,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTapDown: (details) {
        if (mounted) {
          setState(() {
            _onPressed = true;
          });
        }
      },
      onTapUp: (details) {
        Future.delayed(const Duration(milliseconds: 0), () {
          if (mounted) {
            setState(() {
              _onPressed = false;
            });
          }
        });
      },
      onTapCancel: () {
        if (mounted) {
          setState(() {
            _onPressed = false;
          });
        }
      },
      child: Padding(
        padding: widget.padding ?? EdgeInsets.zero,
        child: SvgPicture.asset(
          widget.path,
          colorFilter: ColorFilter.mode(
            _onPressed ? widget.color.withOpacity(0.5) : widget.color,
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }
}
