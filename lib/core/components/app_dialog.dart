import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_dimensions.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/localized_extension.dart';

class AppDialog {
  static void show({
    required BuildContext context,
    required List<Widget> buttons,
    String? title,
    String? description,
    Widget? descriptionWidget,
    Widget? icon,
    double borderRadius = 12,
    TextStyle? titleStyle,
    TextStyle? descriptionStyle,
    Color backgroundColor = AppColors.neutral,
    bool barrierDismissible = true,
    bool isHorizontalButton = true,
  }) {
    showDialog(
        barrierDismissible: barrierDismissible,
        context: context,
        builder: ((context) {
          return PopScope(
            canPop: barrierDismissible,
            child: AppDialogWidget(
              buttons: buttons,
              title: title,
              icon: icon,
              description: description,
              borderRadius: borderRadius,
              titleStyle: titleStyle,
              descriptionStyle: descriptionStyle,
              descriptionWidget: descriptionWidget,
              backgroundColor: backgroundColor,
              isHorizontalButton: isHorizontalButton,
            ),
          );
        }));
  }

  static void showErrorDialog({
    required BuildContext context,
    VoidCallback? onClose,
    String? title,
    required String? error,
    bool barrierDismissible = true,
  }) {
    show(
      context: context,
      barrierDismissible: barrierDismissible,
      buttons: [
        AppButton(
          buttonTitle: context.localization.close,
          onPressed: () {
            Modular.to.pop();
            onClose?.call();
          },
          outline: true,
          backgroundColor: AppColors.system0,
          borderColor: AppColors.system3,
          buttonColor: AppColors.system6,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0D000000), // Hexadecimal color #0000000D
              offset: Offset(0, 14), // Horizontal and vertical offset
              blurRadius: 14, // Blur radius
              spreadRadius: 0, // Spread radius
            ),
          ],
        ),
      ],
      title: title ?? context.localization.notification,
      description: error,
    );
  }
}

class AppDialogWidget extends StatelessWidget {
  final List<Widget> buttons;
  final String? title;
  final String? description;
  final Widget? icon;
  final double borderRadius;
  final TextStyle? titleStyle;
  final TextStyle? descriptionStyle;
  final Widget? descriptionWidget;
  final Color backgroundColor;
  final bool isHorizontalButton;

  const AppDialogWidget({
    required this.buttons,
    this.title,
    this.description,
    this.icon,
    this.borderRadius = 12,
    this.titleStyle,
    this.descriptionStyle,
    this.descriptionWidget,
    this.backgroundColor = Colors.white,
    this.isHorizontalButton = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.neutral,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(borderRadius))),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.neutral,
          borderRadius: BorderRadius.all(Radius.circular(borderRadius)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.baseMargin),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (title != null && title!.isNotEmpty)
                Center(
                  child: Text(
                    title!,
                    style: titleStyle ??
                        CustomStyle.h5.smb.copyWith(color: AppColors.neutral90),
                    textAlign: TextAlign.center,
                  ),
                ),
              if (title != null && title!.isNotEmpty)
                const SizedBox(
                  height: AppDimensions.base,
                ),
              if (icon != null) ...[
                icon!,
                const SizedBox(
                  height: AppDimensions.base,
                ),
              ],
              if (description != null) ...[
                Center(
                  child: Text(
                    description!,
                    textAlign: TextAlign.center,
                    style: descriptionStyle ??
                        CustomStyle.medium.regular
                            .copyWith(color: AppColors.neutral80),
                  ),
                )
              ],
              if (descriptionWidget != null) ...[descriptionWidget!],
              const SizedBox(
                height: 8,
              ),
              isHorizontalButton
                  ? Row(
                      children: [
                        ...(List.generate(
                          buttons.length,
                          (index) => Expanded(
                            child: Container(
                              padding: EdgeInsets.only(
                                  right: index == buttons.length - 1
                                      ? 0
                                      : AppDimensions.base),
                              child: buttons[index],
                            ),
                          ),
                        ))
                      ],
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ...(List.generate(
                            buttons.length,
                            (index) => Container(
                                padding: EdgeInsets.only(
                                    bottom:
                                        index == buttons.length - 1 ? 0 : 8),
                                child: buttons[index])))
                      ],
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
