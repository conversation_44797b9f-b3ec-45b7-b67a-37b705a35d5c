import 'package:flutter/material.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_dimensions.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/extensions/num_extension.dart';

class AppBottomSheet {
  static void show({
    required BuildContext context,
    String? title,
    bool showButtonClose = false,
    bool isScrollControlled = false,
    bool showHeaderBorder = true,
    bool enableDrag = true,
    Color backgroundColor = AppColors.neutral,
    Widget? rightHeaderWidget,
    required Widget contentWidget,
    double? height,
    Widget? customClose,
  }) {
    Widget headerModal() => Container(
          padding: EdgeInsets.symmetric(
            // left: AppDimensions.base,
            horizontal: showButtonClose ? 0 : 12,
          ),
          decoration: showHeaderBorder
              ? const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      width: 1,
                      color: AppColors.neutral50,
                    ),
                  ),
                )
              : null,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Align(
                alignment: rightHeaderWidget == null
                    ? Alignment.center
                    : Alignment.centerLeft,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.base,
                    horizontal: rightHeaderWidget == null
                        ? AppDimensions.base * 3
                        : AppDimensions.base,
                  ),
                  child: Text(
                    title ?? '',
                    textAlign: TextAlign.center,
                    style: CustomStyle.large.smb
                        .copyWith(color: AppColors.neutral90),
                  ),
                ),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (rightHeaderWidget != null) rightHeaderWidget,
                    if (showButtonClose)
                      customClose ??
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(
                              Icons.close,
                              size: 21,
                            ),
                          )
                  ],
                ),
              )
            ],
          ),
        );
    showModalBottomSheet(
      isScrollControlled: isScrollControlled,
      context: context,
      backgroundColor: backgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      enableDrag: enableDrag,
      builder: (context) {
        Widget modalBottomSheetContent = Padding(
          padding: isScrollControlled
              ? EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom)
              : EdgeInsets.zero,
          child: Container(
            decoration: const BoxDecoration(
              color: AppColors.neutral,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                8.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 36,
                      height: 5,
                      decoration: BoxDecoration(
                        color: const Color(0xFF8E8E8E),
                        borderRadius: BorderRadius.circular(2.5),
                      ),
                    ),
                  ],
                ),
                if (title != null && title.isNotEmpty) headerModal(),
                contentWidget,
              ],
            ),
          ),
        );
        return modalBottomSheetContent;
      },
    );
  }
}
