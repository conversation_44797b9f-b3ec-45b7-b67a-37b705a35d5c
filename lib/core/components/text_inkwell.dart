import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:saymee/core/extensions/widget_extension.dart';

class TextInkwell extends StatefulWidget {
  const TextInkwell({
    super.key,
    required this.text,
    required this.color,
    required this.style,
    this.onTap,
    this.icon,
    this.padding = EdgeInsets.zero,
  });

  final String text;
  final TextStyle style;
  final Color color;
  final VoidCallback? onTap;
  final String? icon;
  final EdgeInsets padding;

  @override
  State<TextInkwell> createState() => _TextInkwellState();
}

class _TextInkwellState extends State<TextInkwell> {
  bool _onPressed = false;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      splashFactory: NoSplash.splashFactory,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTapDown: (details) {
        if (mounted) {
          setState(() {
            _onPressed = true;
          });
        }
      },
      onTapUp: (details) {
        Future.delayed(const Duration(milliseconds: 0), () {
          if (mounted) {
            setState(() {
              _onPressed = false;
            });
          }
        });
      },
      onTapCancel: () {
        if (mounted) {
          setState(() {
            _onPressed = false;
          });
        }
      },
      child: Padding(
        padding: widget.padding,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.text,
              style: widget.style.copyWith(
                  color: _onPressed
                      ? widget.color.withOpacity(0.5)
                      : widget.color),
            ),
            if (widget.icon != null)
              SvgPicture.asset(
                widget.icon!,
                colorFilter: ColorFilter.mode(
                  _onPressed ? widget.color.withOpacity(0.5) : widget.color,
                  BlendMode.srcIn,
                ),
              ).paddingOnly(left: 6),
          ],
        ),
      ),
    );
  }
}
