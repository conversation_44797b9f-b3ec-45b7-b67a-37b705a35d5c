import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_dimensions.dart';

enum ButtonSize { small, medium, large }

enum ButtonType { normal, round }

class AppButton extends StatelessWidget {
  final String buttonTitle; // title nút
  final String buttonSubTitle; // subtitle của nút
  final VoidCallback? onPressed; // func khi bấm nút
  final Color backgroundColor; // background nút
  final Color buttonColor; // màu title
  final Color? borderColor; // màu title
  final Color subTitleColor; // màu title
  final bool outline; // hiển thị viền nút, default là false
  final ButtonSize buttonSize; // size của button
  final double titleFontSize; // fontSize title của button
  final FontWeight titleFontWeight; // fontSize title của button
  final FontWeight subTitleFontWeight; // fontSize title của button
  final IconData? prefixIcon; // icon ở phía bên trái
  final IconData? suffixIcon; // icon ở phía bên phải
  final IconData? centerIcon; // icon ở giữa
  final String? prefixIconPath; // icon ở phía bên trái
  final String? suffixIconPath; // icon ở phía bên phải
  final String? centerIconPath; // icon ở giữa
  final double buttonGap; // khoảng cách giữa các component trong button
  final double iconSize; // khoảng cách giữa các component trong button
  final double?
      borderRadius; // borderRadius của nút, nếu truyền thì sẽ lấy giá trị này
  final double? paddingX; // padding left, right của button
  final ButtonType
      buttonType; // type normal là nút tròn, type round borderRadius = baseBorderRadius
  final bool disabled; // disable nút
  final bool isRipple;
  final List<BoxShadow>? boxShadow;
  final TextStyle? titleStyle;
  final bool isDarkMode;
  final Color? centerIconColor;
  final Color? suffixIconColor;

  const AppButton({
    super.key,
    this.buttonTitle = "",
    this.buttonSubTitle = "",
    this.onPressed,
    this.backgroundColor = AppColors.blue,
    this.outline = false,
    this.borderColor,
    Color? buttonColor,
    this.subTitleColor = Colors.white,
    this.buttonSize = ButtonSize.medium,
    this.titleFontSize = AppDimensions.defaultButtonFontSize,
    this.titleFontWeight = FontWeight.w600,
    this.subTitleFontWeight = FontWeight.normal,
    this.prefixIcon,
    this.suffixIcon,
    this.centerIcon,
    this.buttonGap = AppDimensions.buttonGap,
    this.iconSize = AppDimensions.defaultButtonFontSize,
    this.disabled = false,
    this.buttonType = ButtonType.normal,
    this.borderRadius,
    this.prefixIconPath,
    this.suffixIconPath,
    this.centerIconPath,
    this.paddingX,
    this.isRipple = true,
    this.boxShadow,
    this.titleStyle,
    this.isDarkMode = true,
    this.centerIconColor,
    this.suffixIconColor,
  }) : buttonColor =
            buttonColor ?? (outline ? AppColors.neutral80 : AppColors.neutral);

  // trả về height của button dựa trên size
  double _getButtonHeight() {
    switch (buttonSize) {
      case ButtonSize.small:
        {
          return AppDimensions.smallButtonHeight.toDouble();
        }
      case ButtonSize.medium:
        {
          return AppDimensions.mediumButtonHeight.toDouble();
        }
      case ButtonSize.large:
        {
          return AppDimensions.largeButtonHeight.toDouble();
        }
      default:
        {
          return AppDimensions.mediumButtonHeight.toDouble();
        }
    }
  }

  double? _getButtonWidth() {
    if (centerIcon != null || centerIconPath != null) {
      return _getButtonHeight();
    } else {
      return null;
    }
  }

  // tính toán lineHeight của title
  double _getLineHeight(double fontSize) {
    return (1 + AppDimensions.additionalFontLineHeight / fontSize);
  }

  // tính toán font size của sub title
  double _getSubTitleFontSize() {
    return titleFontSize - 2;
  }

  // Lấy màu nền của nút
  Color _getBackgroundColor() {
    // nút outline mặc định là trắng, truyền thêm backgroundColor thì ghi đè lên
    if (outline && backgroundColor != AppColors.primaryMain) {
      return backgroundColor;
    }
    return outline
        ? Colors.transparent
        : (disabled && isRipple ? AppColors.system3 : backgroundColor);
  }

  // Lấy màu text của nút
  Color _getButtonColor() {
    return disabled && isRipple ? AppColors.system4 : buttonColor;
  }

  // Lấy màu viền của nút
  Color _getBorderColor() {
    return disabled && isRipple
        ? AppColors.neutral80
        : (borderColor ?? buttonColor);
  }

  WidgetStateProperty<EdgeInsets?> _getButtonPadding() {
    if (centerIcon != null || centerIconPath != null) {
      return WidgetStateProperty.all(const EdgeInsets.all(0));
    }
    if (paddingX != null) {
      return WidgetStateProperty.all(
          EdgeInsets.only(left: paddingX!, right: paddingX!));
    } else {
      return WidgetStateProperty.all(null);
    }
  }

  RoundedRectangleBorder _getBorderShape() {
    if (borderRadius != null) {
      return RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
            borderRadius ?? AppDimensions.baseBorderRadius),
      );
    } else {
      switch (buttonType) {
        case ButtonType.normal:
          {
            return RoundedRectangleBorder(
              borderRadius:
                  BorderRadius.circular(AppDimensions.baseBorderRadius * 3),
            );
          }
        case ButtonType.round:
          {
            return RoundedRectangleBorder(
              borderRadius:
                  BorderRadius.circular(AppDimensions.baseBorderRadius),
            );
          }

        default:
          {
            return RoundedRectangleBorder(
              borderRadius:
                  BorderRadius.circular(AppDimensions.baseBorderRadius * 3),
            );
          }
      }
    }
  }

  BorderRadiusGeometry? getBorderContainer() {
    if (borderRadius != null) {
      return BorderRadius.circular(
          borderRadius ?? AppDimensions.baseBorderRadius);
    } else {
      switch (buttonType) {
        case ButtonType.normal:
          {
            return BorderRadius.circular(AppDimensions.baseBorderRadius * 3);
          }
        case ButtonType.round:
          {
            return BorderRadius.circular(AppDimensions.baseBorderRadius);
          }

        default:
          {
            return BorderRadius.circular(AppDimensions.baseBorderRadius * 3);
          }
      }
    }
  }

  Widget _buildIcon({
    String? iconPath,
    IconData? icon,
    Color? iconColor,
  }) {
    if (iconPath != null) {
      return SvgPicture.asset(
        iconPath,
        colorFilter: iconColor != null
            ? ColorFilter.mode(iconColor, BlendMode.srcIn)
            : null,
      );
    }
    if (icon != null) {
      return Icon(
        icon,
        color: _getButtonColor(),
        size: iconSize,
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildButtonText() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // main title
        Text(
          buttonTitle,
          style: titleStyle != null
              ? titleStyle?.copyWith(color: _getButtonColor())
              : TextStyle(
                  color: _getButtonColor(),
                  fontSize: titleFontSize,
                  fontWeight: titleFontWeight,
                  height: _getLineHeight(titleFontSize),
                ),
        ),

        // sub title
        if (buttonSubTitle != "")
          Text(
            buttonSubTitle,
            style: TextStyle(
              color: subTitleColor,
              fontSize: _getSubTitleFontSize(),
              fontWeight: subTitleFontWeight,
              height: _getLineHeight(_getSubTitleFontSize()),
            ),
          ),
      ],
    );
  }

  // Nội dung của nút
  Widget _buildButtonContent() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (prefixIcon != null || prefixIconPath != null)
          _buildIcon(icon: prefixIcon, iconPath: prefixIconPath),
        if (prefixIcon != null || prefixIconPath != null)
          SizedBox(width: buttonGap),

        // Button title
        _buildButtonText(),

        if (centerIcon != null || centerIconPath != null)
          _buildIcon(
            icon: centerIcon,
            iconPath: centerIconPath,
            iconColor: centerIconColor,
          ),

        if (suffixIcon != null || suffixIconPath != null)
          SizedBox(width: buttonGap),
        if (suffixIcon != null || suffixIconPath != null)
          _buildIcon(
              icon: suffixIcon,
              iconPath: suffixIconPath,
              iconColor: suffixIconColor),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    VoidCallback? buttonOnPressed = disabled ? null : onPressed;

    final buttonSide = outline
        ? BorderSide(width: 1.0, color: _getBorderColor())
        : BorderSide.none;

    final buttonStyle = ButtonStyle(
      padding: _getButtonPadding(),
      backgroundColor: getButtonStateColor(_getBackgroundColor()),
      side: WidgetStateProperty.all(buttonSide),
      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
        _getBorderShape(),
      ),
    );

    return Container(
      height: _getButtonHeight(),
      width: _getButtonWidth(),
      decoration: BoxDecoration(
        boxShadow: boxShadow,
        borderRadius: getBorderContainer(),
      ),
      child: OutlinedButton(
        onPressed: buttonOnPressed,
        style: buttonStyle,
        child: _buildButtonContent(),
      ),
    );
  }

  WidgetStateProperty<Color> getButtonStateColor(Color backgroundColor) {
    return WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        const Set<WidgetState> interactiveStates = <WidgetState>{
          WidgetState.pressed,
          WidgetState.hovered,
          WidgetState.focused,
        };
        if (states.any(interactiveStates.contains)) {
          return backgroundColor
              .withOpacity(0.4); // Faded color when interactive
        }
        return backgroundColor; // Default color
      },
    );
  }
}
