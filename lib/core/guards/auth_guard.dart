import 'dart:async';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/modules/app/general/app_module_routes.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';

class AuthGuard extends RouteGuard {
  AuthGuard()
      : super(redirectTo: '${AppRoutes.moduleApp}${AppModuleRoutes.main}');

  @override
  FutureOr<bool> canActivate(String path, ParallelRoute route) {
    return !AuthModuleHelper.checkAccessTokenValid();
  }
}
