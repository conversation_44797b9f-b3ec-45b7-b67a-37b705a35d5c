import 'package:flutter/material.dart';

class AppColors {
  AppColors._();

  //primary
  static const Color primaryMain = Color(0xFF15BE6C);
  static const Color primarySurface = Color(0xFFECFBF5);
  static const Color primaryBorder = Color(0xFFD0F4E6);
  static const Color primaryHover = Color(0xFF198E52);
  static const Color primaryPressed = Color(0xFF126D3E);
  static Color primaryFocused = const Color(0xFF13C782).withOpacity(0.2);

  //warning
  static const Color warningMain = Color(0xFFFF6E2B);
  static const Color warningSurface = Color(0xFFFEF3EF);
  static const Color warningBorder = Color(0xFFFBE2D6);
  static const Color warningHover = Color(0xFF8E421F);
  static const Color warningPressed = Color(0xFF5F2C14);

  //red
  static const Color redMain = Color(0xFFF60909);
  static const Color redSurface = Color(0xFFFEEBEB);
  static const Color redBorder = Color(0xFFFDCECE);
  static const Color redHover = Color(0xFF940505);
  static const Color redPressed = Color(0xFF620404);

  //secondary
  static const Color secondaryMain = Color(0xFFFFC043);
  static const Color secondarySurface = Color(0xFFFFFA0F);
  static const Color secondaryBorder = Color(0xFFFFF2D9);
  static const Color secondaryHover = Color(0xFF997328);
  static const Color secondaryPressed = Color(0xFF664D18);

  //teal
  static const Color tealMain = Color(0xFF0DF69E);
  static const Color tealSurface = Color(0xFFEBFFFA);
  static const Color tealBorder = Color(0xFFE8FCF5);
  static const Color tealHover = Color(0xFF0ACC82);
  static const Color tealPressed = Color(0xFF08A368);
  static Color tealFocused = const Color(0xFF0DF69E).withOpacity(0.2);

  //neutral
  static const Color neutral = Colors.white;
  static const Color neutral10 = Color(0XFFF6F6F6);
  static const Color neutral20 = Color(0XFFEFEFEF);
  static const Color neutral30 = Color(0XFFE4E4E4);
  static const Color neutral40 = Color(0XFFC9C8C9);
  static const Color neutral50 = Color(0XFFA9A8AA);
  static const Color neutral60 = Color(0XFF858387);
  static const Color neutral70 = Color(0XFF737175);
  static const Color neutral80 = Color(0XFF565358);
  static const Color neutral90 = Color(0XFF262329);
  static const Color neutral100 = Color(0XFF141316);

  static const Color purple = Color(0xFF9385F2);
  static const Color borderLanguage = Color(0xFF2B3359);
  static const Color black400 = Color(0xFF3A3A3A);
  static const Color blue = Color(0xFF1969FE);
  static const Color darkBlue = Color(0xFF0039A4);
  static const Color yellow = Color(0xFFFFAF02);
  static const Color green = Color(0xFF34C759);
  static const Color error = Color(0xFFED3C3C);

  //system
  static const Color system0 = Colors.white;
  static const Color system1 = Color(0xFFF2F6F9);
  static const Color system2 = Color(0xFFF1F4F9);
  static const Color system3 = Color(0xFFE5EBF0);
  static const Color system4 = Color(0xFF96A9BF);
  static const Color system5 = Color(0xFF5B78A1);
  static const Color system6 = Color(0xFF314873);
  static const Color system8 = Color(0xFF060633);
  static const Color system9 = Color(0xFF101425);
}
