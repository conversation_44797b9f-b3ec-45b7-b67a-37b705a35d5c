import 'package:permission_handler/permission_handler.dart';

class ApiResult {
  ApiResult._();
  static const String success = "success";
  static const String failed = "failed";
}

class WebSocketCommand {
  WebSocketCommand._();

  static const String userLogin = 'user_login';
  static const String userMessage = 'user_message';
  static const String botMessageChunk = 'bot_message_chunk';
  static const String botMessageComplete = 'bot_message_complete';
  static const String alert = 'alert';
}

class AccountType {
  AccountType._();

  static const String email = 'EMAIL';
  static const String apple = 'APPLE';
}

class NotificationStatus {
  NotificationStatus._();

  static const String unknown = 'unknown';
  static String denied = PermissionStatus.denied.name;
  static String granted = PermissionStatus.granted.name;
  static String permanentlyDenied = PermissionStatus.permanentlyDenied.name;
}

class NotificationPermissionStatus {
  NotificationPermissionStatus._();
  static const String granted = 'GRANTED';
  static const String denied = 'DENIED';
}

class MessageType {
  MessageType._();
  static const String text = 'TEXT';
  static const String image = 'IMAGE';
}

enum RoomType {
  single,
  group,
}

class MemberStatus {
  MemberStatus._();

  static const String invited = 'INVITED';
  static const String requested = 'REQUESTED';
  static const String joined = 'JOINED';
  static const String blocked = 'BLOCKED';
}

class MemberRole {
  MemberRole._();

  static const String admin = 'ADMIN';
  static const String member = 'MEMBER';
}

class DeepLinkAction {
  DeepLinkAction._();

  static const String upgrade = 'upgrade';
}

class AFEvent {
  AFEvent._();

  static const String afSubscribe = 'af_subscribe';
  static const String afOpenedFromPushNotification =
      'af_opened_from_push_notification';
  static const String afAdClick = 'af_ad_click';
  static const String afChat = 'af_chat';
}
