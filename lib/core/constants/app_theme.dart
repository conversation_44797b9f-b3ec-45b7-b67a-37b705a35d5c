import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppTheme {
  static ThemeData get theme => ThemeData(
        fontFamily: 'Inter',
        brightness: Brightness.light,
        pageTransitionsTheme: const PageTransitionsTheme(
            builders: <TargetPlatform, PageTransitionsBuilder>{
              TargetPlatform.android: CupertinoPageTransitionsBuilder(),
              TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
              TargetPlatform.linux: FadeUpwardsPageTransitionsBuilder(),
              TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
              TargetPlatform.windows: FadeUpwardsPageTransitionsBuilder(),
            }),
        appBarTheme: const AppBarTheme(
          // color: Colors.black,
          // systemOverlayStyle: SystemUiOverlayStyle.light,
          // iconTheme: IconThemeData(color: Colors.black),
          backgroundColor: Colors.white, // Background color of AppBar
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent, // Transparent status bar
            statusBarIconBrightness: Brightness.dark, // Light status bar icons
            statusBarBrightness: Brightness.light,
            systemNavigationBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.transparent,
          ),

          // systemOverlayStyle: SystemUiOverlayStyle(
          //   statusBarColor: Colors.black, // <-- SEE HERE
          //   statusBarIconBrightness:
          //       Brightness.light, //<-- For Android SEE HERE (dark icons)
          //   statusBarBrightness:
          //       Brightness.light, //<-- For iOS SEE HERE (dark icons)
          // ),
        ),
        bottomSheetTheme: const BottomSheetThemeData(
          surfaceTintColor: Colors.black,
        ),
        dialogTheme: const DialogThemeData(surfaceTintColor: Colors.white),
      );
}
