class AppDimensions {
  AppDimensions._();
  static const double appBarFontSize = 16;
  static const double baseFontSize = 14;
  static const double appBarHeight = 58;

  static const double base = 12;
  static const double baseMargin = 16;
  static const double baseBorderRadius = 8;
  static const double textFieldFontSize = 16;

  static const double buttonGap = base / 4;

  static const defaultButtonFontSize = 14.0;
  static const defaultButtonCardCircleFontSize = 11.0;

  // button
  static const double smallButtonHeight = 36;
  static const double mediumButtonHeight = 44;
  static const double largeButtonHeight = 52;

  static const additionalFontLineHeight = 7;
}
