import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:saymee/core/constants/app_configs.dart';
import 'package:saymee/core/helpers/general_helper.dart';

class AppEnvironment {
  static String get envFileName =>
      AppConfigs.flavorDev == GeneralHelper.appFlavor
          ? '.env.development'
          : '.env.production';

  static String get apiUrl => dotenv.env['API_URL'] ?? '';
  static String get imageUrl => dotenv.env['IMAGE_URL'] ?? '';
  static String get wsUrl => dotenv.env['WS_URL'] ?? '';
  static String get configUrl => dotenv.env['CONFIG_URL'] ?? '';
  static String get iosDevKey => dotenv.env['IOS_DEV_KEY'] ?? '';
  static String get iosAppId => dotenv.env['IOS_APP_ID'] ?? '';
  static String get androidDevKey => dotenv.env['ANDROID_DEV_KEY'] ?? '';
}
