import 'package:saymee/core/constants/app_keys.dart';
import 'package:saymee/core/extensions/localized_extension.dart';

String? validateEmail(String? value, {String? password}) {
  final context = AppKeys.navigatorKey.currentContext!;
  if (value == null || (value.trim()).isEmpty) {
    return context.localization.emailCannotEmpty;
  }

  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
    return context.localization.invalidEmail;
  }
  return null;
}

String? validateFullname(String? value, {String? password}) {
  final context = AppKeys.navigatorKey.currentContext!;
  if (value == null || value.trim().isEmpty) {
    return context.localization.fullNameCannotEmpty;
  }
  return null;
}

String? validateUsername(String? value, {String? password}) {
  final context = AppKeys.navigatorKey.currentContext!;
  final regex = RegExp(r"^[a-zA-Z][a-zA-Z0-9_.]{2,15}(?<![_.])$");
  if (value == null || value.trim().isEmpty) {
    return context.localization.usernameCannotEmpty;
  } else if (!regex.hasMatch(value.trim())) {
    return context.localization.usernameInvalid;
  }
  return null;
}

String? validatePassword(String? value, {String? password}) {
  final context = AppKeys.navigatorKey.currentContext!;
  if (value == null || value.trim().isEmpty) {
    return context.localization.passwordCannotEmpty;
  }
  if (value.trim().length < 6) {
    return context.localization.passwordShorter6Character;
  }

  if (password != null && password != value.trim()) {
    return context.localization.passwordError;
  }
  return null;
}

String? validatePhoneNumber(String? value, {String? password}) {
  final context = AppKeys.navigatorKey.currentContext!;
  if (value == null || value.trim().isEmpty) {
    return context.localization.phoneNumberCannotEmpty;
  } else if (!RegExp(r'^(0|\+84)\d{9,10}$').hasMatch(value)) {
    return context.localization.invalidPhoneNumber;
  } else {
    return null;
  }
}

String? validateOTP(String? value, {String? password}) {
  final context = AppKeys.navigatorKey.currentContext!;
  if (value == null || value.trim().isEmpty) {
    return context.localization.otpCannotEmpty;
  } else {
    return null;
  }
}

String? validateGroupName(String? value, {String? password}) {
  final context = AppKeys.navigatorKey.currentContext!;
  if (value == null || value.trim().isEmpty) {
    return context.localization.groupNameCannotEmpty;
  } else {
    return null;
  }
}

enum AppTextFieldValidatorType {
  email(validatorFunction: validateEmail),
  fullname(validatorFunction: validateFullname),
  username(validatorFunction: validateUsername),
  password(validatorFunction: validatePassword),
  otp(validatorFunction: validateOTP),
  groupName(validatorFunction: validateGroupName),
  phoneNumber(validatorFunction: validatePhoneNumber);

  final String? Function(String? value, {String? password}) validatorFunction;

  const AppTextFieldValidatorType({required this.validatorFunction});
}
