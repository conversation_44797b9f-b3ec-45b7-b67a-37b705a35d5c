import 'package:flutter/material.dart';

enum CustomStyle {
  h1(fontSize: 32, height: 1.22),
  h2(fontSize: 28, height: 1.25),
  h3(fontSize: 24, height: 1.29),
  h4(fontSize: 22, height: 1.27),
  h5(fontSize: 18, height: 1.28),
  large(fontSize: 16, height: 1.5),
  medium(fontSize: 14, height: 1.5),
  small(fontSize: 12, height: 1.4);

  final double fontSize;
  final double height;

  const CustomStyle({required this.fontSize, required this.height});
}

extension CustomStyleExtension on CustomStyle {
  TextStyle get bold => TextStyle(
        fontSize: fontSize,
        //height: height,
        fontWeight: FontWeight.w700,
      );

  TextStyle get smb => TextStyle(
        fontSize: fontSize,
        //height: height,
        fontWeight: FontWeight.w600,
      );

  TextStyle get medium => TextStyle(
        fontSize: fontSize,
        //height: height,
        fontWeight: FontWeight.w500,
      );

  TextStyle get regular => TextStyle(
        fontSize: fontSize,
        //height: height,
        fontWeight: FontWeight.w400,
      );
}
