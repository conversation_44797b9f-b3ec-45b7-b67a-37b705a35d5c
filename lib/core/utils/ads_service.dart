import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:ironsource_mediation/ironsource_mediation.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/utils/analytic_service.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/presentation/blocs/app_banner/app_banner_cubit.dart';

class AdsService {
  AdsService._privateConstructor();

  static final AdsService instance = AdsService._privateConstructor();

  factory AdsService() {
    return instance;
  }

  bool get isInitialized => _isInitialized;

  bool get initializing => _initializing;

  bool _isInitialized = false;

  bool _initializing = false;

  bool _interstitialAdLoaded = false;

  bool get interstitialAdLoaded => _interstitialAdLoaded;

  LevelPlayInterstitialAd? get interstitialAd => _interstitialAd;

  LevelPlayInterstitialAd? _interstitialAd;

  LevelPlayBannerAdView? _bannerAdView;

  LevelPlayBannerAdView? get bannerAdView => _bannerAdView;

  bool _isFirstInit = false;
  bool get isFirstInit => _isFirstInit;

  String? _adsInterstitialIds;
  String? _adsUnitId;

  Future<void> init({
    required String appKey,
    String? adsInterstitialIds,
    String? adsUnitId,
  }) async {
    Utils.debugLogSuccess(appKey, tagName: 'AdsService');
    try {
      _initializing = true;
      _isFirstInit = true;
      if (adsInterstitialIds != null) {
        _adsInterstitialIds = adsInterstitialIds;
      }

      if (adsUnitId != null) {
        _adsUnitId = adsUnitId;
      }

      IronSource.setFlutterVersion('3.24.4');
      if (kDebugMode) {
        await IronSource.setAdaptersDebug(true);
        // this function doesn't have to be awaited
        IronSource.validateIntegration();
      }

      await IronSource.shouldTrackNetworkState(true);

      await IronSource.setConsent(true);
      await IronSource.setMetaData({
        'do_not_sell': ['false'],
        'is_child_directed': ['false'],
        'is_test_suite': ['enable']
      });

      if (Platform.isIOS) {
        await _checkATT();
      }

      // Finally, initialize
      // LevelPlay Init
      List<AdFormat> legacyAdFormats = [
        AdFormat.BANNER,
        AdFormat.INTERSTITIAL,
      ];
      final initRequest = LevelPlayInitRequest(
        appKey: appKey,
        legacyAdFormats: legacyAdFormats,
        userId: globalUuid,
      );

      await LevelPlay.init(
        initRequest: initRequest,
        initListener: _LevelPlayInitListener(
          onInitSuccessCallback: () {
            _isInitialized = true;
            if (adsInterstitialIds != null) {
              _interstitialAd =
                  LevelPlayInterstitialAd(adUnitId: adsInterstitialIds);
              _interstitialAd?.setListener(_InterstitialListener(
                onLoaded: () {
                  Utils.debugLogSuccess("onLoaded", tagName: "AdsService");
                  _interstitialAdLoaded = true;
                },
              ));

              _interstitialAd?.loadAd();

              Utils.debugLog(adsInterstitialIds);
            }
            _createBannerAdView(_adsUnitId);
          },
          onInitFailedCallback: () {
            _isInitialized = false;
          },
        ),
      );
    } on PlatformException catch (e) {
      Utils.debugLogError(e);
      _isInitialized = false;
    }

    _initializing = false;
  }

  void showInterstitialAd() async {
    Utils.debugLog("${await _interstitialAd?.isAdReady()}");
    if (_interstitialAd != null && await _interstitialAd!.isAdReady()) {
      _interstitialAd!.showAd();
      _interstitialAdLoaded = false;
      if (_adsInterstitialIds != null) {
        _interstitialAd =
            LevelPlayInterstitialAd(adUnitId: _adsInterstitialIds!);
        _interstitialAd?.setListener(_InterstitialListener(
          onLoaded: () {
            Utils.debugLogSuccess("onLoaded", tagName: "AdsService");
            _interstitialAdLoaded = true;
          },
        ));

        _interstitialAd?.loadAd();
      }
    }
  }

  void _createBannerAdView(String? adsIds) async {
    if (adsIds != null && _bannerAdView == null) {
      final bannerKey = GlobalKey<LevelPlayBannerAdViewState>();
      _bannerAdView = LevelPlayBannerAdView(
        key: bannerKey,
        adUnitId: adsIds,
        adSize: LevelPlayAdSize.BANNER,
        listener: _BannerListener(
          onDisplayed: () {
            Utils.debugLogSuccess("onDisplayed", tagName: "AppBannerCubit");
            Modular.get<AppBannerCubit>().updateValue(true);
          },
          onClicked: () {
            AnalyticService.instance.logEvent(event: AFEvent.afAdClick);
          },
        ),
        placementName: '',
        onPlatformViewCreated: () {
          _bannerAdView?.loadAd();
        },
      );
    }
  }

  void destroyAdAndCreateNew() {
    if (_bannerAdView == null) return;

    _bannerAdView!.destroy();
    _bannerAdView = null;
    Modular.get<AppBannerCubit>().updateValue(false);
    _createBannerAdView(_adsUnitId);
  }
}

Future<void> _checkATT() async {
  final currentStatus =
      await ATTrackingManager.getTrackingAuthorizationStatus();

  if (currentStatus == ATTStatus.NotDetermined) {
    await ATTrackingManager.requestTrackingAuthorization();
  }
}

class _LevelPlayInitListener implements LevelPlayInitListener {
  final VoidCallback onInitSuccessCallback;
  final VoidCallback onInitFailedCallback;

  _LevelPlayInitListener({
    required this.onInitSuccessCallback,
    required this.onInitFailedCallback,
  });

  @override
  void onInitSuccess(LevelPlayConfiguration configuration) {
    Utils.debugLog('LevelPlay initialized successfully: $configuration');
    onInitSuccessCallback.call();
  }

  @override
  void onInitFailed(LevelPlayInitError error) {
    Utils.debugLog('LevelPlay initialization failed: $error');
    onInitFailedCallback.call();
  }
}

class _BannerListener implements LevelPlayBannerAdViewListener {
  final VoidCallback onDisplayed;
  final VoidCallback onClicked;

  _BannerListener({
    required this.onDisplayed,
    required this.onClicked,
  });

  @override
  void onAdClicked(LevelPlayAdInfo adInfo) {
    onClicked.call();
  }

  @override
  void onAdCollapsed(LevelPlayAdInfo adInfo) {}

  @override
  void onAdDisplayFailed(LevelPlayAdInfo adInfo, LevelPlayAdError error) {}

  @override
  void onAdDisplayed(LevelPlayAdInfo adInfo) {
    onDisplayed.call();
  }

  @override
  void onAdExpanded(LevelPlayAdInfo adInfo) {}

  @override
  void onAdLeftApplication(LevelPlayAdInfo adInfo) {}

  @override
  void onAdLoadFailed(LevelPlayAdError error) {}

  @override
  void onAdLoaded(LevelPlayAdInfo adInfo) {}
}

class _InterstitialListener implements LevelPlayInterstitialAdListener {
  final VoidCallback onLoaded;

  _InterstitialListener({required this.onLoaded});
  @override
  void onAdClicked(LevelPlayAdInfo adInfo) {}

  @override
  void onAdClosed(LevelPlayAdInfo adInfo) {}

  @override
  void onAdDisplayFailed(LevelPlayAdError error, LevelPlayAdInfo adInfo) {}

  @override
  void onAdDisplayed(LevelPlayAdInfo adInfo) {}

  @override
  void onAdInfoChanged(LevelPlayAdInfo adInfo) {}

  @override
  void onAdLoadFailed(LevelPlayAdError error) {}

  @override
  void onAdLoaded(LevelPlayAdInfo adInfo) {
    onLoaded.call();
  }
}
