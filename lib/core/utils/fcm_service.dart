import 'dart:convert';
import 'dart:developer';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/core/utils/local_notification_service.dart';
import 'package:saymee/modules/app/presentation/blocs/app_notification/app_notification_cubit.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/chat/general/chat_module_routes.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';
import 'package:permission_handler/permission_handler.dart';

class FcmService {
  static void initialize() async {
    checkPermission();

    try {
      final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;

      firebaseMessaging.getToken().then((token) {
        log('[FcmService] Token: $token');
        if (token != null) {
          Clipboard.setData(ClipboardData(text: token));
          globalDeviceToken = token;
          final userId = UserModuleHelper.getUserId();
          final cubit = Modular.get<AppNotificationCubit>();
          UserModuleHelper.updateDevice(
            userId: userId,
            uuid: globalUuid ?? "",
            platformToken: globalDeviceToken!,
            granted: cubit.state == NotificationStatus.granted ? 1 : 0,
          );
        }
      });

      firebaseMessaging.onTokenRefresh.listen((token) {
        log('[FcmService] Token: $token');
        Clipboard.setData(ClipboardData(text: token));
        globalDeviceToken = token;
        final userId = UserModuleHelper.getUserId();
        final cubit = Modular.get<AppNotificationCubit>();
        UserModuleHelper.updateDevice(
          userId: userId,
          uuid: globalUuid ?? "",
          platformToken: globalDeviceToken!,
          granted: cubit.state == NotificationStatus.granted ? 1 : 0,
        );
      });

      FirebaseMessaging.onMessage.listen((message) {
        log('[FcmService] onMessage: ${message.toMap()}');
        final notification = message.notification;
        if (notification?.title != null && notification?.body != null) {
          final data = message.data;
          final roomId = data['roomId'];
          if (data['messageId'] != null && roomId != null) {
            //handle show message notification
            if (Modular.to.path !=
                '${AppRoutes.moduleChat}${ChatModuleRoutes.chatbot}/$roomId') {
              LocalNotificationService.showLocalNotification(
                title: notification!.title!,
                body: notification.body!,
                payload: jsonEncode(data),
              );
            }
          } else {
            LocalNotificationService.showLocalNotification(
              title: notification!.title!,
              body: notification.body!,
              payload: jsonEncode(data),
            );
          }
        }
      });

      FirebaseMessaging.onMessageOpenedApp.listen((message) {
        log('[FcmService] onMessageOpenedApp: ${message.toMap()}');
        _handleOpenNotification(message);
      });

      firebaseMessaging.getInitialMessage().then((message) {
        if (message != null) {
          log('[FcmService] getInitialMessage: ${message.toMap()}');
          _handleOpenNotification(message);
        }
      });
    } catch (e) {
      log('[FcmService] Error: $e');
    }
  }

  static void _handleOpenNotification(RemoteMessage message) {
    if (message.data['messageId'] != null &&
        message.data['roomId'] != null &&
        AuthModuleHelper.checkAccessTokenValid()) {
      final roomId = int.tryParse(message.data['roomId'] as String) ?? -1;

      ChatModuleHelper.navigateToRoom(roomId);
    }
  }

  static void checkPermission() async {
    final status = await Permission.notification.status;
    final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();
    //final userId = sharedPreferenceHelper.getUserId();
    log('[FcmService] Status: ${status.name}');
    if (status.isGranted) {
      sharedPreferenceHelper.setNotificationPermission(
          status: NotificationPermissionStatus.granted);
    } else {
      final notificationStatus =
          sharedPreferenceHelper.getNotificationPermission();

      if ((notificationStatus == null ||
              notificationStatus == NotificationPermissionStatus.granted) &&
          status.isDenied) {
        if (AuthModuleHelper.checkAccessTokenValid()) {
          final request = await Permission.notification.request();

          log('[FcmService] Request: ${request.name}');
          if (request.isGranted) {
            sharedPreferenceHelper.setNotificationPermission(
                status: NotificationPermissionStatus.granted);
          } else {
            sharedPreferenceHelper.setNotificationPermission(
                status: NotificationPermissionStatus.denied);
          }
        }
      } else {
        sharedPreferenceHelper.setNotificationPermission(
            status: NotificationPermissionStatus.denied);
      }
    }
  }

  static void requestPermission() async {
    final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();
    final status = sharedPreferenceHelper.getNotificationPermission();
    if (status != NotificationPermissionStatus.denied) {
      final result = await Permission.notification.request();
      if (result.isGranted) {
        sharedPreferenceHelper.setNotificationPermission(
            status: NotificationPermissionStatus.granted);
      } else {
        sharedPreferenceHelper.setNotificationPermission(
            status: NotificationPermissionStatus.denied);
      }
    }
  }
}
