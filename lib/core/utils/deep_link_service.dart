import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/auth/general/auth_module_routes.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/subscription/general/subscription_module_helper.dart';

class DeepLinkService {
  DeepLinkService._privateConstructor();

  static final DeepLinkService instance = DeepLinkService._privateConstructor();

  factory DeepLinkService() {
    return instance;
  }

  AppLinks? _appLinks;

  StreamSubscription<Uri>? _linkSubscription;

  final _linkController = StreamController<Uri>.broadcast(sync: true);

  Stream<Uri> get linkStream => _linkController.stream;

  void initService() {
    _appLinks = AppLinks();
    initDeepLinks();
  }

  Future<void> initDeepLinks() async {
    final initialLink = await _appLinks?.getInitialLink();

    if (initialLink != null) {
      Utils.debugLogSuccess('AppLink: ${initialLink.toString()}',
          tagName: 'DeepLinkService');
      if (initialLink.queryParameters.containsKey('action')) {
        final action = initialLink.queryParameters['action'];
        if (action == DeepLinkAction.upgrade) {
          SubscriptionModuleHelper.goToUpgradeSubscriptionPage();
        }
      } else if (initialLink
          .toString()
          .startsWith('https://jesterbot.net/universal/room/')) {
        String secret = initialLink.pathSegments.isNotEmpty
            ? initialLink.pathSegments.last
            : "";

        if (secret.isNotEmpty) {
          ChatModuleHelper.joinWithSecret(secret);
        }
      }
    }

    _linkSubscription = _appLinks?.uriLinkStream.listen(
      (event) {
        Utils.debugLogSuccess('Event: ${event.toString()}',
            tagName: 'DeepLinkService');

        _linkController.add(event);

        if (event.toString().startsWith('jester') &&
            event.queryParameters.containsKey('action')) {
          final action = event.queryParameters['action'];
          if (action == DeepLinkAction.upgrade) {
            SubscriptionModuleHelper.goToUpgradeSubscriptionPage();
          }
        } else if (event
            .toString()
            .startsWith('https://jesterbot.net/universal/room/')) {
          String secret =
              event.pathSegments.isNotEmpty ? event.pathSegments.last : "";

          if (secret.isNotEmpty) {
            ChatModuleHelper.joinWithSecret(secret);
          }
        } else if (event
            .toString()
            .startsWith('https://jesterbot.net/universal/user/activate')) {
          final queryParameters = event.queryParameters;
          if (queryParameters.containsKey('email') &&
              queryParameters.containsKey('otp')) {
            final email = queryParameters['email'] ?? '';
            final otp = queryParameters['otp'];

            if (Modular.to.path !=
                '${AppRoutes.moduleAuth}${AuthModuleRoutes.activateAccount}') {
              final isLoggedIn = AuthModuleHelper.checkAccessTokenValid();

              AuthModuleHelper.goToActivateAccountPage(
                email: email,
                otp: otp,
                isLoggedIn: isLoggedIn,
              );
            }
          }
        }
      },
      onError: (error) {
        Utils.debugLogError('Error: ${error.toString()}',
            tagName: 'DeepLinkService');
      },
      onDone: () {
        _linkSubscription?.cancel();
      },
    );
  }
}
