import 'dart:convert';
import 'dart:io';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';

class LocalNotificationService {
  LocalNotificationService._();

  static final FlutterLocalNotificationsPlugin _localNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static void initLocalNotificationSetting() async {
    final didNotificationLaunchApp =
        await _localNotificationsPlugin.getNotificationAppLaunchDetails();

    if (didNotificationLaunchApp?.notificationResponse != null) {
      Utils.debugLogSuccess(
          '[LocalNotificationService] didNotificationLaunchApp: ${didNotificationLaunchApp?.notificationResponse?.payload}');
      handleNotificationResponse(
          didNotificationLaunchApp!.notificationResponse!);
    }

    const DarwinInitializationSettings darwinInitializationSettings =
        DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const AndroidInitializationSettings androidInitializationSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(
      iOS: darwinInitializationSettings,
      android: androidInitializationSettings,
    );

    _localNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (details) {
        Utils.debugLogSuccess(
            '[LocalNotificationService] onDidReceiveNotificationResponse: ${details.payload}');
        handleNotificationResponse(details);
      },
      // onDidReceiveBackgroundNotificationResponse: (details) {
      //   Utils.debugLogSuccess(
      //       '[LocalNotificationService] onDidReceiveBackgroundNotificationResponse: $details');
      // },
    );
  }

  static void handleNotificationResponse(NotificationResponse details) {
    if (details.payload != null && Platform.isAndroid) {
      try {
        final data = jsonDecode(details.payload!) as Map<String, dynamic>;
        if (data['messageId'] != null &&
            data['roomId'] != null &&
            AuthModuleHelper.checkAccessTokenValid()) {
          final roomId = int.tryParse(data['roomId'] as String) ?? -1;

          ChatModuleHelper.navigateToRoom(roomId);
        }
      } catch (e) {
        Utils.debugLogError(e);
      }
    }
  }

  static void showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) {
    const DarwinNotificationDetails darwinNotificationDetails =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      'high_priority_channel',
      'High Priority Notifications',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    NotificationDetails platformDetails = const NotificationDetails(
      iOS: darwinNotificationDetails,
      android: androidNotificationDetails,
    );

    _localNotificationsPlugin.show(
      0,
      title,
      body,
      platformDetails,
      payload: payload,
    );
  }
}
