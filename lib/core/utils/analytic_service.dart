import 'dart:io';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_environment.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/user/presentation/blocs/user_info_bloc.dart';

class AnalyticService {
  AnalyticService._privateConstructor();

  static final AnalyticService instance = AnalyticService._privateConstructor();

  factory AnalyticService() {
    return instance;
  }

  AppsflyerSdk? _appsflyerSdk;

  Future<void> initService() async {
    Utils.debugLogWarning(
        'afDevKey: ${AppEnvironment.iosDevKey}, appId: ${AppEnvironment.iosAppId}',
        tagName: 'AnalyticService');
    final AppsFlyerOptions options = AppsFlyerOptions(
      afDevKey: Platform.isIOS
          ? AppEnvironment.iosDevKey
          : AppEnvironment.androidDevKey,
      appId: Platform.isIOS ? AppEnvironment.iosAppId : '',
      showDebug: true,
      timeToWaitForATTUserAuthorization: 15,
    );

    _appsflyerSdk = AppsflyerSdk(options);

    if (globalUuid != null) {
      _appsflyerSdk?.setCustomerUserId(globalUuid!);
    }

    await _appsflyerSdk?.initSdk();
  }

  void logEvent({required String event, Map<String, dynamic>? data}) {
    try {
      final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();
      final userInfoBloc = Modular.get<UserInfoBloc>();
      final userId =
          userInfoBloc.state.user?.userId ?? sharedPreferenceHelper.getUserId();

      Map<String, dynamic> params = {
        'user_id': userId,
        'uuid': globalUuid,
      };

      if (data != null) {
        params.addAll(data);
      }

      Utils.debugLogSuccess(params, tagName: 'AnalyticService');

      _appsflyerSdk?.logEvent(event, params);
    } catch (e) {
      Utils.debugLogError(e, tagName: 'AnalyticService');
    }
  }
}
