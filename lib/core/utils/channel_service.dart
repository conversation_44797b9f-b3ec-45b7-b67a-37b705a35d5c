import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/components/app_dialog.dart';
import 'package:saymee/core/components/buttons/app_button.dart';
import 'package:saymee/core/constants/app_colors.dart';
import 'package:saymee/core/constants/app_keys.dart';
import 'package:saymee/core/constants/custom_style.dart';
import 'package:saymee/core/utils/utils.dart';

class ChannelService {
  ChannelService._();
  static const MethodChannel _methodChannel =
      MethodChannel('com.mdc.jesterbot.method');

  static Future<dynamic> loadProducts(List<String> codes) async {
    try {
      final result = await _methodChannel.invokeMethod("loadProducts", codes);
      Utils.debugLogSuccess(result, tagName: 'MethodService');
      return result;
    } on PlatformException catch (e) {
      Utils.debugLogError(e, tagName: 'MethodService');
      return null;
    }
  }

  static void currentReceipt() async {
    try {
      final result = await _methodChannel.invokeMethod("currentReceipt");
      Utils.debugLogSuccess(result, tagName: 'MethodService');
    } on PlatformException catch (e) {
      Utils.debugLogError(e, tagName: 'MethodService');
    }
  }

  static Future<dynamic> subscribe(String code) async {
    try {
      final result = await _methodChannel.invokeMethod("subscribe", code);
      Utils.debugLogSuccess(result, tagName: 'MethodService');
      return result;
    } catch (e) {
      Utils.debugLogError(e, tagName: 'MethodService');
      return null;
    }
  }

  static void restore() async {
    try {
      await _methodChannel.invokeMethod("restore");
    } on PlatformException catch (e) {
      Utils.debugLogError(e, tagName: 'MethodService');
    }
  }

  static void showAlert({
    required String title,
    required String message,
    required String buttonTitle,
    required String url,
  }) async {
    if (Platform.isIOS) {
      try {
        Utils.debugLogWarning({
          'title': title,
          'message': message,
          'buttonTitle': buttonTitle,
          'url': url,
        });
        await _methodChannel.invokeMethod("showAlert", {
          'title': title,
          'message': message,
          'buttonTitle': buttonTitle,
          'url': url,
        });
      } on PlatformException catch (e) {
        Utils.debugLogError(e, tagName: 'MethodService');
      }
    } else {
      AppDialog.show(
        context: AppKeys.navigatorKey.currentContext!,
        title: title,
        description: message,
        buttons: [
          AppButton(
            backgroundColor: AppColors.system0,
            borderColor: AppColors.system3,
            outline: true,
            buttonColor: AppColors.system6,
            titleStyle: CustomStyle.large.smb,
            boxShadow: const [
              BoxShadow(
                color: Color(0x0D000000), // Hexadecimal color #0000000D
                offset: Offset(0, 14), // Horizontal and vertical offset
                blurRadius: 14, // Blur radius
                spreadRadius: 0, // Spread radius
              ),
            ],
            buttonTitle: "Dismiss",
            onPressed: () {
              Modular.to.pop();
            },
          ),
          AppButton(
            backgroundColor: AppColors.primaryMain,
            buttonColor: AppColors.system0,
            titleStyle: CustomStyle.large.smb,
            buttonTitle: buttonTitle,
            onPressed: () {
              Modular.to.pop();
              Utils.launchURL(urlString: url);
            },
          ),
        ],
      );
    }
  }
}
