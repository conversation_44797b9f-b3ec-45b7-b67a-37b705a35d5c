import 'dart:convert';
import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_data.dart';
import 'package:saymee/core/constants/app_routes.dart';
import 'package:saymee/core/utils/analytic_service.dart';
import 'package:saymee/core/utils/globals.dart';
import 'package:saymee/core/utils/local_notification_service.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/app/presentation/blocs/app_notification/app_notification_cubit.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';
import 'package:saymee/modules/chat/general/chat_module_helper.dart';
import 'package:saymee/modules/chat/general/chat_module_routes.dart';
import 'package:saymee/modules/user/general/user_module_helper.dart';

class PushIOSNotificationService {
  static const EventChannel _eventChannel =
      EventChannel('com.mdc.jesterbot.event');

  static const MethodChannel _methodChannel =
      MethodChannel('com.mdc.jesterbot.method');

  static void listen() {
    _eventChannel.receiveBroadcastStream().listen(
      (event) {
        log('[PushNotificationService] Event: $event');
        try {
          final eventData = jsonDecode(event) as Map<String, dynamic>;
          switch (eventData['event']) {
            case 'notification_click':
              //handle click
              _handleNotificationClick(eventData['userInfo']);
              break;
            case 'notification_token':
              String? token = eventData['deviceToken'];
              if (token != null) {
                globalDeviceToken = token;
                final userId = UserModuleHelper.getUserId();
                final cubit = Modular.get<AppNotificationCubit>();
                UserModuleHelper.updateDevice(
                  userId: userId,
                  uuid: globalUuid ?? "",
                  platformToken: globalDeviceToken!,
                  granted: cubit.state == NotificationStatus.granted ? 1 : 0,
                );
              }
              break;
            case 'notification_data':
              final notificationData =
                  eventData['notificationData'] as Map<String, dynamic>;
              final userInfo =
                  notificationData['userInfo'] as Map<String, dynamic>;

              String payloadData = jsonEncode(userInfo);
              if (userInfo['roomId'] != null && userInfo['messageId'] != null) {
                final roomId = userInfo['roomId'];
                if (Modular.to.path !=
                    '${AppRoutes.moduleChat}${ChatModuleRoutes.chatbot}/$roomId') {
                  LocalNotificationService.showLocalNotification(
                    title: notificationData['title'],
                    body: notificationData['body'],
                    payload: payloadData,
                  );
                }
              } else {
                LocalNotificationService.showLocalNotification(
                  title: notificationData['title'],
                  body: notificationData['body'],
                  payload: payloadData,
                );
              }

              break;
            default:
          }
        } catch (e) {
          log('[PushNotificationService] Error: $e');
        }
      },
    );
  }

  static void _handleNotificationClick(Map<String, dynamic> data) {
    AnalyticService.instance
        .logEvent(event: AFEvent.afOpenedFromPushNotification);
    try {
      if (data['roomId'] != null &&
          data['messageId'] != null &&
          AuthModuleHelper.checkAccessTokenValid()) {
        final roomId = (data['roomId'] as int?) ?? -1;

        ChatModuleHelper.navigateToRoom(roomId);
      } else {
        final payload = data['payload'];
        Utils.debugLogSuccess(payload);
        if (payload is String) {
          final payloadData = jsonDecode(payload) as Map<String, dynamic>;
          if (payloadData['roomId'] != null &&
              payloadData['messageId'] != null &&
              AuthModuleHelper.checkAccessTokenValid()) {
            final roomId = (payloadData['roomId'] as int?) ?? -1;

            ChatModuleHelper.navigateToRoom(roomId);
          }
        }
      }
    } catch (e) {
      Utils.debugLogError(e);
    }
  }

  static void retrieveDeviceToken() async {
    try {
      final deviceToken =
          await _methodChannel.invokeMethod<String>("retrieveDeviceToken");

      if (deviceToken != null) {
        log('[PushNotificationService] DeviceToken: $deviceToken');
        globalDeviceToken = deviceToken;
        final userId = UserModuleHelper.getUserId();
        final cubit = Modular.get<AppNotificationCubit>();
        UserModuleHelper.updateDevice(
          userId: userId,
          uuid: globalUuid ?? "",
          platformToken: globalDeviceToken!,
          granted: cubit.state == NotificationStatus.granted ? 1 : 0,
        );
      }
    } on PlatformException catch (e) {
      log(e.toString());
    }
  }

  static void retrieveTerminatedNotification() async {
    try {
      await _methodChannel
          .invokeMethod<Map<String, dynamic>>("retrieveTerminatedNotification");
      // ignore: unused_local_variable
    } on PlatformException catch (e) {
      log(e.toString());
    }
  }

  static void requestNotificationPermission() async {
    try {
      await _methodChannel.invokeMethod("requestNotificationPermission");
    } on PlatformException catch (e) {
      log(e.toString());
    }
  }
}
