import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/helpers/general_helper.dart';
import 'package:saymee/core/helpers/shared_preference_helper.dart';
import 'package:saymee/core/helpers/web_socket_helper.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:saymee/modules/auth/general/auth_module_helper.dart';

class DioInterceptor extends Interceptor {
  final _sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();
  final Dio dio;

  DioInterceptor({required this.dio});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    String? accessToken = _sharedPreferenceHelper.getAccessToken();

    if (accessToken != null && accessToken.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $accessToken';
    }

    options.headers['Device-Id'] = GeneralHelper.deviceId;
    options.headers['App-Version'] = GeneralHelper.appVersion;
    options.headers['OS-Info'] = GeneralHelper.osInfo;
    options.headers['Device-Info'] = GeneralHelper.deviceInfo;
    options.headers['OS-Version'] = GeneralHelper.osVersion;

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final statusCode = err.response?.statusCode;

    Utils.debugLog(err.requestOptions.path);

    if (statusCode == HttpStatus.unauthorized &&
        err.requestOptions.path != '/api/v3/user/logout') {
      WebSocketHelper().disconnect();
      AuthModuleHelper.signOut();
      AuthModuleHelper.navigateToSignInPage();
    }
    return handler.reject(err);
  }
}
