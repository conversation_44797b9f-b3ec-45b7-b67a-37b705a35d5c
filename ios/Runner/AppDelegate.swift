import Flutter
import UIKit
import UserNotifications
import app_links

@main
@objc class AppDelegate: FlutterAppDelegate {
    var subscription = Subscription()
    let eventChannelName : String = "com.mdc.jesterbot.event"
    let methodChannelName : String = "com.mdc.jesterbot.method"
    var deviceToken : String = ""
    var notificationData : [String: AnyObject]?
    private var eventSink: FlutterEventSink?
    private var flutterViewController: FlutterViewController?
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        UNUserNotificationCenter.current().delegate = self
        let controller = window?.rootViewController as! FlutterViewController
        
        flutterViewController = controller
        
        let eventChannel = FlutterEventChannel(name: eventChannelName, binaryMessenger: controller.binaryMessenger)
        let methodChannel = FlutterMethodChannel(name: methodChannelName, binaryMessenger: controller.binaryMessenger)
        
        eventChannel.setStreamHandler(self)
        
        methodChannel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
            case "retrieveDeviceToken":
                self?.getDeviceToken(result: result)
                break
            case "retrieveTerminatedNotification":
                self?.getTerminatedNotification(result: result)
                break
            case "loadProducts":
                if let productIds = call.arguments as? [String] {
                    self?.loadProducts(productIds, result: result)
                } else {
                    result(FlutterError(code: "LOAD_PRODUCT_ERROR", message: "Params error", details: nil))
                }
                break
            case "currentReceipt":
                self?.currentReceipt(result: result)
                break
            case "restore":
                self?.subscription.restore()
                result(nil)
                break
            case "subscribe":
                if let productId = call.arguments as? String {
                    self?.subscribe(productId, result: result)
                } else {
                    result(FlutterError(code: "PURCHASE_FAILED", message: "Params error", details: nil))
                }
                break;
            case "showAlert":
                if let args = call.arguments as? [String: Any],
                   let title = args["title"] as? String,
                   let message = args["message"] as? String,
                   let buttonTitle = args["buttonTitle"] as? String,
                   let url = args["url"] as? String {
                    // Call the showAlert function
                    self?.showAlert( title, message: message, buttonTitle: buttonTitle, url: url)
                    result(nil)  // Return nil if no result is needed
                } else {
                    result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing parameters", details: nil))
                }
                break
            case "requestNotificationPermission":
                UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
                    if granted {
                        DispatchQueue.main.async {
                            UIApplication.shared.registerForRemoteNotifications()
                        }
                    }
                }
                result(nil)
                break
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        
        if let notification = launchOptions?[.remoteNotification] as? [String: AnyObject] {
            self.notificationData = notification
        }
        
        //        DispatchQueue.main.async {
        //            UIApplication.shared.registerForRemoteNotifications()
        //        }
        
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
        }
        
        GeneratedPluginRegistrant.register(with: self)
        
        if let url = AppLinks.shared.getLink(launchOptions: launchOptions) {
            AppLinks.shared.handleLink(url: url)
            return true
        }
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    override func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        let tokenParts = deviceToken.map { String(format: "%02.2hhx", $0) }
        let token = tokenParts.joined()
        print("Device Token: \(token)")
        self.deviceToken = token
        let data: [String: Any] = [
            "event": "notification_token",
            "deviceToken": token
        ]
        
        let json = try? JSONSerialization.data(withJSONObject: data, options: [])
        
        if json != nil {
            let jsonString = String(data: json!, encoding: .utf8)!
            self.eventSink?(jsonString)
        }
    }
    
    override func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: any Error) {
        print("Failed to register for remote notifications: \(error)")
    }
    
    override func applicationDidBecomeActive(_ application: UIApplication) {
        UIApplication.shared.applicationIconBadgeNumber = 0
    }
    
    override func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // Show notification in the foreground
        let content = notification.request.content
        let userInfo = content.userInfo
        
        if let presentAlert = userInfo["presentAlert"] as? Bool, presentAlert {
            //  Send notification data to Flutter if needed
            if #available(iOS 14.0, *) {
                completionHandler([.banner, .sound, .list, .badge])
            } else {
                completionHandler([.banner, .sound, .list, .badge, .alert])
            }
            
        } else {
            let notificationData: [String: Any] = [
                "title": content.title,
                "body": content.body,
                "userInfo": userInfo
            ]
            
            let data: [String: Any] = [
                "event": "notification_data",
                "notificationData": notificationData
            ]
            
            let json = try? JSONSerialization.data(withJSONObject: data, options: [])
            
            if json != nil {
                let jsonString = String(data: json!, encoding: .utf8)!
                self.eventSink?(jsonString)
            }
            completionHandler([])
        }
        
    }
    
    override func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        let data: [String: Any] = [
            "event": "notification_click",
            "userInfo": userInfo
        ]
        // Send notification data to Flutter if needed
        let json = try? JSONSerialization.data(withJSONObject: data, options: [])
        
        if json != nil {
            let jsonString = String(data: json!, encoding: .utf8)!
            self.eventSink?(jsonString)
        }
        
        UIApplication.shared.applicationIconBadgeNumber = 0
        
        completionHandler()
    }
    
    private func getDeviceToken(result: @escaping FlutterResult) {
        if(deviceToken.isEmpty){
            result(FlutterError(code: "UNAVAILABLE", message: "Device token not available", details: nil))
        } else{
            result(deviceToken)
        }
    }
    
    private func getTerminatedNotification(result: @escaping FlutterResult) {
        if notificationData == nil {
            result(FlutterError(code: "UNAVAILABLE", message: "NotificationData available", details: nil))
        } else {
            if let jsonCompatible = self.convertToJSONCompatible(notificationData!) {
                result(jsonCompatible) // Send JSON-compatible dictionary to Flutter
            } else {
                result(FlutterError(code: "CONVERSION_ERROR", message: "Failed to convert dictionary", details: nil))
            }
        }
    }
    
    private func convertToJSONCompatible(_ dictionary: [String: AnyObject]) -> [String: Any]? {
        do {
            let data = try JSONSerialization.data(withJSONObject: dictionary, options: [])
            let json = try JSONSerialization.jsonObject(with: data, options: [])
            return json as? [String: Any]
        } catch {
            return nil
        }
    }
    
    private func loadProducts(_ listString: [String], result: @escaping FlutterResult) {
        Task {
            do {
                let productLists = try await subscription.loadProducts(listString)
                result(productLists)
            } catch {
                result(FlutterError(code: "LOAD_PRODUCT_ERROR", message: error.localizedDescription, details: nil))
            }
        }
    }
    
    private func currentReceipt(result: @escaping FlutterResult) {
        Task {
            do {
                let purchased =  await subscription.currentReceipt()
                result(purchased)
            }
        }
    }
    
    private func subscribe(_ id: String, result: @escaping FlutterResult) {
        Task {
            let (purchaseResult,receipt) = try await subscription.purchase(id)
            if purchaseResult == true {
                result(receipt)
            }
            else {
                result(FlutterError(code: "PURCHASE_FAILED", message: receipt, details: nil))
            }
        }
    }
    
    private func showAlert(_ title: String, message: String, buttonTitle: String, url: String) {
        Task {
            let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
            let action = UIAlertAction(title: buttonTitle, style: .default) { UIAlertAction in
                if let url = URL(string: url) {
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url)
                    }
                }
            }
            alert.addAction(action)
            
            let dismissAction = UIAlertAction(title: "Dismiss", style: .cancel) { _ in
                // The alert will be automatically dismissed when this action is tapped
            }
            alert.addAction(dismissAction)
            flutterViewController?.present(alert, animated: true, completion: nil)
        }
    }
}

extension AppDelegate: FlutterStreamHandler {
    func onListen(withArguments arguments: Any?, eventSink: @escaping FlutterEventSink) -> FlutterError? {
        // You can store the eventSink to send events later if needed
        self.eventSink = eventSink
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        // Handle cancellation of the stream
        self.eventSink = nil
        return nil
    }
}

