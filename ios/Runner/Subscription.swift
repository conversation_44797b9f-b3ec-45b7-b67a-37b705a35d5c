//
//  Subscription.swift
//  Saymee
//
//  Created by <PERSON><PERSON> on 18/12/24.
//

import Foundation

import StoreKit
class Subscription {
    private var productIds = ["JESTERSUB3","JESTERSUB6","JESTERSUB12"]
    private var products: [Product] = []
    
    func loadProducts(_ productId: [String]) async throws -> [String] {
        products = try await Product.products(for: productIds)
        var productsList:[String] = []
        for (index, product) in products.enumerated() {
            print("\(index): \(product.id) - \(product.displayName) - \(product.displayPrice)")
            let json = String(data: product.jsonRepresentation, encoding: .utf8)
            if json != nil {
                productsList.append(json!)
            }
        }
        return productsList;
    }
    func currentReceipt() async -> [String] {
        // Iterate through the user's purchased products.
        var receipts:[String] = []
        for await verificationResult in Transaction.currentEntitlements {
            switch verificationResult {
            case .verified(let transaction):
                // Check the type of product for the transaction
                // and provide access to the content as appropriate.
                let json = String(data: transaction.jsonRepresentation, encoding: .utf8)
                if json != nil {
                    receipts.append(json!)
                }
                break;
            case .unverified(_, _):
                // Handle unverified transactions based on your
                // business model.
                break;
            }
        }
        return receipts
    }
    func purchase(_ productId: String) async throws -> (Bool,String)
    {
        for product in products where product.id == productId {
            let result = try await product.purchase()
            switch result {
            case let .success(.verified(transaction)):
                print("Purchased \(product.id)")
                await transaction.finish()
                let receipt = String(data:transaction.jsonRepresentation,encoding: .utf8)
                print("JSON data: \(receipt ?? "")")
                return (true,receipt ?? "")
            case .success(.unverified(_, _)):
                break
            case .pending:
                break
            case .userCancelled:
                print("User cancelled")
                return (false,"User cancelled")
            @unknown default:
                break
            }
        }
        return (false,"")
    }
    func restore()
    {
        Task {
            do {
                try await AppStore.sync()
            } catch {
                print(error)
            }
        }
    }

}
