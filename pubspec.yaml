name: saymee
description: "<PERSON><PERSON> in Flutter"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.5+5

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_localizations:
    sdk: flutter
  intl: any
  device_info_plus: ^11.2.0
  package_info_plus: ^8.1.2
  shared_preferences: ^2.3.3
  flutter_modular: ^6.3.4
  dio: ^5.7.0
  flutter_dotenv: ^5.2.1
  equatable: ^2.0.7
  json_annotation: ^4.9.0
  flutter_bloc: ^8.1.6
  dartz: ^0.10.1
  flutter_svg: ^2.0.16
  collection: ^1.18.0
  preload_page_view: ^0.2.0
  # bloc: ^8.1.4
  uuid: ^4.5.1
  sign_in_with_apple: ^6.1.4
  permission_handler: ^11.3.1
  flutter_secure_storage: ^9.2.2
  crypto: ^3.0.6
  app_links: ^6.3.3
  flutter_animate: ^4.5.2
  flutter_portal: ^0.4.0
  image_picker: ^1.1.2
  gal: ^2.3.1
  fluttertoast: ^8.2.10
  share_plus: ^10.1.3
  path_provider: ^2.1.5
  modal_bottom_sheet: ^3.0.0
  flutter_local_notifications: ^18.0.1
  firebase_core: ^3.9.0
  firebase_messaging: ^15.1.6
  ironsource_mediation: ^3.0.1
  stream_transform: ^2.1.1
  markdown: ^7.2.2
  url_launcher: ^6.3.1
  # smooth_page_indicator: ^1.2.0+3
  webview_flutter: ^4.10.0
  cached_network_image: ^3.4.1
  appsflyer_sdk: ^6.15.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  json_serializable: ^6.9.0
  build_runner: ^2.4.13
  flutter_launcher_icons: ^0.14.2
  change_app_package_name: ^1.5.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - .env.development
    - .env.production
    - assets/images/
    - assets/icons/
    - assets/fonts/

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter/Inter_18pt-Black.ttf
          weight: 900
        - asset: assets/fonts/Inter/Inter_18pt-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Inter/Inter_18pt-Bold.ttf
          weight: 700
        - asset: assets/fonts/Inter/Inter_18pt-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter/Inter_18pt-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter/Inter_18pt-Regular.ttf
          weight: 400
        - asset: assets/fonts/Inter/Inter_18pt-Light.ttf
          weight: 300
        - asset: assets/fonts/Inter/Inter_18pt-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Inter/Inter_18pt-Thin.ttf
          weight: 100
    - family: BarlowCondensed
      fonts:   
        - asset: assets/fonts/Barlow_Condensed/BarlowCondensed-SemiBold.ttf
          weight: 600
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
